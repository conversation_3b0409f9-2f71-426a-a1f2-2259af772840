package cn.aguyao.module.mp.convert.user;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.aguyao.framework.common.enums.CommonStatusEnum;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.collection.CollectionUtils;
import cn.aguyao.module.mp.controller.admin.user.vo.MpUserRespVO;
import cn.aguyao.module.mp.controller.admin.user.vo.MpUserUpdateReqVO;
import cn.aguyao.module.mp.dal.dataobject.account.MpAccountDO;
import cn.aguyao.module.mp.dal.dataobject.user.MpUser2DO;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MpUserConvert {

    MpUserConvert INSTANCE = Mappers.getMapper(MpUserConvert.class);

    MpUserRespVO convert(MpUser2DO bean);

    List<MpUserRespVO> convertList(List<MpUser2DO> list);

    PageResult<MpUserRespVO> convertPage(PageResult<MpUser2DO> page);

    @Mappings(value = {
            @Mapping(source = "openId", target = "openid"),
            @Mapping(source = "unionId", target = "unionId"),
            @Mapping(source = "headImgUrl", target = "headImageUrl"),
            @Mapping(target = "subscribeTime", ignore = true), // 单独转换
    })
    MpUser2DO convert(WxMpUser wxMpUser);

    default MpUser2DO convert(MpAccountDO account, WxMpUser wxMpUser) {
        MpUser2DO user = convert(wxMpUser);
        user.setSubscribeStatus(wxMpUser.getSubscribe() ? CommonStatusEnum.ENABLE.getStatus()
                : CommonStatusEnum.DISABLE.getStatus());
        user.setSubscribeTime(LocalDateTimeUtil.of(wxMpUser.getSubscribeTime() * 1000L));
        if (account != null) {
            user.setAccountId(account.getId());
            user.setAppId(account.getAppId());
        }
        return user;
    }

    default List<MpUser2DO> convertList(MpAccountDO account, List<WxMpUser> wxUsers) {
        return CollectionUtils.convertList(wxUsers, wxUser -> convert(account, wxUser));
    }

    MpUser2DO convert(MpUserUpdateReqVO bean);

}
