### 创建微信公众号消息订阅
POST {{baseUrl}}/admin-api/mp/subscription/create
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "用户关注订阅",
  "accountId": 1,
  "subscriptionType": 1,
  "triggerCondition": "subscribe",
  "callbackUrl": "https://example.com/webhook/subscribe",
  "callbackMethod": "POST",
  "callbackHeaders": "{\"Content-Type\":\"application/json\"}",
  "messageTemplate": "用户 {nickname} 关注了公众号",
  "status": 1,
  "remark": "用于监控用户关注情况"
}

### 获取消息订阅分页
GET {{baseUrl}}/admin-api/mp/subscription/page?pageNo=1&pageSize=10
Authorization: Bearer {{token}}

### 获取消息订阅详情
GET {{baseUrl}}/admin-api/mp/subscription/get?id=1
Authorization: Bearer {{token}}

### 更新消息订阅
PUT {{baseUrl}}/admin-api/mp/subscription/update
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": 1,
  "name": "用户关注订阅（已更新）",
  "accountId": 1,
  "subscriptionType": 1,
  "triggerCondition": "subscribe",
  "callbackUrl": "https://example.com/webhook/subscribe-updated",
  "callbackMethod": "POST",
  "callbackHeaders": "{\"Content-Type\":\"application/json\"}",
  "messageTemplate": "用户 {nickname} 关注了公众号（更新版）",
  "status": 1,
  "remark": "用于监控用户关注情况（已更新）"
}

### 启用消息订阅
POST {{baseUrl}}/admin-api/mp/subscription/enable?id=1
Authorization: Bearer {{token}}

### 禁用消息订阅
POST {{baseUrl}}/admin-api/mp/subscription/disable?id=1
Authorization: Bearer {{token}}

### 测试消息订阅
POST {{baseUrl}}/admin-api/mp/subscription/test
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "subscriptionId": 1,
  "testOpenId": "o6_bmjrPTlm6_2sgVt7hMZOPfL2M",
  "testMessage": "这是一条测试消息"
}

### 获取订阅统计信息
GET {{baseUrl}}/admin-api/mp/subscription/statistics?accountId=1
Authorization: Bearer {{token}}

### 根据公众号账号获取订阅列表
GET {{baseUrl}}/admin-api/mp/subscription/list-by-account?accountId=1
Authorization: Bearer {{token}}

### 删除消息订阅
DELETE {{baseUrl}}/admin-api/mp/subscription/delete?id=1
Authorization: Bearer {{token}}

### 创建取消关注订阅
POST {{baseUrl}}/admin-api/mp/subscription/create
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "用户取消关注订阅",
  "accountId": 1,
  "subscriptionType": 2,
  "triggerCondition": "unsubscribe",
  "callbackUrl": "https://example.com/webhook/unsubscribe",
  "callbackMethod": "POST",
  "callbackHeaders": "{\"Content-Type\":\"application/json\"}",
  "messageTemplate": "用户 {nickname} 取消关注了公众号",
  "status": 1,
  "remark": "用于监控用户取消关注情况"
}

### 创建消息事件订阅
POST {{baseUrl}}/admin-api/mp/subscription/create
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "用户消息订阅",
  "accountId": 1,
  "subscriptionType": 3,
  "triggerCondition": "message",
  "callbackUrl": "https://example.com/webhook/message",
  "callbackMethod": "POST",
  "callbackHeaders": "{\"Content-Type\":\"application/json\"}",
  "messageTemplate": "用户 {nickname} 发送了消息：{content}",
  "status": 1,
  "remark": "用于监控用户消息"
}
