package cn.aguyao.module.mp.controller.admin.subscription.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 微信公众号消息订阅 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MpSubscriptionRespVO extends MpSubscriptionBaseVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "公众号 appId", example = "wx5b23ba7a5589ecbb")
    private String appId;

    @Schema(description = "公众号名称", example = "芋道源码")
    private String accountName;

    @Schema(description = "订阅类型名称", example = "关注事件")
    private String subscriptionTypeName;

    @Schema(description = "状态名称", example = "启用")
    private String statusName;

    @Schema(description = "触发次数", example = "100")
    private Integer triggerCount;

    @Schema(description = "最后触发时间")
    private LocalDateTime lastTriggerTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
