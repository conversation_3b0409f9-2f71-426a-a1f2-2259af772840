package cn.aguyao.module.mp.dal.dataobject.subscription;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 微信公众号消息订阅 DO
 *
 * <AUTHOR>
 */
@TableName("mp_subscription")
@KeySequence("mp_subscription_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MpSubscriptionDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    
    /**
     * 订阅名称
     */
    private String name;
    
    /**
     * 公众号账号编号
     */
    private Long accountId;
    
    /**
     * 公众号 appId
     */
    private String appId;
    
    /**
     * 订阅类型
     * 
     * 枚举 {@link cn.aguyao.module.mp.enums.subscription.MpSubscriptionTypeEnum}
     */
    private Integer subscriptionType;
    
    /**
     * 触发条件
     */
    private String triggerCondition;
    
    /**
     * 回调URL
     */
    private String callbackUrl;
    
    /**
     * 回调方法
     */
    private String callbackMethod;
    
    /**
     * 回调头部信息
     */
    private String callbackHeaders;
    
    /**
     * 消息模板
     */
    private String messageTemplate;
    
    /**
     * 状态
     * 
     * 枚举 {@link cn.aguyao.module.mp.enums.subscription.MpSubscriptionStatusEnum}
     */
    private Integer status;
    
    /**
     * 触发次数
     */
    private Integer triggerCount;
    
    /**
     * 最后触发时间
     */
    private LocalDateTime lastTriggerTime;
    
    /**
     * 备注
     */
    private String remark;

}
