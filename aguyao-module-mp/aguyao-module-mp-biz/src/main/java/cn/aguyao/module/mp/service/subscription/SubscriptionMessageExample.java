package cn.aguyao.module.mp.service.subscription;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 订阅消息使用示例
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class SubscriptionMessageExample {

    @Resource
    private MiniProgramSubscribeMessageService miniProgramSubscribeMessageService;

    /**
     * 充电开始时发送订阅消息
     */
    public void sendChargingStartMessage(String openId, String chargingCode, String stationName) {
        // 发送充电开始提醒
        miniProgramSubscribeMessageService.sendChargingStartNotification(
                openId,                    // 用户openId
                "35509",                   // 模板ID
                chargingCode,              // 充电桩编码：000100100011001
                stationName,               // 充电站名称：XXX充电站
                "2022-01-27 15:00",       // 开始时间
                "10.49元",                // 账户余额
                "成功/失效"                // 启动结果
        );
    }

    /**
     * 充电结束时发送订阅消息
     */
    public void sendChargingEndMessage(String openId, String chargingCode, String stationName) {
        // 发送充电结束提醒
        miniProgramSubscribeMessageService.sendChargingEndNotification(
                openId,                    // 用户openId
                "35509",                   // 模板ID
                chargingCode,              // 充电桩编码
                stationName,               // 充电站名称
                "2022-01-27 16:30",       // 结束时间
                "8.50元",                 // 消费金额
                "1小时30分钟"              // 充电时长
        );
    }

    /**
     * 充电异常时发送订阅消息
     */
    public void sendChargingErrorMessage(String openId, String chargingCode, String stationName) {
        // 发送充电异常提醒
        miniProgramSubscribeMessageService.sendChargingErrorNotification(
                openId,                    // 用户openId
                "35509",                   // 模板ID
                chargingCode,              // 充电桩编码
                stationName,               // 充电站名称
                "2022-01-27 15:30",       // 异常时间
                "设备故障，请联系客服"        // 异常原因
        );
    }

    /**
     * 快速发送充电提醒（简化版）
     */
    public void sendQuickMessage(String openId) {
        // 快速发送充电提醒
        miniProgramSubscribeMessageService.sendQuickChargingNotification(
                openId,                    // 用户openId
                "000100100011001",         // 充电桩编码
                "XXX充电站",               // 充电站名称
                "充电完成"                 // 状态
        );
    }

    /**
     * 业务场景示例：用户开始充电
     */
    public void handleUserStartCharging(String openId, String deviceCode, String stationName) {
        try {
            log.info("[handleUserStartCharging][用户开始充电] openId: {}, 设备: {}", openId, deviceCode);
            
            // 1. 处理充电业务逻辑
            // ... 业务代码 ...
            
            // 2. 发送订阅消息通知用户
            sendChargingStartMessage(openId, deviceCode, stationName);
            
            log.info("[handleUserStartCharging][充电开始处理完成] openId: {}", openId);
            
        } catch (Exception e) {
            log.error("[handleUserStartCharging][处理充电开始失败] openId: {}", openId, e);
        }
    }

    /**
     * 业务场景示例：用户充电结束
     */
    public void handleUserEndCharging(String openId, String deviceCode, String stationName) {
        try {
            log.info("[handleUserEndCharging][用户充电结束] openId: {}, 设备: {}", openId, deviceCode);
            
            // 1. 处理充电结束业务逻辑
            // ... 业务代码 ...
            
            // 2. 发送订阅消息通知用户
            sendChargingEndMessage(openId, deviceCode, stationName);
            
            log.info("[handleUserEndCharging][充电结束处理完成] openId: {}", openId);
            
        } catch (Exception e) {
            log.error("[handleUserEndCharging][处理充电结束失败] openId: {}", openId, e);
        }
    }

}
