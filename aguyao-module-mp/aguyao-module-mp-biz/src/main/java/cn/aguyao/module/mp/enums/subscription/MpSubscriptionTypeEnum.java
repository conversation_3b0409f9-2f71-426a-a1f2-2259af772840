package cn.aguyao.module.mp.enums.subscription;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微信公众号消息订阅类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum MpSubscriptionTypeEnum {

    SUBSCRIBE(1, "关注事件", "用户关注公众号时触发"),
    UNSUBSCRIBE(2, "取消关注事件", "用户取消关注公众号时触发"),
    MESSAGE(3, "消息事件", "用户发送消息时触发"),
    MENU_CLICK(4, "菜单点击事件", "用户点击自定义菜单时触发"),
    SCAN(5, "扫码事件", "用户扫描二维码时触发"),
    LOCATION(6, "位置事件", "用户上报地理位置时触发"),
    CUSTOM(99, "自定义事件", "自定义触发条件");

    /**
     * 类型
     */
    private final Integer type;
    
    /**
     * 名称
     */
    private final String name;
    
    /**
     * 描述
     */
    private final String description;

    public static MpSubscriptionTypeEnum valueOf(Integer type) {
        for (MpSubscriptionTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

}
