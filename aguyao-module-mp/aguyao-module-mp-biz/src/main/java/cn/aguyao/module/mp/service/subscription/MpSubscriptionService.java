package cn.aguyao.module.mp.service.subscription;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.mp.controller.admin.subscription.vo.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 微信公众号消息订阅 Service 接口
 *
 * <AUTHOR>
 */
public interface MpSubscriptionService {

    /**
     * 创建消息订阅
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSubscription(@Valid MpSubscriptionCreateReqVO createReqVO);

    /**
     * 更新消息订阅
     *
     * @param updateReqVO 更新信息
     */
    void updateSubscription(@Valid MpSubscriptionUpdateReqVO updateReqVO);

    /**
     * 删除消息订阅
     *
     * @param id 编号
     */
    void deleteSubscription(Long id);

    /**
     * 获得消息订阅
     *
     * @param id 编号
     * @return 消息订阅
     */
    MpSubscriptionRespVO getSubscription(Long id);

    /**
     * 获得消息订阅分页
     *
     * @param pageReqVO 分页查询
     * @return 消息订阅分页
     */
    PageResult<MpSubscriptionRespVO> getSubscriptionPage(MpSubscriptionPageReqVO pageReqVO);

    /**
     * 启用消息订阅
     *
     * @param id 编号
     */
    void enableSubscription(Long id);

    /**
     * 禁用消息订阅
     *
     * @param id 编号
     */
    void disableSubscription(Long id);

    /**
     * 测试消息订阅
     *
     * @param testReqVO 测试信息
     */
    void testSubscription(@Valid MpSubscriptionTestReqVO testReqVO);

    /**
     * 获取订阅统计信息
     *
     * @param accountId 公众号账号编号
     * @return 统计信息
     */
    MpSubscriptionStatisticsRespVO getSubscriptionStatistics(Long accountId);

    /**
     * 根据公众号账号获取订阅列表
     *
     * @param accountId 公众号账号编号
     * @return 订阅列表
     */
    List<MpSubscriptionRespVO> getSubscriptionListByAccount(Long accountId);

    /**
     * 处理微信消息订阅事件
     *
     * @param appId 公众号 appId
     * @param eventType 事件类型
     * @param openId 用户 openId
     * @param messageData 消息数据
     */
    void handleSubscriptionEvent(String appId, String eventType, String openId, Object messageData);

}
