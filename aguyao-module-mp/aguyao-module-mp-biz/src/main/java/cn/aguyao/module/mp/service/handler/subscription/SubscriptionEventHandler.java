package cn.aguyao.module.mp.service.handler.subscription;

import cn.aguyao.module.mp.framework.mp.core.context.MpContextHolder;
import cn.aguyao.module.mp.service.subscription.MpSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpMessageHandler;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 微信公众号消息订阅事件处理器
 * 
 * 用于处理各种微信事件并触发相应的订阅
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SubscriptionEventHandler implements WxMpMessageHandler {

    @Resource
    private MpSubscriptionService mpSubscriptionService;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage, Map<String, Object> context,
                                    WxMpService wxMpService, WxSessionManager sessionManager) {
        
        String appId = MpContextHolder.getAppId();
        String openId = wxMessage.getFromUser();
        
        try {
            // 根据消息类型确定事件类型
            String eventType = determineEventType(wxMessage);
            
            if (eventType != null) {
                log.info("[handle][处理订阅事件] appId: {}, openId: {}, eventType: {}", 
                        appId, openId, eventType);
                
                // 触发订阅事件处理
                mpSubscriptionService.handleSubscriptionEvent(appId, eventType, openId, wxMessage);
            }
        } catch (Exception e) {
            log.error("[handle][处理订阅事件异常] appId: {}, openId: {}", appId, openId, e);
        }
        
        // 返回 null，不影响其他处理器的执行
        return null;
    }

    /**
     * 根据微信消息确定事件类型
     */
    private String determineEventType(WxMpXmlMessage wxMessage) {
        String msgType = wxMessage.getMsgType();
        String event = wxMessage.getEvent();
        
        // 事件消息
        if ("event".equals(msgType)) {
            switch (event) {
                case "subscribe":
                    return "subscribe";
                case "unsubscribe":
                    return "unsubscribe";
                case "CLICK":
                    return "menu_click";
                case "SCAN":
                case "subscribe_scan":
                    return "scan";
                case "LOCATION":
                    return "location";
                default:
                    return "custom";
            }
        }
        
        // 普通消息
        switch (msgType) {
            case "text":
            case "image":
            case "voice":
            case "video":
            case "shortvideo":
            case "location":
            case "link":
                return "message";
            default:
                return null;
        }
    }

}
