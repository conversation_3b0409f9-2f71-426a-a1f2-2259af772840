package cn.aguyao.module.mp.dal.mapper.subscription;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.mp.controller.admin.subscription.vo.MpSubscriptionPageReqVO;
import cn.aguyao.module.mp.dal.dataobject.subscription.MpSubscriptionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 微信公众号消息订阅 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MpSubscriptionMapper extends BaseMapperX<MpSubscriptionDO> {

    default PageResult<MpSubscriptionDO> selectPage(MpSubscriptionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MpSubscriptionDO>()
                .likeIfPresent(MpSubscriptionDO::getName, reqVO.getName())
                .eqIfPresent(MpSubscriptionDO::getAccountId, reqVO.getAccountId())
                .eqIfPresent(MpSubscriptionDO::getSubscriptionType, reqVO.getSubscriptionType())
                .eqIfPresent(MpSubscriptionDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(MpSubscriptionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MpSubscriptionDO::getId));
    }

    default List<MpSubscriptionDO> selectListByAccountId(Long accountId) {
        return selectList(new LambdaQueryWrapperX<MpSubscriptionDO>()
                .eq(MpSubscriptionDO::getAccountId, accountId)
                .orderByDesc(MpSubscriptionDO::getId));
    }

    default List<MpSubscriptionDO> selectEnabledListByAppIdAndEventType(String appId, String eventType) {
        return selectList(new LambdaQueryWrapperX<MpSubscriptionDO>()
                .eq(MpSubscriptionDO::getAppId, appId)
                .eq(MpSubscriptionDO::getStatus, 1) // 启用状态
                .like(MpSubscriptionDO::getTriggerCondition, eventType)
                .orderByDesc(MpSubscriptionDO::getId));
    }

    default Integer selectCountByAccountId(Long accountId) {
        return selectCount(new LambdaQueryWrapperX<MpSubscriptionDO>()
                .eq(MpSubscriptionDO::getAccountId, accountId));
    }

    default Integer selectCountByAccountIdAndStatus(Long accountId, Integer status) {
        return selectCount(new LambdaQueryWrapperX<MpSubscriptionDO>()
                .eq(MpSubscriptionDO::getAccountId, accountId)
                .eq(MpSubscriptionDO::getStatus, status));
    }

    default Integer selectTriggerCountByAccountIdAndDateRange(Long accountId, LocalDateTime startTime, LocalDateTime endTime) {
        return selectCount(new LambdaQueryWrapperX<MpSubscriptionDO>()
                .eq(MpSubscriptionDO::getAccountId, accountId)
                .between(MpSubscriptionDO::getLastTriggerTime, startTime, endTime));
    }

    default Integer selectTotalTriggerCountByAccountId(Long accountId) {
        List<MpSubscriptionDO> list = selectList(new LambdaQueryWrapperX<MpSubscriptionDO>()
                .eq(MpSubscriptionDO::getAccountId, accountId)
                .select(MpSubscriptionDO::getTriggerCount));
        return list.stream().mapToInt(item -> item.getTriggerCount() != null ? item.getTriggerCount() : 0).sum();
    }

    @Update("UPDATE mp_subscription SET trigger_count = trigger_count + 1 WHERE id = #{id}")
    void incrementTriggerCount(@Param("id") Long id);

}
