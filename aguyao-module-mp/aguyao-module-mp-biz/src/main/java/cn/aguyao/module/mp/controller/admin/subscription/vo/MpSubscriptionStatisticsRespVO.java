package cn.aguyao.module.mp.controller.admin.subscription.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 微信公众号消息订阅统计 Response VO")
@Data
public class MpSubscriptionStatisticsRespVO {

    @Schema(description = "总订阅数", example = "10")
    private Integer totalSubscriptions;

    @Schema(description = "启用订阅数", example = "8")
    private Integer enabledSubscriptions;

    @Schema(description = "禁用订阅数", example = "2")
    private Integer disabledSubscriptions;

    @Schema(description = "今日触发次数", example = "100")
    private Integer todayTriggerCount;

    @Schema(description = "本周触发次数", example = "500")
    private Integer weekTriggerCount;

    @Schema(description = "本月触发次数", example = "2000")
    private Integer monthTriggerCount;

    @Schema(description = "总触发次数", example = "10000")
    private Integer totalTriggerCount;

}
