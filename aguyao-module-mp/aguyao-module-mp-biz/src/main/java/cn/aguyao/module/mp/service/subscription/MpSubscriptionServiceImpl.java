package cn.aguyao.module.mp.service.subscription;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.mp.controller.admin.subscription.vo.*;
import cn.aguyao.module.mp.dal.dataobject.account.MpAccountDO;
import cn.aguyao.module.mp.dal.dataobject.subscription.MpSubscriptionDO;
import cn.aguyao.module.mp.dal.mapper.subscription.MpSubscriptionMapper;
import cn.aguyao.module.mp.enums.subscription.MpSubscriptionStatusEnum;
import cn.aguyao.module.mp.enums.subscription.MpSubscriptionTypeEnum;
import cn.aguyao.module.mp.service.account.MpAccountService;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.mp.enums.ErrorCodeConstants.SUBSCRIPTION_NOT_EXISTS;

/**
 * 微信公众号消息订阅 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class MpSubscriptionServiceImpl implements MpSubscriptionService {

    @Resource
    private MpSubscriptionMapper mpSubscriptionMapper;

    @Resource
    private MpAccountService mpAccountService;

    @Resource
    private MiniProgramSubscribeMessageService miniProgramSubscribeMessageService;

    @Override
    public Long createSubscription(MpSubscriptionCreateReqVO createReqVO) {
        // 校验公众号账号存在
        MpAccountDO account = mpAccountService.getRequiredAccount(createReqVO.getAccountId());
        
        // 插入
        MpSubscriptionDO subscription = BeanUtils.toBean(createReqVO, MpSubscriptionDO.class);
        subscription.setAppId(account.getAppId());
        subscription.setTriggerCount(0);
        mpSubscriptionMapper.insert(subscription);
        
        // 返回
        return subscription.getId();
    }

    @Override
    public void updateSubscription(MpSubscriptionUpdateReqVO updateReqVO) {
        // 校验存在
        validateSubscriptionExists(updateReqVO.getId());
        
        // 更新
        MpSubscriptionDO updateObj = BeanUtils.toBean(updateReqVO, MpSubscriptionDO.class);
        mpSubscriptionMapper.updateById(updateObj);
    }

    @Override
    public void deleteSubscription(Long id) {
        // 校验存在
        validateSubscriptionExists(id);
        
        // 删除
        mpSubscriptionMapper.deleteById(id);
    }

    @Override
    public MpSubscriptionRespVO getSubscription(Long id) {
        MpSubscriptionDO subscription = mpSubscriptionMapper.selectById(id);
        if (subscription == null) {
            return null;
        }
        
        // 转换并补充信息
        MpSubscriptionRespVO respVO = BeanUtils.toBean(subscription, MpSubscriptionRespVO.class);
        enrichSubscriptionInfo(respVO, subscription);
        return respVO;
    }

    @Override
    public PageResult<MpSubscriptionRespVO> getSubscriptionPage(MpSubscriptionPageReqVO pageReqVO) {
        PageResult<MpSubscriptionDO> pageResult = mpSubscriptionMapper.selectPage(pageReqVO);
        
        // 转换并补充信息
        List<MpSubscriptionRespVO> list = BeanUtils.toBean(pageResult.getList(), MpSubscriptionRespVO.class);
        list.forEach(respVO -> {
            MpSubscriptionDO subscription = pageResult.getList().stream()
                    .filter(item -> item.getId().equals(respVO.getId()))
                    .findFirst().orElse(null);
            if (subscription != null) {
                enrichSubscriptionInfo(respVO, subscription);
            }
        });
        
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public void enableSubscription(Long id) {
        // 校验存在
        validateSubscriptionExists(id);
        
        // 更新状态
        MpSubscriptionDO updateObj = new MpSubscriptionDO();
        updateObj.setId(id);
        updateObj.setStatus(MpSubscriptionStatusEnum.ENABLED.getStatus());
        mpSubscriptionMapper.updateById(updateObj);
    }

    @Override
    public void disableSubscription(Long id) {
        // 校验存在
        validateSubscriptionExists(id);
        
        // 更新状态
        MpSubscriptionDO updateObj = new MpSubscriptionDO();
        updateObj.setId(id);
        updateObj.setStatus(MpSubscriptionStatusEnum.DISABLED.getStatus());
        mpSubscriptionMapper.updateById(updateObj);
    }

    @Override
    public void testSubscription(MpSubscriptionTestReqVO testReqVO) {
        // 校验订阅存在
        MpSubscriptionDO subscription = validateSubscriptionExists(testReqVO.getSubscriptionId());
        
        // 模拟触发订阅事件
        log.info("[testSubscription][测试订阅({}) 触发，测试用户({}) 测试消息({})]", 
                subscription.getName(), testReqVO.getTestOpenId(), testReqVO.getTestMessage());
        
        // 这里可以实际调用订阅处理逻辑
        handleSubscriptionEvent(subscription.getAppId(), "test", 
                testReqVO.getTestOpenId(), testReqVO.getTestMessage());
    }

    @Override
    public MpSubscriptionStatisticsRespVO getSubscriptionStatistics(Long accountId) {
        // 校验公众号账号存在
        mpAccountService.getRequiredAccount(accountId);
        
        // 查询统计数据
        MpSubscriptionStatisticsRespVO statistics = new MpSubscriptionStatisticsRespVO();
        statistics.setTotalSubscriptions(mpSubscriptionMapper.selectCountByAccountId(accountId));
        statistics.setEnabledSubscriptions(mpSubscriptionMapper.selectCountByAccountIdAndStatus(
                accountId, MpSubscriptionStatusEnum.ENABLED.getStatus()));
        statistics.setDisabledSubscriptions(mpSubscriptionMapper.selectCountByAccountIdAndStatus(
                accountId, MpSubscriptionStatusEnum.DISABLED.getStatus()));
        
        // 查询触发次数统计
        LocalDateTime now = LocalDateTime.now();
        statistics.setTodayTriggerCount(mpSubscriptionMapper.selectTriggerCountByAccountIdAndDateRange(
                accountId, now.toLocalDate().atStartOfDay(), now));
        statistics.setWeekTriggerCount(mpSubscriptionMapper.selectTriggerCountByAccountIdAndDateRange(
                accountId, now.minusWeeks(1), now));
        statistics.setMonthTriggerCount(mpSubscriptionMapper.selectTriggerCountByAccountIdAndDateRange(
                accountId, now.minusMonths(1), now));
        statistics.setTotalTriggerCount(mpSubscriptionMapper.selectTotalTriggerCountByAccountId(accountId));
        
        return statistics;
    }

    @Override
    public List<MpSubscriptionRespVO> getSubscriptionListByAccount(Long accountId) {
        // 校验公众号账号存在
        mpAccountService.getRequiredAccount(accountId);
        
        // 查询订阅列表
        List<MpSubscriptionDO> list = mpSubscriptionMapper.selectListByAccountId(accountId);
        
        // 转换并补充信息
        List<MpSubscriptionRespVO> respList = BeanUtils.toBean(list, MpSubscriptionRespVO.class);
        respList.forEach(respVO -> {
            MpSubscriptionDO subscription = list.stream()
                    .filter(item -> item.getId().equals(respVO.getId()))
                    .findFirst().orElse(null);
            if (subscription != null) {
                enrichSubscriptionInfo(respVO, subscription);
            }
        });
        
        return respList;
    }

    @Override
    public void handleSubscriptionEvent(String appId, String eventType, String openId, Object messageData) {
        // 查询该公众号下启用的订阅
        List<MpSubscriptionDO> subscriptions = mpSubscriptionMapper.selectEnabledListByAppIdAndEventType(appId, eventType);
        
        for (MpSubscriptionDO subscription : subscriptions) {
            try {
                // 处理订阅事件
                processSubscription(subscription, openId, messageData);
                
                // 更新触发次数和时间
                updateTriggerInfo(subscription.getId());
                
                log.info("[handleSubscriptionEvent][订阅({}) 处理成功，用户({}) 事件类型({})]", 
                        subscription.getName(), openId, eventType);
            } catch (Exception e) {
                log.error("[handleSubscriptionEvent][订阅({}) 处理失败，用户({}) 事件类型({})]", 
                        subscription.getName(), openId, eventType, e);
            }
        }
    }

    private MpSubscriptionDO validateSubscriptionExists(Long id) {
        MpSubscriptionDO subscription = mpSubscriptionMapper.selectById(id);
        if (subscription == null) {
            throw exception(SUBSCRIPTION_NOT_EXISTS);
        }
        return subscription;
    }

    private void enrichSubscriptionInfo(MpSubscriptionRespVO respVO, MpSubscriptionDO subscription) {
        // 补充公众号名称
        if (subscription.getAccountId() != null) {
            MpAccountDO account = mpAccountService.getAccount(subscription.getAccountId());
            if (account != null) {
                respVO.setAccountName(account.getName());
            }
        }
        
        // 补充订阅类型名称
        MpSubscriptionTypeEnum typeEnum = MpSubscriptionTypeEnum.valueOf(subscription.getSubscriptionType());
        if (typeEnum != null) {
            respVO.setSubscriptionTypeName(typeEnum.getName());
        }
        
        // 补充状态名称
        MpSubscriptionStatusEnum statusEnum = MpSubscriptionStatusEnum.valueOf(subscription.getStatus());
        if (statusEnum != null) {
            respVO.setStatusName(statusEnum.getName());
        }
    }

    private void processSubscription(MpSubscriptionDO subscription, String openId, Object messageData) {
        // 根据订阅配置处理事件
        try {
            // 1. 发送小程序订阅消息
            sendMiniProgramSubscribeMessage(subscription, openId, messageData);

            // 2. 发送HTTP回调（如果配置了回调URL）
            if (subscription.getCallbackUrl() != null) {
                sendHttpCallback(subscription, openId, messageData);
            }

            log.info("[processSubscription][处理订阅事件成功] 订阅名称: {}, 用户: {}",
                    subscription.getName(), openId);
        } catch (Exception e) {
            log.error("[processSubscription][处理订阅事件失败] 订阅名称: {}, 用户: {}",
                    subscription.getName(), openId, e);
        }
    }

    /**
     * 发送小程序订阅消息
     */
    private void sendMiniProgramSubscribeMessage(MpSubscriptionDO subscription, String openId, Object messageData) {
        try {
            // 根据订阅类型发送不同的消息
            switch (subscription.getSubscriptionType()) {
                case 1: // 关注事件 - 发送欢迎充电提醒
                    miniProgramSubscribeMessageService.sendQuickChargingNotification(
                            openId, "000100100011001", "XXX充电站", "欢迎使用");
                    break;

                case 2: // 取消关注事件
                    log.info("[sendMiniProgramSubscribeMessage][用户取消关注，不发送订阅消息] openId: {}", openId);
                    break;

                case 3: // 消息事件 - 发送充电状态通知
                    miniProgramSubscribeMessageService.sendChargingStartNotification(
                            openId, "35509", "000100100011001", "XXX充电站",
                            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                            "10.49元", "成功启动");
                    break;

                case 4: // 菜单点击事件
                    miniProgramSubscribeMessageService.sendQuickChargingNotification(
                            openId, "000100100011001", "XXX充电站", "菜单操作");
                    break;

                default:
                    // 自定义消息
                    Map<String, String> customData = new HashMap<>();
                    customData.put("thing1", "订阅消息提醒");
                    customData.put("time2", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                    miniProgramSubscribeMessageService.sendCustomSubscribeMessage(openId, "35509", customData);
                    break;
            }

            log.info("[sendMiniProgramSubscribeMessage][发送小程序订阅消息成功] openId: {}, 订阅类型: {}",
                    openId, subscription.getSubscriptionType());

        } catch (Exception e) {
            log.error("[sendMiniProgramSubscribeMessage][发送小程序订阅消息失败] openId: {}", openId, e);
        }
    }


    /**
     * 发送HTTP回调
     */
    private void sendHttpCallback(MpSubscriptionDO subscription, String openId, Object messageData) {
        // HTTP回调逻辑
        log.info("[sendHttpCallback][发送HTTP回调] URL: {}, openId: {}",
                subscription.getCallbackUrl(), openId);
    }

    private void updateTriggerInfo(Long subscriptionId) {
        MpSubscriptionDO updateObj = new MpSubscriptionDO();
        updateObj.setId(subscriptionId);
        updateObj.setLastTriggerTime(LocalDateTime.now());
        // 触发次数通过数据库自增
        mpSubscriptionMapper.incrementTriggerCount(subscriptionId);
        mpSubscriptionMapper.updateById(updateObj);
    }

}
