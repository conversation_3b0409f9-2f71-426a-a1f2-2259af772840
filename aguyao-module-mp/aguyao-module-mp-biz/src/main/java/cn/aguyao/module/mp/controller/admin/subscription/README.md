# 微信公众号消息订阅功能

## 功能概述

微信公众号消息订阅功能允许管理员配置各种微信事件的订阅，当用户在公众号中执行相应操作时，系统会自动触发配置的订阅处理逻辑。

## 主要特性

- **多种订阅类型支持**：关注事件、取消关注事件、消息事件、菜单点击事件、扫码事件、位置事件、自定义事件
- **灵活的回调配置**：支持 HTTP 回调、消息模板等多种处理方式
- **实时统计监控**：提供订阅触发次数、成功率等统计信息
- **测试功能**：支持订阅配置的测试验证
- **状态管理**：支持订阅的启用/禁用操作

## 订阅类型说明

| 类型 | 名称 | 描述 | 触发时机 |
|------|------|------|----------|
| 1 | 关注事件 | 用户关注公众号时触发 | 用户点击关注按钮 |
| 2 | 取消关注事件 | 用户取消关注公众号时触发 | 用户取消关注 |
| 3 | 消息事件 | 用户发送消息时触发 | 用户发送任意消息 |
| 4 | 菜单点击事件 | 用户点击自定义菜单时触发 | 用户点击菜单按钮 |
| 5 | 扫码事件 | 用户扫描二维码时触发 | 用户扫描带参数二维码 |
| 6 | 位置事件 | 用户上报地理位置时触发 | 用户发送位置信息 |
| 99 | 自定义事件 | 自定义触发条件 | 根据配置的条件触发 |

## API 接口

### 1. 创建消息订阅
```http
POST /admin-api/mp/subscription/create
```

### 2. 更新消息订阅
```http
PUT /admin-api/mp/subscription/update
```

### 3. 删除消息订阅
```http
DELETE /admin-api/mp/subscription/delete?id={id}
```

### 4. 获取消息订阅
```http
GET /admin-api/mp/subscription/get?id={id}
```

### 5. 获取消息订阅分页
```http
GET /admin-api/mp/subscription/page
```

### 6. 启用/禁用订阅
```http
POST /admin-api/mp/subscription/enable?id={id}
POST /admin-api/mp/subscription/disable?id={id}
```

### 7. 测试订阅
```http
POST /admin-api/mp/subscription/test
```

### 8. 获取统计信息
```http
GET /admin-api/mp/subscription/statistics?accountId={accountId}
```

## 使用示例

### 创建关注事件订阅

```json
{
  "name": "用户关注订阅",
  "accountId": 1,
  "subscriptionType": 1,
  "triggerCondition": "subscribe",
  "callbackUrl": "https://example.com/webhook/subscribe",
  "callbackMethod": "POST",
  "callbackHeaders": "{\"Content-Type\":\"application/json\"}",
  "messageTemplate": "用户 {nickname} 关注了公众号",
  "status": 1,
  "remark": "用于监控用户关注情况"
}
```

### 回调数据格式

当订阅事件触发时，系统会向配置的回调 URL 发送以下格式的数据：

```json
{
  "subscriptionId": 1,
  "subscriptionName": "用户关注订阅",
  "eventType": "subscribe",
  "appId": "wx1234567890",
  "openId": "o6_bmjrPTlm6_2sgVt7hMZOPfL2M",
  "triggerTime": "2024-01-01T12:00:00",
  "messageData": {
    "nickname": "张三",
    "content": "关注事件"
  }
}
```

## 权限配置

需要配置以下权限：

- `mp:subscription:query` - 查询权限
- `mp:subscription:create` - 创建权限
- `mp:subscription:update` - 更新权限
- `mp:subscription:delete` - 删除权限
- `mp:subscription:test` - 测试权限

## 数据库表结构

主要涉及以下表：

- `mp_subscription` - 消息订阅配置表
- `system_dict_type` - 字典类型表（订阅类型、状态）
- `system_dict_data` - 字典数据表
- `system_menu` - 菜单权限表

## 注意事项

1. 确保回调 URL 可以正常访问
2. 回调接口需要能够处理 POST 请求
3. 建议对回调接口进行签名验证
4. 订阅配置修改后会立即生效
5. 禁用的订阅不会触发回调

## 扩展开发

如需扩展新的订阅类型或处理逻辑，可以：

1. 在 `MpSubscriptionTypeEnum` 中添加新的订阅类型
2. 在 `SubscriptionEventHandler` 中添加新的事件处理逻辑
3. 在 `MpSubscriptionServiceImpl` 中实现具体的处理逻辑
