package cn.aguyao.module.mp.service.subscription;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 小程序订阅消息服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class MiniProgramSubscribeMessageService {

    /**
     * 发送充电开始提醒订阅消息
     * 
     * @param openId 用户openId
     * @param templateId 模板ID
     * @param chargingCode 充电桩编码
     * @param stationName 充电站名称
     * @param startTime 开始时间
     * @param accountBalance 账户余额
     * @param result 启动结果
     */
    public void sendChargingStartNotification(String openId, String templateId, 
                                            String chargingCode, String stationName, 
                                            String startTime, String accountBalance, 
                                            String result) {
        try {
            // 构建订阅消息数据
            Map<String, Object> data = new HashMap<>();
            data.put("character_string6", createDataItem(chargingCode)); // 充电桩编码
            data.put("thing5", createDataItem(stationName)); // 充电站名称  
            data.put("time2", createDataItem(startTime)); // 开始时间
            data.put("amount9", createDataItem(accountBalance)); // 账户余额
            data.put("phrase20", createDataItem(result)); // 启动结果
            
            // 构建完整的订阅消息
            Map<String, Object> subscribeMessage = new HashMap<>();
            subscribeMessage.put("touser", openId);
            subscribeMessage.put("template_id", templateId);
            subscribeMessage.put("data", data);
            subscribeMessage.put("miniprogram_state", "formal"); // 正式版
            subscribeMessage.put("lang", "zh_CN");
            
            // 发送订阅消息
            sendSubscribeMessage(subscribeMessage);
            
            log.info("[sendChargingStartNotification][发送充电开始提醒成功] openId: {}, 充电桩: {}", 
                    openId, chargingCode);
                    
        } catch (Exception e) {
            log.error("[sendChargingStartNotification][发送充电开始提醒失败] openId: {}, 充电桩: {}", 
                    openId, chargingCode, e);
        }
    }

    /**
     * 发送充电结束提醒订阅消息
     */
    public void sendChargingEndNotification(String openId, String templateId,
                                          String chargingCode, String stationName,
                                          String endTime, String totalAmount,
                                          String duration) {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("character_string6", createDataItem(chargingCode)); // 充电桩编码
            data.put("thing5", createDataItem(stationName)); // 充电站名称
            data.put("time2", createDataItem(endTime)); // 结束时间
            data.put("amount9", createDataItem(totalAmount)); // 消费金额
            data.put("phrase20", createDataItem("充电完成")); // 状态
            
            Map<String, Object> subscribeMessage = new HashMap<>();
            subscribeMessage.put("touser", openId);
            subscribeMessage.put("template_id", templateId);
            subscribeMessage.put("data", data);
            subscribeMessage.put("miniprogram_state", "formal");
            subscribeMessage.put("lang", "zh_CN");
            
            sendSubscribeMessage(subscribeMessage);
            
            log.info("[sendChargingEndNotification][发送充电结束提醒成功] openId: {}, 充电桩: {}", 
                    openId, chargingCode);
                    
        } catch (Exception e) {
            log.error("[sendChargingEndNotification][发送充电结束提醒失败] openId: {}, 充电桩: {}", 
                    openId, chargingCode, e);
        }
    }

    /**
     * 发送充电异常提醒订阅消息
     */
    public void sendChargingErrorNotification(String openId, String templateId,
                                            String chargingCode, String stationName,
                                            String errorTime, String errorReason) {
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("character_string6", createDataItem(chargingCode)); // 充电桩编码
            data.put("thing5", createDataItem(stationName)); // 充电站名称
            data.put("time2", createDataItem(errorTime)); // 异常时间
            data.put("thing1", createDataItem(errorReason)); // 异常原因
            data.put("phrase20", createDataItem("充电异常")); // 状态
            
            Map<String, Object> subscribeMessage = new HashMap<>();
            subscribeMessage.put("touser", openId);
            subscribeMessage.put("template_id", templateId);
            subscribeMessage.put("data", data);
            subscribeMessage.put("miniprogram_state", "formal");
            subscribeMessage.put("lang", "zh_CN");
            
            sendSubscribeMessage(subscribeMessage);
            
            log.info("[sendChargingErrorNotification][发送充电异常提醒成功] openId: {}, 充电桩: {}", 
                    openId, chargingCode);
                    
        } catch (Exception e) {
            log.error("[sendChargingErrorNotification][发送充电异常提醒失败] openId: {}, 充电桩: {}", 
                    openId, chargingCode, e);
        }
    }

    /**
     * 通用订阅消息发送方法
     */
    public void sendCustomSubscribeMessage(String openId, String templateId, Map<String, String> dataMap) {
        try {
            Map<String, Object> data = new HashMap<>();
            dataMap.forEach((key, value) -> data.put(key, createDataItem(value)));
            
            Map<String, Object> subscribeMessage = new HashMap<>();
            subscribeMessage.put("touser", openId);
            subscribeMessage.put("template_id", templateId);
            subscribeMessage.put("data", data);
            subscribeMessage.put("miniprogram_state", "formal");
            subscribeMessage.put("lang", "zh_CN");
            
            sendSubscribeMessage(subscribeMessage);
            
            log.info("[sendCustomSubscribeMessage][发送自定义订阅消息成功] openId: {}, 模板: {}", 
                    openId, templateId);
                    
        } catch (Exception e) {
            log.error("[sendCustomSubscribeMessage][发送自定义订阅消息失败] openId: {}, 模板: {}", 
                    openId, templateId, e);
        }
    }

    /**
     * 创建订阅消息数据项
     */
    private Map<String, String> createDataItem(String value) {
        Map<String, String> item = new HashMap<>();
        item.put("value", value);
        return item;
    }

    /**
     * 实际发送订阅消息到微信服务器
     */
    private void sendSubscribeMessage(Map<String, Object> subscribeMessage) {
        // 这里应该调用微信小程序订阅消息API
        // 示例代码：
        /*
        try {
            WxMaService wxMaService = wxMaServiceFactory.getWxMaService(appId);
            WxMaSubscribeMessage message = new WxMaSubscribeMessage();
            message.setToUser((String) subscribeMessage.get("touser"));
            message.setTemplateId((String) subscribeMessage.get("template_id"));
            message.setData((Map<String, WxMaSubscribeMessage.MsgData>) subscribeMessage.get("data"));
            message.setMiniprogramState((String) subscribeMessage.get("miniprogram_state"));
            message.setLang((String) subscribeMessage.get("lang"));
            
            wxMaService.getMsgService().sendSubscribeMsg(message);
        } catch (WxErrorException e) {
            log.error("[sendSubscribeMessage][调用微信API失败]", e);
            throw new RuntimeException("发送订阅消息失败: " + e.getMessage());
        }
        */
        
        // 目前只记录日志，实际使用时需要集成微信小程序SDK
        log.info("[sendSubscribeMessage][模拟发送订阅消息] 消息内容: {}", subscribeMessage);
    }

    /**
     * 快速发送充电提醒 - 简化版本
     */
    public void sendQuickChargingNotification(String openId, String chargingCode, String stationName, String status) {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        String templateId = "35509"; // 默认模板ID
        
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("character_string6", chargingCode); // 充电桩编码
        dataMap.put("thing5", stationName); // 充电站名称
        dataMap.put("time2", currentTime); // 时间
        dataMap.put("phrase20", status); // 状态
        
        sendCustomSubscribeMessage(openId, templateId, dataMap);
    }

}
