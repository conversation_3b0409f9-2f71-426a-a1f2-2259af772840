package cn.aguyao.module.mp.enums.subscription;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微信公众号消息订阅状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum MpSubscriptionStatusEnum {

    ENABLED(1, "启用"),
    DISABLED(0, "禁用");

    /**
     * 状态
     */
    private final Integer status;
    
    /**
     * 名称
     */
    private final String name;

    public static MpSubscriptionStatusEnum valueOf(Integer status) {
        for (MpSubscriptionStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

}
