package cn.aguyao.module.mp.dal.mysql.user;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.mp.controller.admin.user.vo.MpUserPageReqVO;
import cn.aguyao.module.mp.dal.dataobject.user.MpUser2DO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface MpUser2Mapper extends BaseMapperX<MpUser2DO> {

    default PageResult<MpUser2DO> selectPage(MpUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MpUser2DO>()
                .likeIfPresent(MpUser2DO::getOpenid, reqVO.getOpenid())
                .likeIfPresent(MpUser2DO::getNickname, reqVO.getNickname())
                .eqIfPresent(MpUser2DO::getAccountId, reqVO.getAccountId())
                .orderByDesc(MpUser2DO::getId));
    }

    default MpUser2DO selectByAppIdAndOpenid(String appId, String openid) {
        return selectOne(MpUser2DO::getAppId, appId,
                MpUser2DO::getOpenid, openid);
    }

    default List<MpUser2DO> selectListByAppIdAndOpenid(String appId, List<String> openids) {
        return selectList(new LambdaQueryWrapperX<MpUser2DO>()
                .eq(MpUser2DO::getAppId, appId)
                .in(MpUser2DO::getOpenid, openids));

    }

}
