package cn.aguyao.module.mp.controller.admin.subscription;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.mp.controller.admin.subscription.vo.*;
import cn.aguyao.module.mp.service.subscription.MpSubscriptionService;
import cn.aguyao.module.mp.service.subscription.SubscriptionMessageExample;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

/**
 * 微信公众号消息订阅 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 微信公众号消息订阅")
@RestController
@RequestMapping("/mp/subscription")
@Validated
@Slf4j
public class MpSubscriptionController {

    @Resource
    private MpSubscriptionService mpSubscriptionService;

    @Resource
    private SubscriptionMessageExample subscriptionMessageExample;

    @PostMapping("/create")
    @Operation(summary = "创建消息订阅")
    @PreAuthorize("@ss.hasPermission('mp:subscription:create')")
    public CommonResult<Long> createSubscription(@Valid @RequestBody MpSubscriptionCreateReqVO createReqVO) {
        return success(mpSubscriptionService.createSubscription(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新消息订阅")
    @PreAuthorize("@ss.hasPermission('mp:subscription:update')")
    public CommonResult<Boolean> updateSubscription(@Valid @RequestBody MpSubscriptionUpdateReqVO updateReqVO) {
        mpSubscriptionService.updateSubscription(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除消息订阅")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mp:subscription:delete')")
    public CommonResult<Boolean> deleteSubscription(@RequestParam("id") Long id) {
        mpSubscriptionService.deleteSubscription(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得消息订阅")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mp:subscription:query')")
    public CommonResult<MpSubscriptionRespVO> getSubscription(@RequestParam("id") Long id) {
        return success(mpSubscriptionService.getSubscription(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得消息订阅分页")
    @PreAuthorize("@ss.hasPermission('mp:subscription:query')")
    public CommonResult<PageResult<MpSubscriptionRespVO>> getSubscriptionPage(@Valid MpSubscriptionPageReqVO pageReqVO) {
        return success(mpSubscriptionService.getSubscriptionPage(pageReqVO));
    }

    @PostMapping("/enable")
    @Operation(summary = "启用消息订阅")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mp:subscription:update')")
    public CommonResult<Boolean> enableSubscription(@RequestParam("id") Long id) {
        mpSubscriptionService.enableSubscription(id);
        return success(true);
    }

    @PostMapping("/disable")
    @Operation(summary = "禁用消息订阅")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mp:subscription:update')")
    public CommonResult<Boolean> disableSubscription(@RequestParam("id") Long id) {
        mpSubscriptionService.disableSubscription(id);
        return success(true);
    }

    @PostMapping("/test")
    @Operation(summary = "测试消息订阅")
    @PreAuthorize("@ss.hasPermission('mp:subscription:test')")
    public CommonResult<Boolean> testSubscription(@Valid @RequestBody MpSubscriptionTestReqVO testReqVO) {
        mpSubscriptionService.testSubscription(testReqVO);
        return success(true);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取订阅统计信息")
    @Parameter(name = "accountId", description = "公众号账号编号", required = true)
    @PreAuthorize("@ss.hasPermission('mp:subscription:query')")
    public CommonResult<MpSubscriptionStatisticsRespVO> getSubscriptionStatistics(@RequestParam("accountId") Long accountId) {
        return success(mpSubscriptionService.getSubscriptionStatistics(accountId));
    }

    @GetMapping("/list-by-account")
    @Operation(summary = "根据公众号账号获取订阅列表")
    @Parameter(name = "accountId", description = "公众号账号编号", required = true)
    @PreAuthorize("@ss.hasPermission('mp:subscription:query')")
    public CommonResult<java.util.List<MpSubscriptionRespVO>> getSubscriptionListByAccount(@RequestParam("accountId") Long accountId) {
        return success(mpSubscriptionService.getSubscriptionListByAccount(accountId));
    }

    @PermitAll
    @GetMapping("/testSendMsg")
    public void testSendMsg() {
        String openId = "";
        String chargingCode = "";   // 充电桩编码
        String stationName = "";    // 充电站名称
        subscriptionMessageExample.sendChargingStartMessage(openId, chargingCode, stationName);
    }
}
