package cn.aguyao.module.mp.controller.admin.subscription.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 微信公众号消息订阅测试 Request VO")
@Data
public class MpSubscriptionTestReqVO {

    @Schema(description = "订阅编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订阅编号不能为空")
    private Long subscriptionId;

    @Schema(description = "测试用户 openId", example = "o6_bmjrPTlm6_2sgVt7hMZOPfL2M")
    private String testOpenId;

    @Schema(description = "测试消息内容", example = "这是一条测试消息")
    private String testMessage;

}
