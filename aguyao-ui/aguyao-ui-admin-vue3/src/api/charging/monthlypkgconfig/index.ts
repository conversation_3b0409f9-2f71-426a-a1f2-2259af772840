import request from '@/config/axios'

// 包月配置 VO
export interface MonthlyPkgConfigVO {
  id: number // 主键
  communityId: number // 小区id
  // oneMonthPrice: number // 一个月价格
  // threeMonthsPrice: number // 三个月价格
  // sixMonthsPrice: number // 六个月价格
  // oneYearPrice: number // 一年价格
  sort: number    // 排序
  price: number  // 价格
  title: number   // 标题
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 包月配置 API
export const MonthlyPkgConfigApi = {
  // 查询包月配置分页
  getMonthlyPkgConfigPage: async (params: any) => {
    return await request.get({ url: `/charging/monthly-pkg-config/page`, params })
  },

  // 查询包月配置详情
  getMonthlyPkgConfig: async (id: number) => {
    return await request.get({ url: `/charging/monthly-pkg-config/get?id=` + id })
  },

  // 新增包月配置
  createMonthlyPkgConfig: async (data: MonthlyPkgConfigVO) => {
    return await request.post({ url: `/charging/monthly-pkg-config/create`, data })
  },

  // 修改包月配置
  updateMonthlyPkgConfig: async (data: MonthlyPkgConfigVO) => {
    return await request.put({ url: `/charging/monthly-pkg-config/update`, data })
  },

  // 删除包月配置
  deleteMonthlyPkgConfig: async (id: number) => {
    return await request.delete({ url: `/charging/monthly-pkg-config/delete?id=` + id })
  },

  // 导出包月配置 Excel
  exportMonthlyPkgConfig: async (params) => {
    return await request.download({ url: `/charging/monthly-pkg-config/export-excel`, params })
  },
}
