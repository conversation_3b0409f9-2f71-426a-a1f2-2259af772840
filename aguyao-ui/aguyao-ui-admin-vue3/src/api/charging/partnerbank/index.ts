import request from '@/config/axios'

// 伙伴银行 VO
export interface PartnerBankVO {
  id: number // 主键
  partnerId: number // 合作伙伴id
  cardNo: string // 银行卡号
  bankDeposit: string // 开户行
  bankName: string // 银行名称
}

// 伙伴银行 API
export const PartnerBankApi = {
  // 查询伙伴银行分页
  getPartnerBankPage: async (params: any) => {
    return await request.get({ url: `/charging/partner-bank/page`, params })
  },

  // 查询伙伴银行详情
  getPartnerBank: async (id: number) => {
    return await request.get({ url: `/charging/partner-bank/get?id=` + id })
  },

  // 新增伙伴银行
  createPartnerBank: async (data: PartnerBankVO) => {
    return await request.post({ url: `/charging/partner-bank/create`, data })
  },

  // 修改伙伴银行
  updatePartnerBank: async (data: PartnerBankVO) => {
    return await request.put({ url: `/charging/partner-bank/update`, data })
  },

  // 删除伙伴银行
  deletePartnerBank: async (id: number) => {
    return await request.delete({ url: `/charging/partner-bank/delete?id=` + id })
  },

  // 导出伙伴银行 Excel
  exportPartnerBank: async (params) => {
    return await request.download({ url: `/charging/partner-bank/export-excel`, params })
  },
}
