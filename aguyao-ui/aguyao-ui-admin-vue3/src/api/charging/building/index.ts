import request from '@/config/axios'

// 楼栋 VO
export interface BuildingVO {
  id: number // 主键
  code: string // 编号
  name: string // 楼栋名称
  deviceNum: number // 设备数
  chargingDeviceNum: number // 充电中的设备数
  standbyDeviceNum: number // 待机设备数
  networkDisconnDeviceNum: number // 断网设备数
  rate: number // 费率
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 楼栋 API
export const BuildingApi = {
  // 查询楼栋分页
  getBuildingPage: async (params: any) => {
    return await request.get({ url: `/charging/building/page`, params })
  },

  // 查询楼栋详情
  getBuilding: async (id: number) => {
    return await request.get({ url: `/charging/building/get?id=` + id })
  },

  // 新增楼栋
  createBuilding: async (data: BuildingVO) => {
    return await request.post({ url: `/charging/building/create`, data })
  },

  // 修改楼栋
  updateBuilding: async (data: BuildingVO) => {
    return await request.put({ url: `/charging/building/update`, data })
  },

  // 删除楼栋
  deleteBuilding: async (id: number) => {
    return await request.delete({ url: `/charging/building/delete?id=` + id })
  },

  // 导出楼栋 Excel
  exportBuilding: async (params) => {
    return await request.download({ url: `/charging/building/export-excel`, params })
  },

  // 参数：小区id
  getBuildList: async (communityId: number) => {
    return await request.get({ url: `/charging/building/list?communityId=` + communityId })
  }
}
