import request from '@/config/axios'

// 退款记录 VO
export interface RefundRecordVO {
  id: number // 主键
  code: string // 编号
  mbeCode: string // 用户编号
  mobile: string // 用户手机号
  applyAmount: number // 申请金额
  refundSuccessAmount: number // 退款成功金额
  applyStatus: number // 申请状态
  approvalStatus: number // 审批状态
  remark: string // 备注
}

// 退款记录 API
export const RefundRecordApi = {
  // 查询退款记录分页
  getRefundRecordPage: async (params: any) => {
    return await request.get({ url: `/charging/refund-record/page`, params })
  },

  // 查询退款记录详情
  getRefundRecord: async (id: number) => {
    return await request.get({ url: `/charging/refund-record/get?id=` + id })
  },

  // 新增退款记录
  createRefundRecord: async (data: RefundRecordVO) => {
    return await request.post({ url: `/charging/refund-record/create`, data })
  },

  // 修改退款记录
  updateRefundRecord: async (data: RefundRecordVO) => {
    return await request.put({ url: `/charging/refund-record/update`, data })
  },

  // 删除退款记录
  deleteRefundRecord: async (id: number) => {
    return await request.delete({ url: `/charging/refund-record/delete?id=` + id })
  },

  // 导出退款记录 Excel
  exportRefundRecord: async (params) => {
    return await request.download({ url: `/charging/refund-record/export-excel`, params })
  },

   // 发起退款
   handleRefund: async (id: number) => {
    return await request.put({ url: `/charging/refund-record/refund?id=` + id })
  },
}
