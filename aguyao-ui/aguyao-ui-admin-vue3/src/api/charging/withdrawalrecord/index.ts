import request from '@/config/axios'

// 提现记录 VO
export interface WithdrawalRecordVO {
  id: number // 主键
  code: string // 编号
  partnerCode: string // 主管编号
  partnerName: string // 主管名称
  partnerMobile: string // 主管手机
  applyTime: Date // 申请时间
  applyAmount: number // 申请金额
  bankName: string // 提现银行名称
  bankCardNum: string // 银行卡号
  remark: string // 备注
  status: number // 申请状态
}

// 提现记录 API
export const WithdrawalRecordApi = {
  // 查询提现记录分页
  getWithdrawalRecordPage: async (params: any) => {
    return await request.get({ url: `/charging/withdrawal-record/page`, params })
  },

  // 查询提现记录详情
  getWithdrawalRecord: async (id: number) => {
    return await request.get({ url: `/charging/withdrawal-record/get?id=` + id })
  },

  // 新增提现记录
  createWithdrawalRecord: async (data: WithdrawalRecordVO) => {
    return await request.post({ url: `/charging/withdrawal-record/create`, data })
  },

  // 修改提现记录
  updateWithdrawalRecord: async (data: WithdrawalRecordVO) => {
    return await request.put({ url: `/charging/withdrawal-record/update`, data })
  },

  // 删除提现记录
  deleteWithdrawalRecord: async (id: number) => {
    return await request.delete({ url: `/charging/withdrawal-record/delete?id=` + id })
  },

  // 导出提现记录 Excel
  exportWithdrawalRecord: async (params) => {
    return await request.download({ url: `/charging/withdrawal-record/export-excel`, params })
  },
}
