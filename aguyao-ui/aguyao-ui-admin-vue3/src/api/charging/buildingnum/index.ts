import request from '@/config/axios'

// 楼号 VO
export interface BuildingNumVO {
  id: number // 主键
  buildingId: number // 楼栋id
  name: string // 楼号名称，如：7号
  deviceNum: number // 设备数
  portNum: number // 端口数
  rateInfo: string // 费率信息
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 楼号 API
export const BuildingNumApi = {
  // 查询楼号分页
  getBuildingNumPage: async (params: any) => {
    return await request.get({ url: `/charging/building-num/page`, params })
  },

  // 查询楼号详情
  getBuildingNum: async (id: number) => {
    return await request.get({ url: `/charging/building-num/get?id=` + id })
  },

  // 新增楼号
  createBuildingNum: async (data: BuildingNumVO) => {
    return await request.post({ url: `/charging/building-num/create`, data })
  },

  // 修改楼号
  updateBuildingNum: async (data: BuildingNumVO) => {
    return await request.put({ url: `/charging/building-num/update`, data })
  },

  // 删除楼号
  deleteBuildingNum: async (id: number) => {
    return await request.delete({ url: `/charging/building-num/delete?id=` + id })
  },

  // 导出楼号 Excel
  exportBuildingNum: async (params) => {
    return await request.download({ url: `/charging/building-num/export-excel`, params })
  },
}
