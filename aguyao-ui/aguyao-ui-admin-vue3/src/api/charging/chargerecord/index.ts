import request from '@/config/axios'

// 充电记录，消费记录 VO
export interface ChargeRecordVO {
  id: number // 主键
  code: string // 编号
  mbeCode: string // 用户编号
  mobile: string // 用户手机号
  chargeTime: Date // 充电时间
  chargeCommunityId: number // 充电小区id
  buildingsId: number // 楼栋id
  deviceId: number // 设备id
  amount: number // 消费金额
  paySource: number // 支付来源，消费来源
  proceedStatus: number // 进行状态
  wattage: number // 瓦数，消耗的瓦数
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 充电记录，消费记录 API
export const ChargeRecordApi = {
  // 查询充电记录，消费记录分页
  getChargeRecordPage: async (params: any) => {
    return await request.get({ url: `/charging/charge-record/page`, params })
  },

  // 查询充电记录，消费记录详情
  getChargeRecord: async (id: number) => {
    return await request.get({ url: `/charging/charge-record/get?id=` + id })
  },

  // 新增充电记录，消费记录
  createChargeRecord: async (data: ChargeRecordVO) => {
    return await request.post({ url: `/charging/charge-record/create`, data })
  },

  // 修改充电记录，消费记录
  updateChargeRecord: async (data: ChargeRecordVO) => {
    return await request.put({ url: `/charging/charge-record/update`, data })
  },

  // 删除充电记录，消费记录
  deleteChargeRecord: async (id: number) => {
    return await request.delete({ url: `/charging/charge-record/delete?id=` + id })
  },

  // 导出充电记录，消费记录 Excel
  exportChargeRecord: async (params) => {
    return await request.download({ url: `/charging/charge-record/export-excel`, params })
  },
}
