import request from '@/config/axios'

// 设备 VO
export interface DeviceVO {
  id: number // 主键
  code: string // 编号
  networkStatus: number // 联网状态
  runningStatus: number // 运行状态
  remark: string // 备注
}

// 设备 API
export const DeviceApi = {
  // 查询设备分页
  getDevicePage: async (params: any) => {
    return await request.get({ url: `/charging/device/page`, params })
  },

  // 查询设备详情
  getDevice: async (id: number) => {
    return await request.get({ url: `/charging/device/get?id=` + id })
  },

  // 新增设备
  createDevice: async (data: DeviceVO) => {
    return await request.post({ url: `/charging/device/create`, data })
  },

  // 修改设备
  updateDevice: async (data: DeviceVO) => {
    return await request.put({ url: `/charging/device/update`, data })
  },

  // 删除设备
  deleteDevice: async (id: number) => {
    return await request.delete({ url: `/charging/device/delete?id=` + id })
  },

  // 导出设备 Excel
  exportDevice: async (params) => {
    return await request.download({ url: `/charging/device/export-excel`, params })
  },
}
