import request from '@/config/axios'

// 收费方案 VO
export interface SchemeVO {
  id: number // 主键
  type: number // 方案类型， 1：按时间收费； 2：按电量收费
  name: string // 方案名称
  desc: string // 方案描述
  remark: string // 备注
}

// 收费方案 API
export const SchemeApi = {
  // 查询收费方案分页
  getSchemePage: async (params: any) => {
    return await request.get({ url: `/charging/scheme/page`, params })
  },

  // 查询收费方案详情
  getScheme: async (id: number) => {
    return await request.get({ url: `/charging/scheme/get?id=` + id })
  },

  // 新增收费方案
  createScheme: async (data: SchemeVO) => {
    return await request.post({ url: `/charging/scheme/create`, data })
  },

  // 修改收费方案
  updateScheme: async (data: SchemeVO) => {
    return await request.put({ url: `/charging/scheme/update`, data })
  },

  // 删除收费方案
  deleteScheme: async (id: number) => {
    return await request.delete({ url: `/charging/scheme/delete?id=` + id })
  },

  // 导出收费方案 Excel
  exportScheme: async (params) => {
    return await request.download({ url: `/charging/scheme/export-excel`, params })
  },
}
