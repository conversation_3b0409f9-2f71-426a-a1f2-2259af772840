import request from '@/config/axios'

// 投保配置 VO
export interface InsureConfigVO {
  id: number // 主键
  code: string // 优惠编号
  title: string // 投保标题
  content: string // 投保内容
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 投保配置 API
export const InsureConfigApi = {
  // 查询投保配置分页
  getInsureConfigPage: async (params: any) => {
    return await request.get({ url: `/charging/insure-config/page`, params })
  },

  // 查询投保配置详情
  getInsureConfig: async (id: number) => {
    return await request.get({ url: `/charging/insure-config/get?id=` + id })
  },

  // 新增投保配置
  createInsureConfig: async (data: InsureConfigVO) => {
    return await request.post({ url: `/charging/insure-config/create`, data })
  },

  // 修改投保配置
  updateInsureConfig: async (data: InsureConfigVO) => {
    return await request.put({ url: `/charging/insure-config/update`, data })
  },

  // 删除投保配置
  deleteInsureConfig: async (id: number) => {
    return await request.delete({ url: `/charging/insure-config/delete?id=` + id })
  },

  // 导出投保配置 Excel
  exportInsureConfig: async (params) => {
    return await request.download({ url: `/charging/insure-config/export-excel`, params })
  },
}
