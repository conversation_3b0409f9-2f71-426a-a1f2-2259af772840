import request from '@/config/axios'

// 充值记录 VO
export interface RechargeRecordVO {
  id: number // 主键
  code: string // 编号
  mbeCode: string // 用户编号
  mobile: string // 用户手机号
  amount: number // 消费金额
  rechargeTime: Date // 充值时间
  paySource: number // 支付来源，消费来源
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 充值记录 API
export const RechargeRecordApi = {
  // 查询充值记录分页
  getRechargeRecordPage: async (params: any) => {
    return await request.get({ url: `/charging/recharge-record/page`, params })
  },

  // 查询充值记录详情
  getRechargeRecord: async (id: number) => {
    return await request.get({ url: `/charging/recharge-record/get?id=` + id })
  },

  // 新增充值记录
  createRechargeRecord: async (data: RechargeRecordVO) => {
    return await request.post({ url: `/charging/recharge-record/create`, data })
  },

  // 修改充值记录
  updateRechargeRecord: async (data: RechargeRecordVO) => {
    return await request.put({ url: `/charging/recharge-record/update`, data })
  },

  // 删除充值记录
  deleteRechargeRecord: async (id: number) => {
    return await request.delete({ url: `/charging/recharge-record/delete?id=` + id })
  },

  // 导出充值记录 Excel
  exportRechargeRecord: async (params) => {
    return await request.download({ url: `/charging/recharge-record/export-excel`, params })
  },
}
