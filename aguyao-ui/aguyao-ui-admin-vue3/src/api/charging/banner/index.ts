import request from '@/config/axios'

// 移动端banner VO
export interface BannerVO {
  id: number // 主键
  tag: string // 标签
  jumpLink: string // 跳转链接
  imageUrl: string // 图片链接
  sort: number // 排序
}

// 移动端banner API
export const BannerApi = {
  // 查询移动端banner分页
  getBannerPage: async (params: any) => {
    return await request.get({ url: `/charging/banner/page`, params })
  },

  // 查询移动端banner详情
  getBanner: async (id: number) => {
    return await request.get({ url: `/charging/banner/get?id=` + id })
  },

  // 新增移动端banner
  createBanner: async (data: BannerVO) => {
    return await request.post({ url: `/charging/banner/create`, data })
  },

  // 修改移动端banner
  updateBanner: async (data: BannerVO) => {
    return await request.put({ url: `/charging/banner/update`, data })
  },

  // 删除移动端banner
  deleteBanner: async (id: number) => {
    return await request.delete({ url: `/charging/banner/delete?id=` + id })
  },

  // 导出移动端banner Excel
  exportBanner: async (params) => {
    return await request.download({ url: `/charging/banner/export-excel`, params })
  },
}
