import request from '@/config/axios'

// 会员，即充电的用户 VO
export interface MemberVO {
  id: number // 主键
  code: string // 编号
  mobile: string // 手机号
  belongCommunityId: number // 所属小区id
  rechargeBalance: number // 充值余额
  giftBalance: number // 赠送余额
  monthlyPassCommunityId: number // 包月卡小区id
  monthlyPassDuration: Date // 包月卡有效期
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 会员，即充电的用户 API
export const MemberApi = {
  // 查询会员，即充电的用户分页
  getMemberPage: async (params: any) => {    
    return await request.get({ url: `/charging/member/page`, params })
  },

  // 查询会员，即充电的用户详情
  getMember: async (id: number) => {
    return await request.get({ url: `/charging/member/get?id=` + id })
  },

  // 新增会员，即充电的用户
  createMember: async (data: MemberVO) => {
    return await request.post({ url: `/charging/member/create`, data })
  },

  // 修改会员，即充电的用户
  updateMember: async (data: MemberVO) => {
    return await request.put({ url: `/charging/member/update`, data })
  },

  // 删除会员，即充电的用户
  deleteMember: async (id: number) => {
    return await request.delete({ url: `/charging/member/delete?id=` + id })
  },

  // 导出会员，即充电的用户 Excel
  exportMember: async (params) => {
    return await request.download({ url: `/charging/member/export-excel`, params })
  },
}
