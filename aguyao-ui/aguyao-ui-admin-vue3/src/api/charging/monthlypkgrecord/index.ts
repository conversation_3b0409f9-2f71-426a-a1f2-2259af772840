import request from '@/config/axios'

// 包月记录 VO
export interface MonthlyPkgRecordVO {
  id: number // 主键
  code: string // 编号
  mbeCode: string // 用户编号
  mobile: string // 用户手机号
  amount: number // 消费金额
  purchaseTime: Date // 购买时间
  belongCommunityId: number // 包月小区id
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 包月记录 API
export const MonthlyPkgRecordApi = {
  // 查询包月记录分页
  getMonthlyPkgRecordPage: async (params: any) => {
    return await request.get({ url: `/charging/monthly-pkg-record/page`, params })
  },

  // 查询包月记录详情
  getMonthlyPkgRecord: async (id: number) => {
    return await request.get({ url: `/charging/monthly-pkg-record/get?id=` + id })
  },

  // 新增包月记录
  createMonthlyPkgRecord: async (data: MonthlyPkgRecordVO) => {
    return await request.post({ url: `/charging/monthly-pkg-record/create`, data })
  },

  // 修改包月记录
  updateMonthlyPkgRecord: async (data: MonthlyPkgRecordVO) => {
    return await request.put({ url: `/charging/monthly-pkg-record/update`, data })
  },

  // 删除包月记录
  deleteMonthlyPkgRecord: async (id: number) => {
    return await request.delete({ url: `/charging/monthly-pkg-record/delete?id=` + id })
  },

  // 导出包月记录 Excel
  exportMonthlyPkgRecord: async (params) => {
    return await request.download({ url: `/charging/monthly-pkg-record/export-excel`, params })
  },
}
