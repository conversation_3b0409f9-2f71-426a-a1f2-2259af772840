import request from '@/config/axios'

// 合作伙伴——小区管理人员（物业） VO
export interface PartnerVO {
  id: number // 主键
  code: string // 编号
  mobile: string // 手机号
  name: string // 名称
  profitSharingPoints: number // 分润点数
  historicalProfitSharing: number // 历史分润
  cashoutProfitSharing: number // 已提现分润
  communityNum: number // 管理的小区数
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 合作伙伴——小区管理人员（物业） API
export const PartnerApi = {
  // 查询合作伙伴——小区管理人员（物业）分页
  getPartnerPage: async (params: any) => {
    return await request.get({ url: `/charging/partner/page`, params })
  },

  // 查询合作伙伴——小区管理人员（物业）详情
  getPartner: async (id: number) => {
    return await request.get({ url: `/charging/partner/get?id=` + id })
  },

  // 新增合作伙伴——小区管理人员（物业）
  createPartner: async (data: PartnerVO) => {
    return await request.post({ url: `/charging/partner/create`, data })
  },

  // 修改合作伙伴——小区管理人员（物业）
  updatePartner: async (data: PartnerVO) => {
    return await request.put({ url: `/charging/partner/update`, data })
  },

  // 删除合作伙伴——小区管理人员（物业）
  deletePartner: async (id: number) => {
    return await request.delete({ url: `/charging/partner/delete?id=` + id })
  },

  // 导出合作伙伴——小区管理人员（物业） Excel
  exportPartner: async (params) => {
    return await request.download({ url: `/charging/partner/export-excel`, params })
  },
}
