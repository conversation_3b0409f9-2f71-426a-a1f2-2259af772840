import request from '@/config/axios'

// 分润记录 VO
export interface ProfitSharingRecordVO {
  id: number // 主键
  code: string // 编号
  partnerCode: string // 主管编号
  partnerName: string // 主管名称
  partnerMobile: string // 主管手机
  rate: number // 费率
  profitSharing: number // 分润
  belongCommunityId: number // 所属小区id
  mbeMobile: string // 用户手机
  consumeTime: Date // 消费时间
  consumeAmount: number // 消费金额
  consumeType: number // 消费类型
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 分润记录 API
export const ProfitSharingRecordApi = {
  // 查询分润记录分页
  getProfitSharingRecordPage: async (params: any) => {
    return await request.get({ url: `/charging/profit-sharing-record/page`, params })
  },

  // 查询分润记录详情
  getProfitSharingRecord: async (id: number) => {
    return await request.get({ url: `/charging/profit-sharing-record/get?id=` + id })
  },

  // 新增分润记录
  createProfitSharingRecord: async (data: ProfitSharingRecordVO) => {
    return await request.post({ url: `/charging/profit-sharing-record/create`, data })
  },

  // 修改分润记录
  updateProfitSharingRecord: async (data: ProfitSharingRecordVO) => {
    return await request.put({ url: `/charging/profit-sharing-record/update`, data })
  },

  // 删除分润记录
  deleteProfitSharingRecord: async (id: number) => {
    return await request.delete({ url: `/charging/profit-sharing-record/delete?id=` + id })
  },

  // 导出分润记录 Excel
  exportProfitSharingRecord: async (params) => {
    return await request.download({ url: `/charging/profit-sharing-record/export-excel`, params })
  },
}
