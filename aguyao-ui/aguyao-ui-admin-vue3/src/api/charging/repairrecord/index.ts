import request from '@/config/axios'

// 报修记录 VO
export interface RepairRecordVO {
  id: number // 主键
  code: string // 保留记录编号
  mobile: string // 用户手机号
  communityId: number // 报修小区id
  buildingId: number // 楼栋id
  deviceCode: string // 设备编号
  repairStatus: number // 维修状态
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 报修记录 API
export const RepairRecordApi = {
  // 查询报修记录分页
  getRepairRecordPage: async (params: any) => {
    return await request.get({ url: `/charging/repair-record/page`, params })
  },

  // 查询报修记录详情
  getRepairRecord: async (id: number) => {
    return await request.get({ url: `/charging/repair-record/get?id=` + id })
  },

  // 新增报修记录
  createRepairRecord: async (data: RepairRecordVO) => {
    return await request.post({ url: `/charging/repair-record/create`, data })
  },

  // 修改报修记录
  updateRepairRecord: async (data: RepairRecordVO) => {
    return await request.put({ url: `/charging/repair-record/update`, data })
  },

  // 删除报修记录
  deleteRepairRecord: async (id: number) => {
    return await request.delete({ url: `/charging/repair-record/delete?id=` + id })
  },

  // 导出报修记录 Excel
  exportRepairRecord: async (params) => {
    return await request.download({ url: `/charging/repair-record/export-excel`, params })
  },
}
