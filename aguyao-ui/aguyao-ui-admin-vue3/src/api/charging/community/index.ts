import request from '@/config/axios'

// 小区 VO
export interface CommunityVO {
  id: number // 主键
  code: string // 编号
  name: string // 小区名称
  buildingsNum: number // 楼栋数
  deviceNum: number // 设备数
  mbeNum: number // 用户数，会员数
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 小区 API
export const CommunityApi = {
  // 查询小区分页
  getCommunityPage: async (params: any) => {
    return await request.get({ url: `/charging/community/page`, params })
  },

  // 查询小区详情
  getCommunity: async (id: number) => {
    return await request.get({ url: `/charging/community/get?id=` + id })
  },

  // 新增小区
  createCommunity: async (data: CommunityVO) => {
    return await request.post({ url: `/charging/community/create`, data })
  },

  // 修改小区
  updateCommunity: async (data: CommunityVO) => {
    return await request.put({ url: `/charging/community/update`, data })
  },

  // 删除小区
  deleteCommunity: async (id: number) => {
    return await request.delete({ url: `/charging/community/delete?id=` + id })
  },

  // 导出小区 Excel
  exportCommunity: async (params) => {
    return await request.download({ url: `/charging/community/export-excel`, params })
  },

  // 获取小区列表
  getCommunityList: async (params) => {
    return await request.get({ url: `/charging/community/list`, params })
  },
}
