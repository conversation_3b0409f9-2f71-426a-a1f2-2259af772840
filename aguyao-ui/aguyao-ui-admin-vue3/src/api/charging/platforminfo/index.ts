import request from '@/config/axios'

// 平台信息 VO
export interface PlatformInfoVO {
  id: number // 主键
  name: string // 公司名称
  customerServicePhone: string // 客服热线
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 平台信息 API
export const PlatformInfoApi = {
  // 查询平台信息分页
  getPlatformInfoPage: async (params: any) => {
    return await request.get({ url: `/charging/platform-info/page`, params })
  },

  // 查询平台信息详情
  getPlatformInfo: async (id: number) => {
    return await request.get({ url: `/charging/platform-info/get?id=` + id })
  },

  // 新增平台信息
  createPlatformInfo: async (data: PlatformInfoVO) => {
    return await request.post({ url: `/charging/platform-info/create`, data })
  },

  // 修改平台信息
  updatePlatformInfo: async (data: PlatformInfoVO) => {
    return await request.put({ url: `/charging/platform-info/update`, data })
  },

  // 删除平台信息
  deletePlatformInfo: async (id: number) => {
    return await request.delete({ url: `/charging/platform-info/delete?id=` + id })
  },

  // 导出平台信息 Excel
  exportPlatformInfo: async (params) => {
    return await request.download({ url: `/charging/platform-info/export-excel`, params })
  },
}
