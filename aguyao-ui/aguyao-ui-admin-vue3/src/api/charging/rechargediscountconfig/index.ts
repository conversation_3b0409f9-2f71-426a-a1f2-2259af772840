import request from '@/config/axios'

// 充值优惠配置 VO
export interface RechargeDiscountConfigVO {
  id: number // 主键
  code: string // 优惠编号
  amount: number // 金额
  giftAmount: number // 赠送金额
  remark: string // 备注
  status: number // 帐号状态（0正常 1停用）
}

// 充值优惠配置 API
export const RechargeDiscountConfigApi = {
  // 查询充值优惠配置分页
  getRechargeDiscountConfigPage: async (params: any) => {
    return await request.get({ url: `/charging/recharge-discount-config/page`, params })
  },

  // 查询充值优惠配置详情
  getRechargeDiscountConfig: async (id: number) => {
    return await request.get({ url: `/charging/recharge-discount-config/get?id=` + id })
  },

  // 新增充值优惠配置
  createRechargeDiscountConfig: async (data: RechargeDiscountConfigVO) => {
    return await request.post({ url: `/charging/recharge-discount-config/create`, data })
  },

  // 修改充值优惠配置
  updateRechargeDiscountConfig: async (data: RechargeDiscountConfigVO) => {
    return await request.put({ url: `/charging/recharge-discount-config/update`, data })
  },

  // 删除充值优惠配置
  deleteRechargeDiscountConfig: async (id: number) => {
    return await request.delete({ url: `/charging/recharge-discount-config/delete?id=` + id })
  },

  // 导出充值优惠配置 Excel
  exportRechargeDiscountConfig: async (params) => {
    return await request.download({ url: `/charging/recharge-discount-config/export-excel`, params })
  },
}
