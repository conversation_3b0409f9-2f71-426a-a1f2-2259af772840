import request from '@/config/axios'

// 使用手册 VO
export interface ServiceManualVO {
  id: number // 主键
  title: string // 标题
  content: string // 内容，明细
  remark: string // 备注
}

// 使用手册 API
export const ServiceManualApi = {
  // 查询使用手册分页
  getServiceManualPage: async (params: any) => {
    return await request.get({ url: `/charging/service-manual/page`, params })
  },

  // 查询使用手册详情
  getServiceManual: async (id: number) => {
    return await request.get({ url: `/charging/service-manual/get?id=` + id })
  },

  // 新增使用手册
  createServiceManual: async (data: ServiceManualVO) => {
    return await request.post({ url: `/charging/service-manual/create`, data })
  },

  // 修改使用手册
  updateServiceManual: async (data: ServiceManualVO) => {
    return await request.put({ url: `/charging/service-manual/update`, data })
  },

  // 删除使用手册
  deleteServiceManual: async (id: number) => {
    return await request.delete({ url: `/charging/service-manual/delete?id=` + id })
  },

  // 导出使用手册 Excel
  exportServiceManual: async (params) => {
    return await request.download({ url: `/charging/service-manual/export-excel`, params })
  },
}
