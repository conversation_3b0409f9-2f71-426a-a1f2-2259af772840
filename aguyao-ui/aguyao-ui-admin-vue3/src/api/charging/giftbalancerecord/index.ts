import request from '@/config/axios'

// 赠送金额记录 VO
export interface GiftBalanceRecordVO {
  id: number // 主键
  mbeId: number // 会员id
  mpId: number // 微信id
  type: number // 操作类型
  amt: number // 金额
}

// 赠送金额记录 API
export const GiftBalanceRecordApi = {
  // 操作赠送金额
  giftbalance: async (data: GiftBalanceRecordVO) => {
    return await request.post({ url: `/charging/gift-balance/create`, data })
  },

}
