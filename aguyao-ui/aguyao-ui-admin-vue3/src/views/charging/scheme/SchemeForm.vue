<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-card class="box-card" shadow="hover">
        <!-- vue3 插槽 写法 -->
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <!-- <el-button class="button" type="text">操作按钮</el-button> -->
          </div>
        </template>
        <el-form-item label="方案名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入方案名称" />
        </el-form-item>
        <el-form-item label="方案描述" prop="description">
          <el-input
            type="textarea"
            rows="2"
            v-model="formData.description"
            placeholder="请输入方案描述"
          />
        </el-form-item>
        <el-form-item label="计费方式" prop="type">
          <el-radio-group v-model="formData.type" @change="changeMode">
            <el-radio-button :label="1">按时间计费</el-radio-button>
            <el-radio-button :label="2">按电量计费</el-radio-button>
            <el-radio-button :label="3">按电量计费2.0</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-card>

      <!-- 2、其他信息-->
      <el-tabs v-model="activeName">
        <!-- 2.1、按时间计费 -->
        <el-tab-pane label="按时间计费" name="sj" v-if="showMode === 0" :style="{ width: '99.5%' }">
          <!-- 2.1.1、 功率档位 -->
          <el-card class="box-card" shadow="hover">
            <!-- vue3 插槽 写法 -->
            <template #header>
              <div class="card-header">
                <span>功率档位</span>
                <el-button class="button" type="text" @click="addItem('power')"
                  >添加功率档位</el-button
                >
              </div>
            </template>

            <!-- <div class="flex gap-4 mb-4 items-center">
              <el-input
                v-model="formData.intpu1"
                style="width: 240px"
                placeholder="Please Input"
                :suffix-icon="Search"
              />
              <el-input
                v-model="formData.input2"
                style="width: 240px"
                placeholder="Please Input"
              >
                <template #suffix>
                  <el-icon @click="handleIconClick"><RemoveFilled /></el-icon>
                </template>
              </el-input>
            </div> -->
            <div
              v-for="(item, index) in powerItems"
              :key="index"
              class="flex gap-4 mb-4 items-center"
              :data-id="item.sort"
            >
              <el-input-number
                v-model="item.startPower"
                style="width: 120px"
                placeholder="低档(瓦)"
                :controls="false"
              >
                <template #append>瓦</template>
              </el-input-number>
              ~
              <el-input-number
                v-model="item.endPower"
                style="width: 120px"
                placeholder="高档(瓦)"
                :controls="false"
              >
                <template #append>瓦</template>
              </el-input-number>
              <el-input v-model="item.amount" style="width: 240px" placeholder="单价">
                <!-- <template #suffix>
                  <el-icon @click="handleIconClick(item.id, 'power')"><RemoveFilled /></el-icon>
                </template> -->
                <template #append>元/小时</template>
              </el-input>
              <div style="color: #5a9cf8">
                <el-icon @click="handleIconClick(item.sort, 'power')"><RemoveFilled /></el-icon>
              </div>
            </div>
          </el-card>

          <!-- 2.1.2、 收费套餐 -->
          <el-card class="box-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>收费套餐</span>
                <el-button class="button" type="text" @click="addItem('setmeal1')"
                  >添加套餐</el-button
                >
              </div>
            </template>
            <div
              v-for="(item, index) in setmealItems1"
              :key="index"
              class="flex gap-4 mb-4 items-center"
              :data-id="item.sort"
            >
              <el-input-number
                v-model="item.amount"
                style="width: 240px"
                placeholder="单价"
                :controls="false"
              >
                <template #append>单价</template>
              </el-input-number>
              <el-input v-model="item.description" style="width: 240px" placeholder="充电说明" />
              <div style="color: #5a9cf8">
                <el-icon @click="handleIconClick(item.sort, 'setmeal1')"><RemoveFilled /></el-icon>
              </div>
            </div>
          </el-card>
        </el-tab-pane>

        <!-- ========================== -->
        <!-- 2.2、按电量计费 -->
        <el-tab-pane label="按电量计费" name="dl" v-if="showMode === 1" :style="{ width: '99.5%' }">
          <!-- 2.2.1、 时段电费 -->
          <el-card class="box-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>
                  时段电费
                  <Tooltip
                    message="时间格式说明，1)时间范围00:00~23:59； 2)不能含中文冒号；3)不能有空格"
                    title="时间格式"
                  />
                </span>
                <el-button class="button" type="text" @click="addItem('time')">添加时段</el-button>
              </div>
            </template>
            <div
              v-for="(item, index) in timeItems"
              :key="index"
              class="flex gap-4 mb-4 items-center"
              :data-id="item.sort"
            >
              <el-input v-model="item.startTime" style="width: 120px" placeholder="开始" />
              ~
              <el-input
                v-model="item.endTime"
                style="width: 120px"
                placeholder="结束"
                :controls="false"
              />

              <el-input v-model="item.eprice" style="width: 240px" placeholder="电费">
                <!-- <template #suffix>
                  <el-icon @click="handleIconClick(item.id, 'time')"><RemoveFilled /></el-icon>
                </template> -->
                <template #append><div style="font-size: 11px">元/度</div></template>
              </el-input>

              <el-input v-model="item.sprice" style="width: 240px" placeholder="服务费">
                <!-- <template #suffix>
                  <el-icon @click="handleIconClick(item.id, 'time')"><RemoveFilled /></el-icon>
                </template> -->
                <template #append><div style="font-size: 11px">元/度</div></template>
              </el-input>
              <div style="color: #5a9cf8">
                <el-icon @click="handleIconClick(item.sort, 'time')"><RemoveFilled /></el-icon>
              </div>
            </div>
          </el-card>

          <!-- 2.2.2、 收费套餐 -->
          <el-card class="box-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>收费套餐</span>
                <el-button class="button" type="text" @click="addItem('setmeal2')"
                  >添加套餐</el-button
                >
              </div>
            </template>
            <div
              v-for="(item, index) in setmealItems2"
              :key="index"
              class="flex gap-4 mb-4 items-center"
              :data-id="item.sort"
            >
              <el-input-number
                v-model="item.amount"
                style="width: 240px"
                placeholder="单价"
                :controls="false"
              >
                <template #append>单价</template>
              </el-input-number>
              <el-input v-model="item.description" style="width: 240px" placeholder="充电说明" />
              <div style="color: #5a9cf8">
                <el-icon @click="handleIconClick(item.sort, 'setmeal2')"><RemoveFilled /></el-icon>
              </div>
            </div>
          </el-card>
        </el-tab-pane>

        <!-- ========================== -->
        <!-- 2.3、按电量计费2.0 -->
        <el-tab-pane
          label="按电量计费"
          name="dl2"
          v-if="showMode === 2"
          :style="{ width: '99.5%' }"
        >
          <!-- 2.3.1、 时段电费 -->
          <el-card class="box-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>
                  时段电费
                  <Tooltip
                    message="时间格式说明，1)时间范围00:00~23:59； 2)不能含中文冒号；3)不能有空格"
                    title="时间格式"
                  />
                </span>
                <el-button class="button" type="text" @click="addItem('time3')">添加时段</el-button>
              </div>
            </template>
            <div
              v-for="(item, index) in timeItems3"
              :key="index"
              class="flex gap-4 mb-4 items-center"
              :data-id="item.sort"
            >
              <el-input v-model="item.startTime" style="width: 120px" placeholder="开始" />
              ~
              <el-input
                v-model="item.endTime"
                style="width: 120px"
                placeholder="结束"
                :controls="false"
              />
              <el-input v-model="item.eprice" style="width: 240px" placeholder="电费">
                <template #append><div style="font-size: 11px">元/度</div></template>
              </el-input>
              <div style="color: #5a9cf8">
                <el-icon @click="handleIconClick(item.sort, 'time3')"><RemoveFilled /></el-icon>
              </div>
            </div>
          </el-card>

          <!-- 2.3.2、 功率服务费 -->
          <el-card class="box-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>
                  功率服务费
                  <!-- <Tooltip
                    message="时间格式说明，1)时间范围00:00~23:59； 2)不能含中文冒号；3)不能有空格"
                    title="时间格式"
                  /> -->
                </span>
                <el-button class="button" type="text" @click="addItem('power3')"
                  >添加功率</el-button
                >
              </div>
            </template>
            <div
              v-for="(item, index) in powerItems3"
              :key="index"
              class="flex gap-4 mb-4 items-center"
              :data-id="item.sort"
            >
              <el-input-number
                v-model="item.startPower"
                style="width: 120px"
                placeholder="低档(瓦)"
                :controls="false"
              >
                <template #append>瓦</template>
              </el-input-number>
              ~
              <el-input-number
                v-model="item.endPower"
                style="width: 120px"
                placeholder="高档(瓦)"
                :controls="false"
              >
                <template #append>瓦</template>
              </el-input-number>
              <el-input v-model="item.amount" style="width: 240px" placeholder="服务费">
                <template #append>元/小时</template>
              </el-input>
              <div style="color: #5a9cf8">
                <el-icon @click="handleIconClick(item.sort, 'power3')"><RemoveFilled /></el-icon>
              </div>
            </div>
          </el-card>

          <!-- 2.3.3、 收费套餐 -->
          <el-card class="box-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>收费套餐</span>
                <el-button class="button" type="text" @click="addItem('setmeal3')"
                  >添加套餐</el-button
                >
              </div>
            </template>
            <div
              v-for="(item, index) in setmealItems3"
              :key="index"
              class="flex gap-4 mb-4 items-center"
              :data-id="item.sort"
            >
              <el-input-number
                v-model="item.amount"
                style="width: 240px"
                placeholder="单价"
                :controls="false"
              >
                <template #append>单价</template>
              </el-input-number>
              <el-input v-model="item.description" style="width: 240px" placeholder="充电说明" />
              <div style="color: #5a9cf8">
                <el-icon @click="handleIconClick(item.sort, 'setmeal3')"><RemoveFilled /></el-icon>
              </div>
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { SchemeApi, SchemeVO } from '@/api/charging/scheme'
import { RemoveFilled } from '@element-plus/icons-vue'
// import { ElMessage } from 'element-plus'

/** 收费方案 表单 */
defineOptions({ name: 'SchemeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  type: undefined,
  name: undefined,
  description: undefined,
  remark: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择计费方式', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const showMode = ref(0) // 计费模式
const activeName = ref('sj') // 当前激活的标签页

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 初始化items数据
  initItems()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SchemeApi.getScheme(id)

      powerItems.value = formData.value.powerItems
      setmealItems1.value = formData.value.setmealItems1

      timeItems.value = formData.value.timeItems
      setmealItems2.value = formData.value.setmealItems2

      timeItems3.value = formData.value.timeItems3
      powerItems3.value = formData.value.powerItems3
      setmealItems3.value = formData.value.setmealItems3

      setData()
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 设置activeName
const setData = () => {
  // 默认值，或者是按时间计费的
  if (!!!formData.value.type || formData.value.type === 1) {
    formData.value.type = 1
    activeName.value = 'sj'
    changeMode(1)
  } else if (formData.value.type === 2) {
    formData.value.type = 2
    activeName.value = 'dl'
    changeMode(2)
  } else if (formData.value.type === 3) {
    formData.value.type = 3
    activeName.value = 'dl2'
    changeMode(3)
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    // 数据校验之时间格式
    const tmpTimeItems = timeItems.value
    if (formData.value.type == 2 && !!tmpTimeItems) {
      for (var i = 0; i < tmpTimeItems.length; i++) {
        // 包含中文冒号
        // if () {

        // }
        // 去掉多余空格
        // 时间范围00:00~23:59
        const stResult = validateTime(tmpTimeItems[i].startTime)
        const etResult = validateTime(tmpTimeItems[i].endTime)
        if (!stResult || !etResult) {
          message.error('时间格式不正确，1)时间范围00:00~23:59； 2)不能含中文冒号；3)不能有空格')
          return
        }
      }
    }

    // 组装数据
    formData.value.powerItems = powerItems.value
    formData.value.setmealItems1 = setmealItems1.value

    formData.value.timeItems = timeItems.value
    formData.value.setmealItems2 = setmealItems2.value

    formData.value.timeItems3 = timeItems3.value
    formData.value.powerItems3 = powerItems3.value
    formData.value.setmealItems3 = setmealItems3.value

    const data = formData.value as unknown as SchemeVO

    if (formType.value === 'create') {
      await SchemeApi.createScheme(data)
      message.success(t('common.createSuccess'))
    } else {
      await SchemeApi.updateScheme(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const validateTime = (timeStr) => {
  // 去除多余空格
  timeStr = timeStr.trim()

  // 检查是否包含中文冒号
  if (timeStr.includes('：')) {
    return false
  }

  // 检查时间格式是否为 HH:mm
  const timeRegex = /^(0?[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(timeStr)
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    type: undefined,
    name: undefined,
    description: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}

/** 点击切页事件 */
const handleClick = (tab, event) => {
  debugger
  if ('sj' === tab.paneName) {
    // 预估 取项目表单即可
    // alert('sj')
  } else if ('dl' === tab.paneName) {
    // 合同列表
    // this.getContractList(pid);
    // alert('dl')
  }
}
// 功率档位
const powerItems = ref([{ sort: 1, startPower: undefined, endPower: undefined, amount: undefined }])
// 套餐1
const setmealItems1 = ref([{ sort: 1, amount: undefined, description: '' }])

// 时段
const timeItems = ref([
  { sort: 1, startTime: undefined, endTime: undefined, eprice: undefined, sprice: undefined }
])
// 套餐2
const setmealItems2 = ref([{ sort: 1, amount: undefined, description: '' }])

// 时段
const timeItems3 = ref([{ sort: 1, startTime: undefined, endTime: undefined, eprice: undefined }])
// 功率
const powerItems3 = ref([
  { sort: 1, startPower: undefined, endPower: undefined, amount: undefined }
])
// 套餐2
const setmealItems3 = ref([{ sort: 1, amount: undefined, description: '' }])

const initItems = () => {
  powerItems.value = [{ sort: 1, startPower: undefined, endPower: undefined, amount: undefined }]
  setmealItems1.value = [{ sort: 1, amount: undefined, description: '' }]

  timeItems.value = [
    { sort: 1, startTime: undefined, endTime: undefined, eprice: undefined, sprice: undefined }
  ]
  setmealItems2.value = [{ sort: 1, amount: undefined, description: '' }]

  timeItems3.value = [{ sort: 1, startTime: undefined, endTime: undefined, eprice: undefined }]
  powerItems3.value = [{ sort: 1, startPower: undefined, endPower: undefined, amount: undefined }]
  setmealItems3.value = [{ sort: 1, amount: undefined, description: '' }]
}

let powerNextId = 2 // 用于生成唯一ID
let setmealNextId1 = 2 // 用于生成唯一ID

let timeNextId = 2 // 用于生成唯一ID
let setmealNextId2 = 2 // 用于生成唯一ID

let timeNextId3 = 2 // 用于生成唯一ID
let powerNextId3 = 2 // 用于生成唯一ID
let setmealNextId3 = 2 // 用于生成唯一ID

// 添加一行
const addItem = (type) => {
  if ('power' === type) {
    powerItems.value.push({
      sort: powerNextId++,
      startPower: undefined,
      endPower: undefined,
      amount: undefined
    })
    return
  } else if ('setmeal1' === type) {
    setmealItems1.value.push({ sort: setmealNextId1++, amount: undefined, description: '' })
    return
  } else if ('time' === type) {
    timeItems.value.push({
      sort: timeNextId++,
      startTime: undefined,
      endTime: undefined,
      eprice: undefined,
      sprice: undefined
    })
    return
  } else if ('setmeal2' === type) {
    setmealItems2.value.push({ sort: setmealNextId2++, amount: undefined, description: '' })
  } else if ('time3' === type) {
    timeItems3.value.push({
      sort: timeNextId3++,
      startTime: undefined,
      endTime: undefined,
      eprice: undefined
    })
    return
  } else if ('power3' === type) {
    powerItems3.value.push({
      sort: powerNextId3++,
      startPower: undefined,
      endPower: undefined,
      amount: undefined
    })
    return
  } else if ('setmeal3' === type) {
    setmealItems3.value.push({ sort: setmealNextId3++, amount: undefined, description: '' })
  }
}

const handleIconClick = (sort, type) => {
  if ('power' === type) {
    if (powerItems.value.length <= 1) {
      message.error('至少保留一行')
      return
    }
    powerItems.value = powerItems.value.filter((item) => item.sort !== sort)
  } else if ('setmeal1' === type) {
    if (setmealItems1.value.length <= 1) {
      message.error('至少保留一行')
      return
    }
    setmealItems1.value = setmealItems1.value.filter((item) => item.sort !== sort)
  } else if ('time' === type) {
    if (timeItems.value.length <= 1) {
      message.error('至少保留一行')
      return
    }
    timeItems.value = timeItems.value.filter((item) => item.sort !== sort)
  } else if ('setmeal2' === type) {
    if (setmealItems2.value.length <= 1) {
      message.error('至少保留一行')
      return
    }
    setmealItems2.value = setmealItems2.value.filter((item) => item.sort !== sort)
  } else if ('time3' === type) {
    if (timeItems3.value.length <= 1) {
      message.error('至少保留一行')
      return
    }
    timeItems3.value = timeItems3.value.filter((item) => item.sort !== sort)
  } else if ('power3' === type) {
    if (powerItems3.value.length <= 1) {
      message.error('至少保留一行')
      return
    }
    powerItems3.value = powerItems3.value.filter((item) => item.sort !== sort)
  } else if ('setmeal3' === type) {
    if (setmealItems3.value.length <= 1) {
      message.error('至少保留一行')
      return
    }
    setmealItems3.value = setmealItems3.value.filter((item) => item.sort !== sort)
  }
}

// 切换计费方式
const changeMode = (cmode) => {
  if (cmode === 1) {
    // 按时间计费
    showMode.value = 0
    activeName.value = 'sj'
  } else if (cmode === 2) {
    // 按电量计费
    showMode.value = 1
    activeName.value = 'dl'
  } else {
    // 按电量计费2.0
    showMode.value = 2
    activeName.value = 'dl2'
  }
}
</script>

<style>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.box-card {
  width: 100%;
  margin-bottom: 20px;
}
</style>
