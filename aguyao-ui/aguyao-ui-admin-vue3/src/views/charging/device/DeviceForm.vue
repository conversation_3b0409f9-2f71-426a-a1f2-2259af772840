<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入CP开头的编码" />
      </el-form-item>
      <el-form-item label="设备号" prop="device">
        <el-input v-model="formData.device" placeholder="请输入86开头的设备号" />
      </el-form-item>
      <el-form-item label="小区" prop="communityId">
        <el-input v-model="formData.communityName" readonly>
          <template #append>
            <el-button @click="openCommunityList"> <Icon icon="ep:search" /> 选择 </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="楼栋" prop="buildingId">
        <el-select
          v-model="formData.buildingId"
          placeholder="请选择分类状态"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="build in buildList"
            :key="build.id"
            :label="build.name"
            :value="build.id"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="联网状态" prop="networkStatus"> -->
      <!-- <el-radio-group v-model="formData.networkStatus">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group> -->
      <!-- <el-select v-model="formData.networkStatus" placeholder="请选择">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CHARGING_NETWORK_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->

      <!-- <el-form-item label="联网状态" prop="networkStatus">
        <span>{{ formData.runningStatus === 1 ? '已联网' : '未联网' }}</span>
      </el-form-item>
      
      <el-form-item label="运行状态" prop="runningStatus">
        <el-select v-model="formData.runningStatus" placeholder="请选择">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CHARGING_RUNNING_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="目标地址" prop="targetAddr">
        <el-input
          v-model="formData.targetAddr"
          placeholder="目标地址"
          style="width: 80%"
          :disabled="modified"
        />
        <el-button @click="handleTargetAddr" style="width: 20%">{{ modifyTip }}</el-button>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <CommunityList ref="communityListRef" @success="handleCommunityChange" />
</template>
<script setup lang="ts">
import { DeviceApi, DeviceVO } from '@/api/charging/device'
import { BuildingApi, BuildingVO } from '@/api/charging/building'
import { CommunityVO } from '@/api/charging/community'
import CommunityList from '@/views/charging/community/components/CommunityList.vue'

/** 设备 表单 */
defineOptions({ name: 'DeviceForm' })

const buildList = ref<BuildingVO[]>([]) // 楼栋列表

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const modified = ref(true) // 修改时，目标地址是否可编辑
const modifyTip = ref('修改地址') // 修改提示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  networkStatus: undefined,
  runningStatus: undefined,
  remark: undefined,
  communityId: undefined,
  communityName: undefined,
  buildingId: undefined
})
const formRules = reactive({
  buildingId: [{ required: true, message: '楼栋必须选择', trigger: 'blur' }],
  communityId: [{ required: true, message: '小区必须选择', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DeviceApi.getDevice(id)
    } finally {
      formLoading.value = false
    }
  }

  // 获取楼栋列表
  getBuildList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DeviceVO
    if (formType.value === 'create') {
      await DeviceApi.createDevice(data)
      message.success(t('common.createSuccess'))
    } else {
      await DeviceApi.updateDevice(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    networkStatus: undefined,
    runningStatus: undefined,
    remark: undefined,
    communityId: undefined,
    communityName: undefined,
    buildingId: undefined
  }
  formRef.value?.resetFields()
}

// ################## start ##########
const communityListRef = ref() // 可入库的订单列表 Ref
const openCommunityList = (type: string) => {
  // selectCommunityType.value = type
  if (communityListRef.value) {
    communityListRef.value.open()
  }
}

const handleCommunityChange = (vo: CommunityVO) => {
  if (vo == null) {
    return
  }

  // 所属小区
  formData.value.communityId = vo[0].id
  formData.value.communityName = vo[0].name

  // 获取楼栋列表
  getBuildList()
}

const handleTargetAddr = () => {
  debugger
  modified.value = !modified.value
  if (!modified.value) {
    modifyTip.value = '取消修改'
  } else {
    modifyTip.value = '修改地址'
  }
}
// ################## end ##########

const getBuildList = async () => {
  if (!formData.value.communityId) {
    return
  }
  buildList.value = await BuildingApi.getBuildList(formData.value.communityId)
}
</script>
