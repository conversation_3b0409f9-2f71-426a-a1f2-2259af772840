<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="系统自动生成" disabled />
      </el-form-item>
      <el-form-item label="小区" prop="communityId">
        <!-- <el-input v-model="formData.communityId" placeholder="请选择小区" /> -->
        <el-input v-model="formData.communityName" readonly>
          <template #append>
            <el-button @click="openCommunityList('1')"> <Icon icon="ep:search" /> 选择 </el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" placeholder="请输入序号" style="width: 100%" />
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input-number v-model="formData.price" placeholder="请输入价格" style="width: 100%" />
      </el-form-item>
      <el-form-item label="实际月数" prop="monthNum">
        <el-input-number
          v-model="formData.monthNum"
          placeholder="请输入实际月数"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="提示信息" prop="title">
        <el-input v-model="formData.title" placeholder="请输入提示信息" />
      </el-form-item>
      <!-- <el-form-item label="一个月(¥)" prop="oneMonthPrice">
        <el-input v-model="formData.oneMonthPrice" placeholder="请输入一个月价格" />
      </el-form-item>
      <el-form-item label="三个月(¥)" prop="threeMonthsPrice">
        <el-input v-model="formData.threeMonthsPrice" placeholder="请输入三个月价格" />
      </el-form-item>
      <el-form-item label="六个月(¥)" prop="sixMonthsPrice">
        <el-input v-model="formData.sixMonthsPrice" placeholder="请输入六个月价格" />
      </el-form-item>
      <el-form-item label="一年(¥)" prop="oneYearPrice">
        <el-input v-model="formData.oneYearPrice" placeholder="请输入一年价格" />
      </el-form-item> -->
      <el-form-item label="备注信息" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <!-- <el-form-item label="帐号状态（0正常 1停用）" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <CommunityList ref="communityListRef" @success="handleCommunityChange" />
</template>
<script setup lang="ts">
import { MonthlyPkgConfigApi, MonthlyPkgConfigVO } from '@/api/charging/monthlypkgconfig'
import { CommunityVO } from '@/api/charging/community'
import CommunityList from '@/views/charging/community/components/CommunityList.vue'

/** 包月配置 表单 */
defineOptions({ name: 'MonthlyPkgConfigForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  communityId: undefined,
  communityName: undefined,
  oneMonthPrice: undefined,
  threeMonthsPrice: undefined,
  sixMonthsPrice: undefined,
  oneYearPrice: undefined,
  remark: undefined,
  status: undefined,
  monthNum: undefined
})
const formRules = reactive({
  sort: [{ required: true, message: '序号不能为空', trigger: 'blur' }],
  price: [{ required: true, message: '价格不能为空', trigger: 'blur' }],
  monthNum: [{ required: true, message: '实际月数不能为空', trigger: 'blur' }],
  title: [{ required: true, message: '提示信息不能为空', trigger: 'blur' }],
  communityId: [{ required: true, message: '小区不能为空', trigger: ['blur', 'change'] }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await MonthlyPkgConfigApi.getMonthlyPkgConfig(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as MonthlyPkgConfigVO
    if (formType.value === 'create') {
      await MonthlyPkgConfigApi.createMonthlyPkgConfig(data)
      message.success(t('common.createSuccess'))
    } else {
      await MonthlyPkgConfigApi.updateMonthlyPkgConfig(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    communityId: undefined,
    communityName: undefined,
    oneMonthPrice: undefined,
    threeMonthsPrice: undefined,
    sixMonthsPrice: undefined,
    oneYearPrice: undefined,
    remark: undefined,
    status: undefined,
    monthNum: undefined
  }
  formRef.value?.resetFields()
}

/** 打开【小区列表】弹窗 */
const communityListRef = ref() // 可入库的订单列表 Ref
const openCommunityList = () => {
  communityListRef.value.open()
}

const handleCommunityChange = (vo: CommunityVO) => {
  if (vo == null) {
    return
  }

  formData.value.communityId = vo[0].id
  formData.value.communityName = vo[0].name
}
</script>
