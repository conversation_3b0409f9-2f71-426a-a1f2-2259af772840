<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="所属小区" prop="belongCommunityName">
        <!-- <el-input
          v-model="queryParams.belongCommunityName"
          placeholder="请输入所属小区"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        /> -->
        <el-input v-model="queryParams.belongCommunityName" readonly>
          <template #append>
            <el-button @click="openCommunityList"> <Icon icon="ep:search" /> 选择 </el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="月卡有效期" prop="monthlyPassDuration" label-width="108">
        <el-date-picker
          v-model="queryParams.monthlyPassDuration"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="xxxx" prop="monthlyPassDuration">
                <el-date-picker
          v-model="queryParams.monthlyPassDuration"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetime"
          placeholder="月卡有效期"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['charging:member:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['charging:member:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="编号" align="center" prop="code" width="150" />
      <el-table-column label="手机号" align="center" prop="mobile" width="120" />
      <el-table-column label="所属小区" align="center" prop="belongCommunityName" width="120" />
      <el-table-column label="充值余额" align="center" prop="rechargeBalance" width="100" />
      <el-table-column label="赠送余额" align="center" prop="giftBalance" width="100" />
      <el-table-column
        label="包月卡小区"
        align="center"
        prop="monthlyPassCommunityName"
        width="120"
      />
      <el-table-column
        label="包月卡有效期"
        align="center"
        prop="monthlyPassDuration"
        :formatter="dateFormatter"
        width="180"
      />

      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        width="150"
        show-overflow-tooltip
      />
      <!-- <el-table-column label="帐号状态" align="center" prop="status" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      /> -->
      <el-table-column label="操作" align="center" fixed="right" width="120">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['charging:member:update']"
          >
            编辑
          </el-button>
          <!-- <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['charging:member:delete']"
          >
            删除
          </el-button> -->
          <el-button
            link
            type="danger"
            @click="handleGiftBalance(scope.row.id)"
            v-hasPermi="['charging:member:giftBalance']"
          >
            余额
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <MemberForm ref="formRef" @success="getList" />
  <CommunityList ref="communityListRef" @success="handleCommunityChange" />
  <MemberGiftBalanceForm ref="memberGiftBalanceFormRef" :mbeId="mbeId" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { MemberApi, MemberVO } from '@/api/charging/member'
import MemberForm from './MemberForm.vue'
import CommunityList from '@/views/charging/community/components/CommunityList.vue'
import { CommunityVO } from '@/api/charging/community'
import MemberGiftBalanceForm from './MemberGiftBalanceForm.vue'

/** 会员，即充电的用户 列表 */
defineOptions({ name: 'Member' })

/** 打开【小区列表】弹窗 */
const communityListRef = ref() // 可入库的订单列表 Ref
const openCommunityList = () => {
  communityListRef.value.open()
}

const handleCommunityChange = (vo: CommunityVO) => {
  if (vo == null) {
    return
  }
  queryParams.belongCommunityId = vo[0].id
  queryParams.belongCommunityName = vo[0].name
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<MemberVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
// 响应式对象
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: undefined,
  mobile: undefined,
  belongCommunityId: undefined,
  belongCommunityName: undefined,
  rechargeBalance: undefined,
  giftBalance: undefined,
  monthlyPassCommunityId: undefined,
  monthlyPassDuration: undefined,
  remark: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MemberApi.getMemberPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MemberApi.deleteMember(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await MemberApi.exportMember(queryParams)
    download.excel(data, '会员，即充电的用户.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const mbeId = ref()
/** 添加/修改操作 */
const memberGiftBalanceFormRef = ref()
const handleGiftBalance = (id?: number) => {
  console.log(id)
  mbeId.value = id
  memberGiftBalanceFormRef.value.open(id)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
