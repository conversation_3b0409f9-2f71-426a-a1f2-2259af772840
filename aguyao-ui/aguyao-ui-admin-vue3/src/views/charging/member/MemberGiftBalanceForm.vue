<template>
  <Dialog v-model="dialogVisible" title="赠送余额">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      v-loading="formLoading"
    >
      <el-form-item label="操作类型" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio :label="1">赠送</el-radio>
          <el-radio :label="2">扣除</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="赠送余额" prop="amt">
        <el-input-number v-model="formData.amt" style="width: 100%" :min="0" />
      </el-form-item>
      <el-form-item label="操作说明">
        <p
          style="font-family: '新宋体', SimSun; font-size: 14px; font-style: italic; color: #ff007f"
        >
          选择【赠送】，则会增加用户赠送的余额；选择【扣除】，则会减少用户赠送的余额。请谨慎操作！</p
        >
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { GiftBalanceRecordApi } from '@/api/charging/giftbalancerecord'
import { number } from 'vue-types'

defineOptions({ name: 'MemberGiftBalanceForm' })

// 定义 props, 用户传递参数，或者在open方法中直接传递也可以
const props = defineProps({
  mbeId: {
    type: number,
    required: true
  }
})

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  type: undefined,
  amt: undefined
})

const formRules = reactive({
  type: [{ required: true, message: '操作类型不能为空', trigger: 'blur' }],
  amt: [{ required: true, message: '金额不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const mId = ref() // 会员ID

/** 打开弹窗 */
const open = async (mbeId) => {
  mId.value = mbeId
  dialogVisible.value = true
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    // console.log(props.mbeId);

    // return ;
    await GiftBalanceRecordApi.giftbalance({
      type: formData.value.type,
      amt: formData.value.amt,
      mbeId: mId.value
    })
    message.success('操作成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success', true)
  } finally {
    formLoading.value = false
  }
}

const resetForm = () => {
  formData.value = {
    type: undefined,
    amt: undefined
  }
}
</script>
