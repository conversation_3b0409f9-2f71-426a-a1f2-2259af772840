<template>
  <Dialog
    title="选择会员"
    v-model="dialogVisible"
    :appendToBody="true"
    :scroll="true"
    width="60%"
    min-height="80%"
  >
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="会员编码" prop="code">
          <el-input
            v-model="queryParams.code"
            placeholder="请输入会员编码"
            clearable
            @keyup.enter="handleQuery"
            class="!w-160px"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input
            v-model="queryParams.mobile"
            placeholder="请输入会员手机号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-160px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 查询</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <ContentWrap>
      <el-table
        v-loading="loading"
        :data="list"
        :show-overflow-tooltip="true"
        :stripe="true"
        @selection-change="handleSelectionChange"
      >
        <!-- 如果没有这行 上面的@selection-change 是没有效果的 -->
        <el-table-column width="30" label="选择" type="selection" />
        <!-- 采用最小宽度， 最大宽度方式 -->
        <el-table-column prop="code" label="编码" min-width="35%" width="150" />
        <!-- 使用 CSS 样式 -->
        <el-table-column align="center" prop="mobile" label="手机号" class="custom-width" />
        <!-- 采用内联方式 -->
        <el-table-column prop="remark" label="备注信息" :style="{ width: '30%' }" />
      </el-table>
      <!-- 分页 -->
      <Pagination
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNo"
        :total="total"
        @pagination="getList"
      />
    </ContentWrap>
    <template #footer>
      <el-button :disabled="!selectionList.length" type="primary" @click="submitForm">
        确 定
      </el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ElTable } from 'element-plus'
import { MemberApi, MemberVO } from '@/api/charging/member'

// export const CommunityList = defineComponent({
//   name: 'CommunityList'
// })

defineOptions({
  name: 'MemberList'
})

const list = ref<MemberVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const loading = ref(false) // 列表的加载中
const dialogVisible = ref(false) // 弹窗的是否展示
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: '',
  name: ''
})

const queryFormRef = ref() // 搜索的表单
const selectionList = ref<MemberVO[]>([]) // 列表的选择项
const handleSelectionChange = (rows: MemberVO[]) => {
  selectionList.value = rows
}

/**
 * 打开弹窗
 */
const open = async () => {
  dialogVisible.value = true
  await nextTick() // 等待，避免queryFormRef为空
  await resetQuery() // 重置查询条件
  await getList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交选择 */
const emit = defineEmits<{
  (e: 'success', value: MemberVO[]): void
}>()

const submitForm = () => {
  try {
    emit('success', selectionList.value)
  } finally {
    dialogVisible.value = false
  }
}

/** 加载列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await MemberApi.getMemberPage(queryParams)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const handleQuery = () => {
  queryParams.pageNo = 1
  selectionList.value = []
  getList()
}

// // 使用 computed 处理双向绑定
</script>

<style scoped>
.custom-width {
  width: 40% !important;
}
</style>
