<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="系统自动生成" disabled />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入手机号" disabled />
      </el-form-item>
      <el-form-item label="所属小区" prop="belongCommunityId">
        <!-- <el-input v-model="formData.belongCommunityId" placeholder="请输入所属小区" /> -->
        <el-input v-model="formData.belongCommunityName" readonly>
          <template #append>
            <el-button @click="openCommunityList('1')"> <Icon icon="ep:search" /> 选择 </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="充值余额" prop="rechargeBalance">
        <el-input v-model="formData.rechargeBalance" placeholder="请输入充值余额" disabled />
      </el-form-item>
      <el-form-item label="赠送余额" prop="giftBalance">
        <el-input v-model="formData.giftBalance" placeholder="请输入赠送余额" disabled />
      </el-form-item>
      <el-form-item label="月卡小区" prop="monthlyPassCommunityId">
        <!-- <el-input v-model="formData.monthlyPassCommunityId" placeholder="请输入包月卡小区" /> -->
        <el-input v-model="formData.monthlyPassCommunityName" readonly>
          <template #append>
            <el-button @click="openCommunityList('2')"> <Icon icon="ep:search" /> 选择 </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="月卡有效期" prop="monthlyPassDuration">
        <!-- <el-input v-model="formData.monthlyPassDuration" placeholder="请输入包月卡有效期" /> -->
        <!-- {{ formatDate(formData.monthlyPassDuration) }} -->
        <!-- <el-date-picker
          v-model="formData.monthlyPassDuration"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetime"
          start-placeholder="月卡有效期"
          class="!w-240px"
        /> -->

        <!-- <el-date-picker
          v-model="form.monthlyPassDuration"
          type="datetime"
          placeholder="选择日期时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker> -->
        <el-date-picker
          v-model="formData.monthlyPassDuration"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetime"
          placeholder="月卡有效期"
          class="!w-240px"
          disabled
        />
      </el-form-item>
      <el-form-item label="备注信息" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <!-- <el-form-item label="帐号状态（0正常 1停用）" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <CommunityList ref="communityListRef" @success="handleCommunityChange" />
</template>
<script setup lang="ts">
import { MemberApi, MemberVO } from '@/api/charging/member'
import { CommunityVO } from '@/api/charging/community'
import CommunityList from '@/views/charging/community/components/CommunityList.vue'

/** 会员，即充电的用户 表单 */
defineOptions({ name: 'MemberForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
// 创建一个响应式对象
const formData = ref({
  id: undefined,
  code: undefined,
  mobile: undefined,
  belongCommunityId: undefined,
  belongCommunityName: undefined,
  rechargeBalance: undefined,
  giftBalance: undefined,
  monthlyPassCommunityId: undefined,
  monthlyPassCommunityName: undefined,
  monthlyPassDuration: undefined,
  remark: undefined,
  status: undefined
})
const formRules = reactive({
  status: [{ required: true, message: '帐号状态（0正常 1停用）不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await MemberApi.getMember(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  debugger
  try {
    const data = formData.value as unknown as MemberVO
    if (formType.value === 'create') {
      await MemberApi.createMember(data)
      message.success(t('common.createSuccess'))
    } else {
      await MemberApi.updateMember(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    mobile: undefined,
    belongCommunityId: undefined,
    belongCommunityName: undefined,
    rechargeBalance: undefined,
    giftBalance: undefined,
    monthlyPassCommunityId: undefined,
    monthlyPassCommunityName: undefined,
    monthlyPassDuration: undefined,
    remark: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}

/** 选择的小区类型 */
const selectCommunityType = ref('')

/** 打开【小区列表】弹窗 */
const communityListRef = ref() // 可入库的订单列表 Ref
const openCommunityList = (type: string) => {
  console.log('type', type)
  selectCommunityType.value = type
  communityListRef.value.open()
}

const handleCommunityChange = (vo: CommunityVO) => {
  // debugger
  if (vo == null) {
    return
  }

  // console.log(selectCommunityType.value);
  if (selectCommunityType.value == '1') {
    // 所属小区
    formData.value.belongCommunityId = vo[0].id
    formData.value.belongCommunityName = vo[0].name
  } else {
    // 月卡小区
    formData.value.monthlyPassCommunityId = vo[0].id
    formData.value.monthlyPassCommunityName = vo[0].name
  }
}
</script>
