<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="系统自动生成" disabled />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="formData.name" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入手机号" />
      </el-form-item>
      <!-- <el-form-item label="分润点数" prop="profitSharingPoints">
        <el-input v-model="formData.profitSharingPoints" placeholder="请输入分润点数" disabled/>
      </el-form-item> -->
      <!-- <el-form-item label="历史分润" prop="historicalProfitSharing">
        <el-input v-model="formData.historicalProfitSharing" placeholder="请输入历史分润" disabled/>
      </el-form-item> -->
      <!-- <el-form-item label="已提现分润" prop="cashoutProfitSharing">
        <el-input v-model="formData.cashoutProfitSharing" placeholder="请输入已提现分润" disabled/>
      </el-form-item> -->
      <!-- <el-form-item label="管理的小区数" prop="communityNum">
        <el-input v-model="formData.communityNum" placeholder="请输入管理的小区数" />
      </el-form-item> -->

      <el-form-item label="会员" prop="mbeId">
        <el-input v-model="formData.mbeCode" readonly>
          <template #append>
            <el-button @click="openMbeList"> <Icon icon="ep:search" /> 选择 </el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="管理小区">
        <el-select v-model="formData.communityIds" multiple placeholder="请选择">
          <el-option
            v-for="item in communityList"
            :key="item.id"
            :label="item.name"
            :value="item.id!"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <!-- <el-form-item label="帐号状态（0正常 1停用）" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <MemberList ref="mbeListRef" @success="handleMbeChange" />
</template>
<script setup lang="ts">
import { PartnerApi, PartnerVO } from '@/api/charging/partner'
import { CommunityApi, CommunityVO } from '@/api/charging/community'
import MemberList from '@/views/charging/member/components/MemberList.vue'

/** 合作伙伴——小区管理人员（物业） 表单 */
defineOptions({ name: 'PartnerForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

const communityName = ref('') // 小区名称, 用户下列搜索
const communityList = ref([]) // 小区列表

const formData = ref({
  id: undefined,
  code: undefined,
  mobile: undefined,
  name: undefined,
  profitSharingPoints: undefined,
  historicalProfitSharing: undefined,
  cashoutProfitSharing: undefined,
  communityNum: undefined,
  remark: undefined,
  status: undefined,
  mbeId: undefined,
  communityIds: [],
  mbeCode: ''
})
const formRules = reactive({
  status: [{ required: true, message: '帐号状态（0正常 1停用）不能为空', trigger: 'blur' }],
  mbeId: [{ required: true, message: '会员不能为空', trigger: ['blur', 'click'] }]
})
const formRef = ref() // 表单 Ref

/** 打开【会员列表】弹窗 */
const mbeListRef = ref() // 可入库的订单列表 Ref
const openMbeList = (type: string) => {
  if (mbeListRef.value) {
    mbeListRef.value.open()
  }
}

const handleMbeChange = (vo: CommunityVO) => {
  if (vo == null) {
    return
  }

  // 所属小区
  formData.value.mbeId = vo[0].id
  formData.value.mbeCode = vo[0].code
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      // 获取小区列表
      const param = ref({ communityName: '' })
      communityList.value = await CommunityApi.getCommunityList(param.value)

      // 获取合作伙伴信息
      formData.value = await PartnerApi.getPartner(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    debugger
    const data = formData.value as unknown as PartnerVO
    if (formType.value === 'create') {
      await PartnerApi.createPartner(data)
      message.success(t('common.createSuccess'))
    } else {
      await PartnerApi.updatePartner(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    mobile: undefined,
    name: undefined,
    profitSharingPoints: undefined,
    historicalProfitSharing: undefined,
    cashoutProfitSharing: undefined,
    communityNum: undefined,
    remark: undefined,
    status: undefined,
    mbeId: undefined,
    communityIds: [],
    mbeCode: ''
  }
  formRef.value?.resetFields()
}
</script>
