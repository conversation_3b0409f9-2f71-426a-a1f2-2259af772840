<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="系统自动生成" disabled />
      </el-form-item>
      <!-- <el-form-item label="主管编号" prop="partnerCode">
        <el-input v-model="formData.partnerCode" placeholder="请输入主管编号" />
      </el-form-item> -->
      <el-form-item label="主管名称" prop="partnerName">
        <el-input v-model="formData.partnerName" placeholder="请输入主管名称" disabled />
      </el-form-item>
      <el-form-item label="主管手机" prop="partnerMobile">
        <el-input v-model="formData.partnerMobile" placeholder="请输入主管手机" disabled />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime" disabled>
        <!-- <el-date-picker
          v-model="formData.applyTime"
          type="date"
          value-format="x"
          placeholder="选择申请时间"
        /> -->
        <el-input v-model="formData.applyTime" disabled />
      </el-form-item>
      <el-form-item label="申请金额" prop="applyAmount">
        <el-input v-model="formData.applyAmount" placeholder="请输入申请金额" disabled />
      </el-form-item>
      <el-form-item label="提现银行名称" prop="bankName">
        <el-input v-model="formData.bankName" placeholder="请输入提现银行名称" disabled />
      </el-form-item>
      <el-form-item label="银行卡号" prop="bankCardNum">
        <el-input v-model="formData.bankCardNum" placeholder="请输入银行卡号" disabled />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="申请状态" prop="status">
        <!-- <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group> -->
        <el-select
          v-model="formData.status"
          placeholder="请选择申请状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.CHARGING_WITHDRAW_APPLY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { WithdrawalRecordApi, WithdrawalRecordVO } from '@/api/charging/withdrawalrecord'

/** 提现记录 表单 */
defineOptions({ name: 'WithdrawalRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  partnerCode: undefined,
  partnerName: undefined,
  partnerMobile: undefined,
  applyTime: undefined,
  applyAmount: undefined,
  bankName: undefined,
  bankCardNum: undefined,
  remark: undefined,
  status: undefined
})
const formRules = reactive({
  status: [{ required: true, message: '申请状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await WithdrawalRecordApi.getWithdrawalRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as WithdrawalRecordVO
    if (formType.value === 'create') {
      await WithdrawalRecordApi.createWithdrawalRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await WithdrawalRecordApi.updateWithdrawalRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    partnerCode: undefined,
    partnerName: undefined,
    partnerMobile: undefined,
    applyTime: undefined,
    applyAmount: undefined,
    bankName: undefined,
    bankCardNum: undefined,
    remark: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>
