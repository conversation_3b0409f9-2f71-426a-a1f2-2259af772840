<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="用户编号" prop="mbeCode">
        <el-input
          v-model="queryParams.mbeCode"
          placeholder="请输入用户编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="用户手机" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入用户手机"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <!-- <el-form-item label="申请状态" prop="applyStatus">
        <el-select
          v-model="queryParams.applyStatus"
          placeholder="请选择申请状态"
          clearable
          class="!w-240px"
        >
          <el-option 
           v-for="dict in getIntDictOptions(DICT_TYPE.CHARGING_REFUND_APPLY)"
           :key="dict.value"
           :label="dict.label"
           :value="dict.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" prop="approvalStatus">
        <el-select
          v-model="queryParams.approvalStatus"
          placeholder="请选择审批状态"
          clearable
          class="!w-240px"
        >
          <el-option 
           v-for="dict in getIntDictOptions(DICT_TYPE.CHARGING_REFUND_APPROVE)"
           :key="dict.value"
           :label="dict.label"
           :value="dict.value"/>
        </el-select>
      </el-form-item> -->

      <el-form-item label="申请时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['charging:refund-record:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
          -->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['charging:refund-record:export']"
        >
          <Icon icon="ep:download" class="mr-5px" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="编号" align="center" prop="code" width="150" />
      <el-table-column label="用户编号" align="center" prop="mbeCode" width="150" />
      <el-table-column label="用户手机" align="center" prop="mobile" width="150" />
      <el-table-column label="申请金额" align="center" prop="applyAmount" width="120" />
      <el-table-column label="退款状态" align="center" prop="status" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.CHARGING_REFUND_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="退款成功金额" align="center" prop="refundSuccessAmount"  width="120"/>
      <el-table-column label="申请状态" align="center" prop="applyStatus"  width="120"/>
      <el-table-column label="审批状态" align="center" prop="approvalStatus"  width="120"/> -->
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="申请时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['charging:refund-record:update']"
          >
            编辑
          </el-button>
          <!-- <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['charging:refund-record:delete']"
          >
            删除
          </el-button> -->
          <el-button
            link
            type="primary"
            @click="handleRefund(scope.row)"
            v-hasPermi="['charging:refund-record:update']"
            v-if="scope.row.status === 2"
          >
            退款
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <RefundRecordForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { RefundRecordApi, RefundRecordVO } from '@/api/charging/refundrecord'
import RefundRecordForm from './RefundRecordForm.vue'

/** 退款记录 列表 */
defineOptions({ name: 'RefundRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<RefundRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: undefined,
  mbeCode: undefined,
  mobile: undefined,
  applyAmount: undefined,
  refundSuccessAmount: undefined,
  applyStatus: undefined,
  approvalStatus: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RefundRecordApi.getRefundRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await RefundRecordApi.deleteRefundRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 手动发起退款 */
const handleRefund = async (obj: Object) => {
  try {
    // 二次确认
    const msg = '手机：' + obj.mobile + '，金额：' + obj.applyAmount + '元，确定要退款吗？'
    await message.confirm(msg)
    // 发起退款
    await RefundRecordApi.handleRefund(obj.id)
    message.success('发起成功')
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RefundRecordApi.exportRefundRecord(queryParams)
    download.excel(data, '退款记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
