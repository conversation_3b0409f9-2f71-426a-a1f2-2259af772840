<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="系统自动生成" disabled />
      </el-form-item>
      <el-form-item label="用户编号" prop="mbeCode">
        <el-input v-model="formData.mbeCode" placeholder="请输入用户编号" disabled />
      </el-form-item>
      <el-form-item label="用户手机号" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入用户手机号" disabled />
      </el-form-item>
      <el-form-item label="申请金额" prop="applyAmount">
        <el-input v-model="formData.applyAmount" placeholder="请输入申请金额" disabled />
      </el-form-item>
      <!-- <el-form-item label="退款成功金额" prop="refundSuccessAmount">
        <el-input v-model="formData.refundSuccessAmount" placeholder="请输入退款成功金额" />
      </el-form-item>
      <el-form-item label="申请状态" prop="applyStatus">
        <el-radio-group v-model="formData.applyStatus">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审批状态" prop="approvalStatus">
        <el-radio-group v-model="formData.approvalStatus">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { RefundRecordApi, RefundRecordVO } from '@/api/charging/refundrecord'

/** 退款记录 表单 */
defineOptions({ name: 'RefundRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  mbeCode: undefined,
  mobile: undefined,
  applyAmount: undefined,
  refundSuccessAmount: undefined,
  applyStatus: undefined,
  approvalStatus: undefined,
  remark: undefined
})
const formRules = reactive({})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await RefundRecordApi.getRefundRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as RefundRecordVO
    if (formType.value === 'create') {
      await RefundRecordApi.createRefundRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await RefundRecordApi.updateRefundRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    mbeCode: undefined,
    mobile: undefined,
    applyAmount: undefined,
    refundSuccessAmount: undefined,
    applyStatus: undefined,
    approvalStatus: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
