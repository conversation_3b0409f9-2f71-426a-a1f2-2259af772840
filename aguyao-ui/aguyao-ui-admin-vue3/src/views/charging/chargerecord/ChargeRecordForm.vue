<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="编号" prop="code">
            <el-input v-model="formData.code" placeholder="" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="消耗度数" prop="kilowatt">
            <el-input v-model="formData.kilowatt" placeholder="" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="用户编号" prop="mbeCode">
            <el-input v-model="formData.mbeCode" placeholder="" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户手机号" prop="mobile">
            <el-input v-model="formData.mobile" placeholder="" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="开始充电" prop="chargeTimeStart">
            <el-date-picker
              v-model="formData.chargeTimeStart"
              type="date"
              value-format="x"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="完成充电" prop="chargeTimeEnd">
            <el-date-picker
              v-model="formData.chargeTimeEnd"
              type="date"
              value-format="x"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="充电小区" prop="chargeCommunityName">
            <el-input v-model="formData.chargeCommunityName" placeholder="" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="楼栋号" prop="buildingsName">
            <el-input v-model="formData.buildingsName" placeholder="" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="设备号" prop="device">
            <el-input v-model="formData.device" placeholder="" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="消费金额" prop="actualAmount">
            <el-input v-model="formData.actualAmount" placeholder="" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="支付方式" prop="paySourceStr">
            <el-input :value="handlePaySource(formData.paySource)" placeholder="" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="充电状态" prop="proceedStatus">
            <el-input
              :value="handleProceedStatus(formData.proceedStatus)"
              placeholder=""
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" :rows="2" v-model="formData.remark" placeholder="" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ChargeRecordApi, ChargeRecordVO } from '@/api/charging/chargerecord'

/** 充电记录，消费记录 表单 */
defineOptions({ name: 'ChargeRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  mbeCode: undefined,
  mobile: undefined,
  chargeTime: undefined,
  chargeCommunityId: undefined,
  buildingsId: undefined,
  deviceId: undefined,
  amount: undefined,
  paySource: undefined,
  proceedStatus: undefined,
  wattage: undefined,
  remark: undefined,
  status: undefined
})
const formRules = reactive({
  status: [{ required: true, message: '帐号状态（0正常 1停用）不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ChargeRecordApi.getChargeRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ChargeRecordVO
    if (formType.value === 'create') {
      await ChargeRecordApi.createChargeRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await ChargeRecordApi.updateChargeRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    mbeCode: undefined,
    mobile: undefined,
    chargeTime: undefined,
    chargeCommunityId: undefined,
    buildingsId: undefined,
    deviceId: undefined,
    amount: undefined,
    paySource: undefined,
    proceedStatus: undefined,
    wattage: undefined,
    remark: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}

/**
 * 支付渠道
 */
const handlePaySource = (val: number) => {
  if (val === 1) {
    return '包月消费'
  } else if (val === 2 || val === 3) {
    return '余额支付'
  } else if (val === 4) {
    return '微信支付'
  }
}

/**
 * 充电状态
 */
const handleProceedStatus = (val: number) => {
  if (val === 0) {
    return '待充电'
  } else if (val === 1) {
    return '充电中'
  } else if (val === 2) {
    return '已完成'
  }
}
</script>
