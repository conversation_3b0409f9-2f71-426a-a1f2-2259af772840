<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item label="编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="用户编号" prop="mbeCode">
        <el-input
          v-model="queryParams.mbeCode"
          placeholder="请输入用户编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="用户手机" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入用户手机"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="充电小区" prop="chargeCommunityName">
        <el-input
          v-model="queryParams.chargeCommunityName"
          placeholder="请输入充电小区"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="充电时间" prop="chargeTimeStart">
        <el-date-picker
          v-model="queryParams.chargeTimeStart"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item label="设备编号" prop="device">
        <el-input
          v-model="queryParams.device"
          placeholder="请输入设备编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!--       
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['charging:charge-record:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
         -->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['charging:charge-record:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="编号" align="center" prop="code" width="150" fixed />
      <el-table-column label="用户编号" align="center" prop="mbeCode" width="150" />
      <el-table-column label="用户手机" align="center" prop="mobile" width="150" />
      <el-table-column
        label="开始充电"
        align="center"
        prop="chargeTimeStart"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="完成充电"
        align="center"
        prop="chargeTimeEnd"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="充电状态" align="center" prop="proceedStatus" width="100">
        <template #default="{ row }">
          <span
            :style="{
              color: row.proceedStatus === 1 ? 'red' : row.proceedStatus === 2 ? 'green' : 'gray'
            }"
          >
            {{ row.proceedStatus === 1 ? '充电中' : row.proceedStatus === 2 ? '已完成' : '待充电' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="充电小区" align="center" prop="chargeCommunityName" width="150" />
      <el-table-column label="楼栋" align="center" prop="buildingsName" width="150" />
      <el-table-column label="设备" align="center" prop="device" width="150" />
      <el-table-column label="消费金额" align="center" prop="actualAmount" width="100" />
      <el-table-column label="支付渠道" align="center" prop="paySource" width="150">
        <template #default="{ row }">
          {{ handlePaySource(row.paySource) }}
        </template>
      </el-table-column>

      <!-- <el-table-column label="消耗瓦数" align="center" prop="wattage" width="100"/> -->
      <el-table-column label="消耗度数" align="center" prop="kilowatt" width="100" />
      <el-table-column label="备注" align="center" prop="remark" width="180" />
      <!-- <el-table-column label="帐号状态（0正常 1停用）" align="center" prop="status" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['charging:charge-record:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['charging:charge-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ChargeRecordForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ChargeRecordApi, ChargeRecordVO } from '@/api/charging/chargerecord'
import ChargeRecordForm from './ChargeRecordForm.vue'

/** 充电记录，消费记录 列表 */
defineOptions({ name: 'ChargeRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ChargeRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: undefined,
  mbeCode: undefined,
  mobile: undefined,
  chargeTime: [],
  chargeCommunityId: undefined,
  chargeCommunityName: undefined,
  buildingsId: undefined,
  deviceId: undefined,
  amount: undefined,
  paySource: undefined,
  proceedStatus: undefined,
  wattage: undefined,
  remark: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ChargeRecordApi.getChargeRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ChargeRecordApi.deleteChargeRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ChargeRecordApi.exportChargeRecord(queryParams)
    download.excel(data, '充电记录，消费记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/**
 * 支付渠道
 */
const handlePaySource = (val: number) => {
  if (val === 1) {
    return '包月消费'
  } else if (val === 2 || val === 3) {
    return '余额支付'
  } else if (val === 4) {
    return '微信支付'
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
