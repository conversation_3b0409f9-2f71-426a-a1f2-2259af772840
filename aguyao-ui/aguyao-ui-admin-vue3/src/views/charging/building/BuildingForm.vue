<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="系统自动生成" disabled />
      </el-form-item>
      <el-form-item label="小区" prop="communityId">
        <el-input v-model="formData.communityName" readonly>
          <template #append>
            <el-button @click="openCommunityList"> <Icon icon="ep:search" /> 选择 </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入楼栋名称" />
      </el-form-item>
      <el-form-item label="收费方案" prop="schemeId">
        <el-input v-model="formData.schemeName" readonly>
          <template #append>
            <el-button @click="openSchemeList"> <Icon icon="ep:search" /> 选择 </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="设备数" prop="deviceNum">
        <el-input v-model="formData.deviceNum" placeholder="请输入设备数" />
      </el-form-item>
      <el-form-item label="设备数(充电)" prop="chargingDeviceNum">
        <el-input v-model="formData.chargingDeviceNum" placeholder="请输入充电中的设备数" />
      </el-form-item>
      <el-form-item label="设备数(待机)" prop="standbyDeviceNum">
        <el-input v-model="formData.standbyDeviceNum" placeholder="请输入待机设备数" />
      </el-form-item>
      <el-form-item label="设备数(断网)" prop="networkDisconnDeviceNum">
        <el-input v-model="formData.networkDisconnDeviceNum" placeholder="请输入断网设备数" />
      </el-form-item>
      <el-form-item label="费率" prop="rate">
        <!-- <el-input v-model="formData.rate" placeholder="请输入费率" /> -->
        <Editor v-model="formData.rate" height="150px" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <!-- <el-form-item label="帐号状态（0正常 1停用）" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
  <SchemeList ref="schemeListRef" @success="handleSchemeChange" />
  <CommunityList ref="communityListRef" @success="handleCommunityChange" />
</template>
<script setup lang="ts">
import { BuildingApi, BuildingVO } from '@/api/charging/building'
import { CommunityVO } from '@/api/charging/community'
import CommunityList from '@/views/charging/community/components/CommunityList.vue'
import { SchemeVO } from '@/api/charging/scheme'
import SchemeList from '@/views/charging/scheme/components/SchemeList.vue'

/** 楼栋 表单 */
defineOptions({ name: 'BuildingForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  name: undefined,
  deviceNum: undefined,
  chargingDeviceNum: undefined,
  standbyDeviceNum: undefined,
  networkDisconnDeviceNum: undefined,
  rate: undefined,
  remark: undefined,
  status: undefined,
  communityId: undefined,
  communityName: undefined,
  schemeId: undefined,
  schemeName: undefined
})

const formRules = reactive({
  status: [{ required: true, message: '帐号状态（0正常 1停用）不能为空', trigger: 'blur' }],
  schemeId: [{ required: true, message: '收费方案不能为空', trigger: ['blur', 'change'] }],
  communityId: [{ required: true, message: '小区名称', trigger: ['blur', 'change'] }],
  name: [{ required: true, message: '楼栋名称不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await BuildingApi.getBuilding(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 选择的小区类型 */
// const selectCommunityType = ref('')   //

/** 打开【小区列表】弹窗 */
const communityListRef = ref() // 可入库的订单列表 Ref
const openCommunityList = (type: string) => {
  // selectCommunityType.value = type
  if (communityListRef.value) {
    communityListRef.value.open()
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as BuildingVO
    if (formType.value === 'create') {
      await BuildingApi.createBuilding(data)
      message.success(t('common.createSuccess'))
    } else {
      await BuildingApi.updateBuilding(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    name: undefined,
    deviceNum: undefined,
    chargingDeviceNum: undefined,
    standbyDeviceNum: undefined,
    networkDisconnDeviceNum: undefined,
    rate: undefined,
    remark: undefined,
    status: undefined,
    communityId: undefined,
    communityName: undefined,
    schemeId: undefined,
    schemeName: undefined
  }
  formRef.value?.resetFields()
}

const handleCommunityChange = (vo: CommunityVO) => {
  if (vo == null) {
    return
  }

  // 所属小区
  formData.value.communityId = vo[0].id
  formData.value.communityName = vo[0].name
}

/** 打开【收费方案列表】弹窗 */
const schemeListRef = ref() // 可入库的订单列表 Ref
const openSchemeList = (type: string) => {
  if (schemeListRef.value) {
    schemeListRef.value.open()
  }
}

const handleSchemeChange = (vo: SchemeVO) => {
  if (vo == null) {
    return
  }

  // 所选方案
  formData.value.schemeId = vo[0].id
  formData.value.schemeName = vo[0].name
}
</script>
