<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="系统自动生成" disabled />
      </el-form-item>
      <el-form-item label="用户编号" prop="mbeCode">
        <el-input v-model="formData.mbeCode" placeholder="请输入用户编号" disabled />
      </el-form-item>
      <el-form-item label="用户手机号" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入用户手机号" disabled />
      </el-form-item>
      <el-form-item label="消费金额" prop="amount">
        <el-input v-model="formData.amount" placeholder="请输入消费金额" disabled />
      </el-form-item>
      <el-form-item label="购买时间" prop="purchaseTime">
        <el-date-picker
          v-model="formData.purchaseTime"
          type="date"
          value-format="x"
          placeholder="选择购买时间"
          disabled
        />
      </el-form-item>
      <el-form-item label="包月小区" prop="belongCommunityName">
        <el-input v-model="formData.belongCommunityName" placeholder="请输入包月小区" disabled />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" disabled />
      </el-form-item>
      <!-- <el-form-item label="帐号状态（0正常 1停用）" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { MonthlyPkgRecordApi, MonthlyPkgRecordVO } from '@/api/charging/monthlypkgrecord'

/** 包月记录 表单 */
defineOptions({ name: 'MonthlyPkgRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  mbeCode: undefined,
  mobile: undefined,
  amount: undefined,
  purchaseTime: undefined,
  belongCommunityId: undefined,
  remark: undefined,
  status: undefined
})
const formRules = reactive({
  belongCommunityId: [{ required: true, message: '包月小区id不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '帐号状态（0正常 1停用）不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await MonthlyPkgRecordApi.getMonthlyPkgRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as MonthlyPkgRecordVO
    if (formType.value === 'create') {
      await MonthlyPkgRecordApi.createMonthlyPkgRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await MonthlyPkgRecordApi.updateMonthlyPkgRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    mbeCode: undefined,
    mobile: undefined,
    amount: undefined,
    purchaseTime: undefined,
    belongCommunityId: undefined,
    remark: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>
