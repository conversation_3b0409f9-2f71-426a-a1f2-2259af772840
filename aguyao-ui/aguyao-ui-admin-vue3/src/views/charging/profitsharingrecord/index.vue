<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item label="编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="主管编号" prop="partnerCode">
        <el-input
          v-model="queryParams.partnerCode"
          placeholder="请输入主管编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="主管名称" prop="partnerName">
        <el-input
          v-model="queryParams.partnerName"
          placeholder="请输入主管名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="主管手机" prop="partnerMobile">
        <el-input
          v-model="queryParams.partnerMobile"
          placeholder="请输入主管手机"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item label="所属小区" prop="belongCommunityId">
        <el-input
          v-model="queryParams.belongCommunityId"
          placeholder="请输入所属小区"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="用户手机" prop="mbeMobile">
        <el-input
          v-model="queryParams.mbeMobile"
          placeholder="请输入用户手机"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="消费时间" prop="consumeTime">
        <el-date-picker
          v-model="queryParams.consumeTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="消费金额" prop="consumeAmount">
        <el-input
          v-model="queryParams.consumeAmount"
          placeholder="请输入消费金额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="消费类型" prop="consumeType">
        <el-select
          v-model="queryParams.consumeType"
          placeholder="请选择消费类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="帐号状态（0正常 1停用）" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择帐号状态（0正常 1停用）"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['charging:profit-sharing-record:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['charging:profit-sharing-record:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="编号" align="center" prop="code" />
      <el-table-column label="主管编号" align="center" prop="partnerCode" />
      <el-table-column label="主管名称" align="center" prop="partnerName" />
      <el-table-column label="主管手机" align="center" prop="partnerMobile" />
      <el-table-column label="费率" align="center" prop="rate" />
      <el-table-column label="分润" align="center" prop="profitSharing" />
      <el-table-column label="所属小区" align="center" prop="belongCommunityId" />
      <el-table-column label="用户手机" align="center" prop="mbeMobile" />
      <el-table-column
        label="消费时间"
        align="center"
        prop="consumeTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="消费金额" align="center" prop="consumeAmount" />
      <el-table-column label="消费类型" align="center" prop="consumeType" />
      <el-table-column label="备注" align="center" prop="remark" />
      <!-- <el-table-column label="帐号状态（0正常 1停用）" align="center" prop="status" /> -->
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['charging:profit-sharing-record:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['charging:profit-sharing-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ProfitSharingRecordForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ProfitSharingRecordApi, ProfitSharingRecordVO } from '@/api/charging/profitsharingrecord'
import ProfitSharingRecordForm from './ProfitSharingRecordForm.vue'

/** 分润记录 列表 */
defineOptions({ name: 'ProfitSharingRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ProfitSharingRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: undefined,
  partnerCode: undefined,
  partnerName: undefined,
  partnerMobile: undefined,
  rate: undefined,
  profitSharing: undefined,
  belongCommunityId: undefined,
  mbeMobile: undefined,
  consumeTime: [],
  consumeAmount: undefined,
  consumeType: undefined,
  remark: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProfitSharingRecordApi.getProfitSharingRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProfitSharingRecordApi.deleteProfitSharingRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProfitSharingRecordApi.exportProfitSharingRecord(queryParams)
    download.excel(data, '分润记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
