<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="系统自动生成" disabled />
      </el-form-item>
      <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="请输入编号" />
      </el-form-item>
      <el-form-item label="主管编号" prop="partnerCode">
        <el-input v-model="formData.partnerCode" placeholder="请输入主管编号" />
      </el-form-item>
      <el-form-item label="主管名称" prop="partnerName">
        <el-input v-model="formData.partnerName" placeholder="请输入主管名称" />
      </el-form-item>
      <el-form-item label="主管手机" prop="partnerMobile">
        <el-input v-model="formData.partnerMobile" placeholder="请输入主管手机" />
      </el-form-item>
      <el-form-item label="费率" prop="rate">
        <el-input v-model="formData.rate" placeholder="请输入费率" />
      </el-form-item>
      <el-form-item label="分润" prop="profitSharing">
        <el-input v-model="formData.profitSharing" placeholder="请输入分润" />
      </el-form-item>
      <el-form-item label="所属小区id" prop="belongCommunityId">
        <el-input v-model="formData.belongCommunityId" placeholder="请输入所属小区id" />
      </el-form-item>
      <el-form-item label="用户手机" prop="mbeMobile">
        <el-input v-model="formData.mbeMobile" placeholder="请输入用户手机" />
      </el-form-item>
      <el-form-item label="消费时间" prop="consumeTime">
        <el-date-picker
          v-model="formData.consumeTime"
          type="date"
          value-format="x"
          placeholder="选择消费时间"
        />
      </el-form-item>
      <el-form-item label="消费金额" prop="consumeAmount">
        <el-input v-model="formData.consumeAmount" placeholder="请输入消费金额" />
      </el-form-item>
      <el-form-item label="消费类型" prop="consumeType">
        <el-select v-model="formData.consumeType" placeholder="请选择消费类型">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="帐号状态（0正常 1停用）" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ProfitSharingRecordApi, ProfitSharingRecordVO } from '@/api/charging/profitsharingrecord'

/** 分润记录 表单 */
defineOptions({ name: 'ProfitSharingRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  partnerCode: undefined,
  partnerName: undefined,
  partnerMobile: undefined,
  rate: undefined,
  profitSharing: undefined,
  belongCommunityId: undefined,
  mbeMobile: undefined,
  consumeTime: undefined,
  consumeAmount: undefined,
  consumeType: undefined,
  remark: undefined,
  status: undefined
})
const formRules = reactive({
  status: [{ required: true, message: '帐号状态（0正常 1停用）不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProfitSharingRecordApi.getProfitSharingRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ProfitSharingRecordVO
    if (formType.value === 'create') {
      await ProfitSharingRecordApi.createProfitSharingRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProfitSharingRecordApi.updateProfitSharingRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    partnerCode: undefined,
    partnerName: undefined,
    partnerMobile: undefined,
    rate: undefined,
    profitSharing: undefined,
    belongCommunityId: undefined,
    mbeMobile: undefined,
    consumeTime: undefined,
    consumeAmount: undefined,
    consumeType: undefined,
    remark: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>
