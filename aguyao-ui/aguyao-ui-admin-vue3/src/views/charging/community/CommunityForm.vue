<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <!-- <el-form-item label="编号" prop="code">
        <el-input v-model="formData.code" placeholder="系统自动生成" disabled/>
      </el-form-item> -->
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入小区名称" />
      </el-form-item>
      <el-form-item label="分成比例(%)" prop="profitSharingPoints">
        <el-input-number
          v-model="formData.profitSharingPoints"
          placeholder="请输入分成比例"
          style="width: 100%"
          min="0"
          max="100"
        />
      </el-form-item>

      <el-form-item label="商户负责人" prop="partnerId">
        <el-input v-model="formData.partnerName" readonly>
          <template #append>
            <el-button @click="openPartnerList"> <Icon icon="ep:search" /> 选择 </el-button>
          </template>
        </el-input>
      </el-form-item>

      <!-- <el-form-item label="楼栋数" prop="buildingsNum">
        <el-input v-model="formData.buildingsNum" placeholder="请输入楼栋数" />
      </el-form-item>
      <el-form-item label="设备数" prop="deviceNum">
        <el-input v-model="formData.deviceNum" placeholder="请输入设备数" />
      </el-form-item>
      <el-form-item label="用户数" prop="mbeNum">
        <el-input v-model="formData.mbeNum" placeholder="请输入用户数" />
      </el-form-item> -->

      <el-form-item label="经度" prop="longitude">
        <el-input v-model="formData.longitude" placeholder="请输入经度" />
      </el-form-item>
      <el-form-item label="纬度" prop="latitude">
        <el-input v-model="formData.latitude" placeholder="请输入纬度" />
      </el-form-item>

      <el-form-item label="地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入地址" />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
      <!-- <el-form-item label="费率" prop="rate" >
        <Editor v-model="formData.rate" height="150px" />
      </el-form-item> -->
      <!-- <el-form-item label="帐号状态（0正常 1停用）" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio label="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <PartnerList ref="partnerListRef" @success="handlePartnerChange" />
</template>
<script setup lang="ts">
import { CommunityApi, CommunityVO } from '@/api/charging/community'
import { PartnerVO } from '@/api/charging/partner'
import PartnerList from '@/views/charging/partner/components/PartnerList.vue'

/** 小区 表单 */
defineOptions({ name: 'CommunityForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  code: undefined,
  name: undefined,
  buildingsNum: undefined,
  deviceNum: undefined,
  mbeNum: undefined,
  remark: undefined,
  status: undefined,
  address: undefined,
  longitude: undefined,
  latitude: undefined,
  partnerId: undefined,
  partnerName: undefined
  // rate: undefined,
})
const formRules = reactive({
  name: [{ required: true, message: '小区名称不能为空', trigger: 'blur' }],
  profitSharingPoints: [{ required: true, message: '分成比例不能为空', trigger: 'blur' }],
  partnerId: [{ required: true, message: '商户负责人不能为空', trigger: ['blur', 'click'] }],
  latitude: [
    { required: true, message: '纬度不能为空', trigger: 'blur' },
    {
      pattern: /^[-+]?([1-8]?\d(\.\d{1,15})?|90(\.0{1,15})?)$/,
      message: '纬度必须在-90到+90之间，最多15位小数点',
      trigger: 'blur'
    }
  ],
  longitude: [
    { required: true, message: '经度不能为空', trigger: 'blur' },
    { 
      pattern: /^[-+]?((1[0-7]\d|0?\d{1,2})(\.\d{1,15})?|180(\.0{1,15})?)$/,
      message: '经度必须在-180到+180之间，最多15位小数点',
      trigger: 'blur'
    }
  ]
  
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CommunityApi.getCommunity(id)
      debugger
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 
  if (!lngVerify() || !latVerify()) {
    return false;
  }
  
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CommunityVO
    if (formType.value === 'create') {
      await CommunityApi.createCommunity(data)
      message.success(t('common.createSuccess'))
    } else {
      await CommunityApi.updateCommunity(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    code: undefined,
    name: undefined,
    buildingsNum: undefined,
    deviceNum: undefined,
    mbeNum: undefined,
    remark: undefined,
    status: undefined,
    address: undefined,
    longitude: undefined,
    latitude: undefined,
    partnerId: undefined,
    partnerName: undefined
    // rate: undefined,
  }
  formRef.value?.resetFields()
}

const partnerList = ref([]) // 小区列表
/** 打开【商户列表】弹窗 */
const partnerListRef = ref() // 可入库的订单列表 Ref
const openPartnerList = (type: string) => {
  if (partnerListRef.value) {
    partnerListRef.value.open()
  }
}

const handlePartnerChange = (vo: PartnerVO) => {
  if (vo == null) {
    return
  }

  // 所属商户
  formData.value.partnerId = vo[0].id
  formData.value.partnerName = vo[0].name
}

// 纬度正则校验
const latVerify = () => {
  const latitudeRegex = /^[-+]?([1-8]?\d(\.\d{1,15})?|90(\.0{1,15})?)$/; 
  if (formData.value.latitude != null && !latitudeRegex.test(formData.value.latitude)) {
    message.error("纬度必须在-90到+90之间，最多15位小数点");
    return false;
  }
  return true;
}

// 经度正则校验
const lngVerify = () => {
  const longitudeRegex = /^[-+]?((1[0-7]\d|0?\d{1,2})(\.\d{1,15})?|180(\.0{1,15})?)$/;
  if (formData.value.longitude != null && !longitudeRegex.test(formData.value.longitude)) {
    message.error("经度必须在-180到+180之间，最多15位小数点");
    return false;
  }
  return true;
}
</script>
