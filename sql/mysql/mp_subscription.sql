-- 微信公众号消息订阅表
CREATE TABLE `mp_subscription` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
    `name` varchar(100) NOT NULL COMMENT '订阅名称',
    `account_id` bigint NOT NULL COMMENT '公众号账号编号',
    `app_id` varchar(32) NOT NULL COMMENT '公众号 appId',
    `subscription_type` tinyint NOT NULL COMMENT '订阅类型：1-关注事件，2-取消关注事件，3-消息事件，4-菜单点击事件，5-扫码事件，6-位置事件，99-自定义事件',
    `trigger_condition` varchar(500) DEFAULT NULL COMMENT '触发条件',
    `callback_url` varchar(500) DEFAULT NULL COMMENT '回调URL',
    `callback_method` varchar(10) DEFAULT 'POST' COMMENT '回调方法',
    `callback_headers` text DEFAULT NULL COMMENT '回调头部信息，JSON格式',
    `message_template` text DEFAULT NULL COMMENT '消息模板',
    `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `trigger_count` int NOT NULL DEFAULT 0 COMMENT '触发次数',
    `last_trigger_time` datetime DEFAULT NULL COMMENT '最后触发时间',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
    PRIMARY KEY (`id`),
    KEY `idx_account_id` (`account_id`),
    KEY `idx_app_id` (`app_id`),
    KEY `idx_subscription_type` (`subscription_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信公众号消息订阅表';

-- 插入字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('微信公众号订阅类型', 'mp_subscription_type', 0, '微信公众号消息订阅类型', '1', NOW(), '1', NOW(), b'0');

-- 插入字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '关注事件', '1', 'mp_subscription_type', 0, 'primary', '', '用户关注公众号时触发', '1', NOW(), '1', NOW(), b'0'),
(2, '取消关注事件', '2', 'mp_subscription_type', 0, 'danger', '', '用户取消关注公众号时触发', '1', NOW(), '1', NOW(), b'0'),
(3, '消息事件', '3', 'mp_subscription_type', 0, 'info', '', '用户发送消息时触发', '1', NOW(), '1', NOW(), b'0'),
(4, '菜单点击事件', '4', 'mp_subscription_type', 0, 'warning', '', '用户点击自定义菜单时触发', '1', NOW(), '1', NOW(), b'0'),
(5, '扫码事件', '5', 'mp_subscription_type', 0, 'success', '', '用户扫描二维码时触发', '1', NOW(), '1', NOW(), b'0'),
(6, '位置事件', '6', 'mp_subscription_type', 0, 'default', '', '用户上报地理位置时触发', '1', NOW(), '1', NOW(), b'0'),
(99, '自定义事件', '99', 'mp_subscription_type', 0, 'default', '', '自定义触发条件', '1', NOW(), '1', NOW(), b'0');

-- 插入字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES ('微信公众号订阅状态', 'mp_subscription_status', 0, '微信公众号消息订阅状态', '1', NOW(), '1', NOW(), b'0');

-- 插入字典数据
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
(1, '启用', '1', 'mp_subscription_status', 0, 'success', '', '订阅启用状态', '1', NOW(), '1', NOW(), b'0'),
(0, '禁用', '0', 'mp_subscription_status', 0, 'danger', '', '订阅禁用状态', '1', NOW(), '1', NOW(), b'0');

-- 插入菜单权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
('微信公众号消息订阅', '', 2, 10, 2218, 'subscription', 'ep:bell', 'mp/subscription/index', 'MpSubscription', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0');

-- 获取刚插入的菜单ID（这里假设是自增的，实际使用时需要根据实际情况调整）
SET @menu_id = LAST_INSERT_ID();

-- 插入按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
('消息订阅查询', 'mp:subscription:query', 3, 1, @menu_id, '', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0'),
('消息订阅创建', 'mp:subscription:create', 3, 2, @menu_id, '', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0'),
('消息订阅更新', 'mp:subscription:update', 3, 3, @menu_id, '', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0'),
('消息订阅删除', 'mp:subscription:delete', 3, 4, @menu_id, '', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0'),
('消息订阅测试', 'mp:subscription:test', 3, 5, @menu_id, '', '', '', 0, b'1', b'1', b'1', '1', NOW(), '1', NOW(), b'0');
