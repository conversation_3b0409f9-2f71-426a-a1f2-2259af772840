<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aguyao.module.charging.dal.mysql.community.CommunityMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getStationPage" parameterType="cn.aguyao.module.charging.controller.app.station.vo.AppStationPageReqVO"
            resultType="cn.aguyao.module.charging.controller.app.station.vo.AppStationRespVO">
        select *
        from (select st_distance_sphere(point(longitude, latitude),
                                        point(#{reqVO.longitude}, #{reqVO.latitude}))                              as distance,
                     t.longitude,
                     t.latitude,
                     t.id,
                     t.name,
                     t.address,
                     (select count(*) from charging_device d where d.community_id = t.id) as totalDevice
              from charging_community t
              where t.longitude is not null
                and t.latitude is not null) ot
        order by ot.distance asc
    </select>


    <select id="selectPage4Mgr"
            resultType="cn.aguyao.module.charging.controller.mgr.community.vo.MgrCommunityRespVO">

        select distinct t.id, t.name , t.address,
            (select count(*) from charging_device d where d.community_id = t.id) as totalDevice
        from charging_community t
        left join charging_device t2 on t2.community_id = t.id
        left join charging_partner_community t3 on t3.community_id = t.id
        where 1=1 and t3.mp_id = #{mpId}
        <if test="communityName != null and communityName != ''">
            and t.name like concat('%', #{communityName}, '%')
        </if>
        order by t.id
    </select>

</mapper>
