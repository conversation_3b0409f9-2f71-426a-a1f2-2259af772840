<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aguyao.module.charging.dal.mysql.partnerbank.PartnerBankMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="findPage"
            resultType="cn.aguyao.module.charging.dal.dataobject.partnerbank.PartnerBankDO">

        select t1.* from charging_partner_bank t1
        left join charging_partner t2 on t1.partner_id = t2.id
        where 1=1 and t1.deleted = 0 and t2.deleted = 0
        <if test="reqVO.cardNo != null and reqVO.cardNo != ''">
            and t1.card_no like concat('%', #{reqVO.cardNo}, '%')
        </if>
        <if test="reqVO.bankName != null and reqVO.bankName != ''">
            and t1.bank_name like concat('%', #{reqVO.bankName}, '%')
        </if>
        <if test="reqVO.bankDeposit != null and reqVO.bankDeposit != ''">
            and t1.bank_deposit like concat('%', #{reqVO.bankDeposit}, '%')
        </if>
        order by t1.id desc
    </select>
</mapper>