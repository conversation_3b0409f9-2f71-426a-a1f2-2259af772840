<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aguyao.module.charging.dal.mysql.device.DeviceMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="findPage" parameterType="cn.aguyao.module.charging.controller.admin.device.vo.DevicePageReqVO"
            resultType="cn.aguyao.module.charging.dal.dataobject.device.DeviceDO">
        select t1.* from charging_device t1
        left join charging_community t2 on t1.community_id = t2.id and t2.deleted = 0
        where 1=1
          and t1.deleted = 0
          <if test="reqVO.communityName != null and reqVO.communityName != ''">
              and t2.name like concat('%', #{reqVO.communityName}, '%')
          </if>
        <if test="reqVO.code != null and reqVO.code != ''">
            and t1.code like concat('%', #{reqVO.code}, '%')
        </if>
        <if test="reqVO.device != null and reqVO.device != ''">
            and t1.device like concat('%', #{reqVO.device}, '%')
        </if>

        order by t1.create_time desc
    </select>


    <select id="useageAmt" resultType="java.lang.Integer">
        select count(distinct t2.device)
        from charging_device t1
        left join charging_charge_record t2 on t1.device = t2.device
        where 1=1 and t1.community_id = #{communityId}
          and t2.proceed_status in (1,2)
          and t2.charge_time_start >= #{startTime}
    </select>
</mapper>
