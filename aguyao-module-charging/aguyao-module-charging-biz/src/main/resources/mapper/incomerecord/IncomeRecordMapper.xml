<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aguyao.module.charging.dal.mysql.incomerecord.IncomeRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="incomeCollect" resultType="cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCollectRespVO">
        SELECT
            SUM(CASE WHEN DATE(t1.create_time) = CURDATE() THEN share_benefit ELSE 0 END) AS todayIncome,
            SUM(CASE WHEN DATE(t1.create_time) >= DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN share_benefit ELSE 0 END) AS monthIncome
        FROM
            charging_income_record t1
            left join charging_community t2 on t1.community_id = t2.id
        WHERE
            t1.partner_id = #{partnerId}
            <if test="communityName != null and communityName != ''">
                AND t2.name like concat('%', #{communityName}, '%')
            </if>
    </select>


    <select id="incomeCommunity"
            resultType="cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCommunityRespVO">
        SELECT
            t1.community_id as communityId,
            t2.name as communityName,
            SUM(CASE WHEN DATE(t1.create_time) = CURDATE() THEN t1.share_benefit ELSE 0 END) AS todayIncome,
            SUM(CASE WHEN DATE(t1.create_time) >= DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN t1.share_benefit ELSE 0 END) AS monthIncome,
            t2.profit_sharing_points  as shareRatio
        FROM
            charging_income_record t1
            left join charging_community t2 on t1.community_id = t2.id
        WHERE
            t1.partner_id = #{reqVO.partnerId}
            <if test="reqVO.communityName != null and reqVO.communityName != ''">
                AND t2.name like concat('%', #{reqVO.communityName}, '%')
            </if>

            group by t1.community_id
    </select>

</mapper>
