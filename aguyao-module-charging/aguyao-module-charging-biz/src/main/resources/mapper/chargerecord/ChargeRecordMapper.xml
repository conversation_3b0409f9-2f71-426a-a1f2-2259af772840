<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.aguyao.module.charging.dal.mysql.chargerecord.ChargeRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="getChargeRecord4Export"
            parameterType="cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordPageReqVO"
            resultType="cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordRespVO">

        select t1.code, t1.mbe_code as mbeCode,
        t1.mobile, t1.charge_time_start as chargeTimeStart, t1.charge_time_end as chargeTimeEnd,
        t1.device, t1.port, t1.actual_amount as actualAmount, t1.wattage,
        t1.kilowatt, t1.status, t1.pay_source as paySource,
        t1.proceed_status as proceedStatus,t1.remark,t1.power,
        t1.actual_charge_time as actualChargeTime,
        t1.guard_falg as guardFalg,

        t2.name as chargeCommunityName,
        t3.name as buildingsName
        from
        charging_charge_record t1
        left join charging_community t2 on t2.id = t1.charge_community_id
        left join charging_building t3 on t3.id = t1.buildings_id
        where 1=1
        and t1.deleted = 0
        and t1.mbe_id is not null

        <if test="reqVO.chargeTimeStart != null ">
            and t1.charge_time_start between #{reqVO.chargeTimeStart[0]} and #{reqVO.chargeTimeStart[1]}
        </if>

        <if test="reqVO.device != null and reqVO.device != ''">
            and t1.device like concat('%', #{reqVO.device}, '%')
        </if>

        <if test="reqVO.mobile != null and reqVO.mobile != ''">
            and t1.mobile like concat('%', #{reqVO.mobile}, '%')
        </if>

        <if test="reqVO.chargeCommunityName != null and reqVO.chargeCommunityName != ''">
            and t2.name like concat('%', #{reqVO.chargeCommunityName}, '%')
        </if>
    </select>

</mapper>