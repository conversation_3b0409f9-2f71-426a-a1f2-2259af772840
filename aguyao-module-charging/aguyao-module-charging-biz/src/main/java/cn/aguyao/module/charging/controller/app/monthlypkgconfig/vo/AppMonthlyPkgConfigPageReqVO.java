package cn.aguyao.module.charging.controller.app.monthlypkgconfig.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 包月配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppMonthlyPkgConfigPageReqVO extends PageParam {

    @Schema(description = "小区id", example = "29573")
    private Long communityId;

//    @Schema(description = "一个月价格", example = "27013")
//    private BigDecimal oneMonthPrice;
//
//    @Schema(description = "三个月价格", example = "14995")
//    private BigDecimal threeMonthsPrice;
//
//    @Schema(description = "六个月价格", example = "8755")
//    private BigDecimal sixMonthsPrice;
//
//    @Schema(description = "一年价格", example = "13497")
//    private BigDecimal oneYearPrice;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


    @Schema(description = "排序", example = "2")
    private Integer sort;

    @Schema(description = "价格", example = "2")
    private String price;

    @Schema(description = "提示", example = "2")
    private String title;

}
