package cn.aguyao.module.charging.controller.mgr.community.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 小区分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MgrCommunityPageReqVO extends PageParam {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "小区名称", example = "赵六")
    private String name;

    @Schema(description = "楼栋数")
    private Integer buildingsNum;

    @Schema(description = "设备数")
    private Integer deviceNum;

    @Schema(description = "用户数，会员数")
    private Integer mbeNum;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "合作方id")
    private Long partnerId;

    @Schema(description = "地址")
    private String address;
}
