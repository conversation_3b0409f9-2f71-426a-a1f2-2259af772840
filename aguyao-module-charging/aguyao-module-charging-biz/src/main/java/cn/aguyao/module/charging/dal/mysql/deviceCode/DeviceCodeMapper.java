package cn.aguyao.module.charging.dal.mysql.deviceCode;

import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.dal.dataobject.deviceCode.DeviceCodeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 设备编码 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceCodeMapper extends BaseMapperX<DeviceCodeDO> {


    default DeviceCodeDO findDeviceByCode(String code) {
        return selectOne(new LambdaQueryWrapperX<DeviceCodeDO>()
                .eq(DeviceCodeDO::getCode, code));
    }

    default DeviceCodeDO findByDevice(String device) {
        return selectOne(new LambdaQueryWrapperX<DeviceCodeDO>()
                .eq(DeviceCodeDO::getDevice, device));
    }
}
