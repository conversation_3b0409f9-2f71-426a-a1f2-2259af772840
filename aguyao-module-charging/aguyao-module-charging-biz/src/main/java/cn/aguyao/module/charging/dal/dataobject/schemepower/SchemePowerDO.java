package cn.aguyao.module.charging.dal.dataobject.schemepower;

import cn.aguyao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 收费方案—功率档位 DO
 *
 * <AUTHOR>
 */
@TableName("charging_scheme_power")
@KeySequence("charging_scheme_power_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchemePowerDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 方案id
     */
    private Long schemeId;
    /**
     * 序号
     */
    private Integer sort;
    /**
     * 功率区间，左侧（包含）
     */
    private Integer startPower;
    /**
     * 功率区间，右侧（不含）
     */
    private Integer endPower;
    /**
     * 收费标准（单位：元）
     */
    private BigDecimal amount;
    /**
     * 备注
     */
    private String remark;

}
