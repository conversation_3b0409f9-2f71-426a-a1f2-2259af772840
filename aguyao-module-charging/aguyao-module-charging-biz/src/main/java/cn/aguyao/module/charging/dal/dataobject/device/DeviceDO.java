package cn.aguyao.module.charging.dal.dataobject.device;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 设备 DO
 *
 * <AUTHOR>
 */
@TableName("charging_device")
@KeySequence("charging_device_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 编号
     */
    private String code;
    /**
     * 联网状态
     */
    private Integer networkStatus;
    /**
     * 运行状态
     */
    private Integer runningStatus;
    /**
     * 备注
     */
    private String remark;

    /**
     * 小区id
     */
    private Long communityId;

    /**
     * 楼栋id
     */
    private Long buildingId;


    @TableField(exist = false)
    private String communityName;

    /**
     * 楼栋名称
     */
    @TableField(exist = false)
    private String buildingName;

    /**
     * 计费方案名称
     */
    @TableField(exist = false)
    private String schemeName;

    /**
     * 设备号
     */
    private String device;

    /**
     * 信号强度0-31
     */
    private int rssi;

    /**
     * 4G模块IMEI 15位IMEI号
     */
    private String imei;

    /**
     * SIM卡ICCID 15位ICCID
     */
    private String iccid;

    /**
     * 设备软件版本号LF_101_057_4G_Luat_V0020_ASR1802_720D
     */
    private String version;

    /**
     * 服务器类型TCP,MQTT,IOT
     */
    private String servertype;


    /**
     * 服务器IP地址www.xxxxx.com连接阿里云时ProductKey
     */
    private String address;

    /**
     * 服务器端口号8100连接阿里云时ProductSecret
     */
    private String port;

    /**
     * MQTT用户名
     */
    private String mqttuser;

    /**
     * MQTT密码
     */
    private String mqttpass;


    /**
     * 目标地址，默认值等于address
     */
    private String targetAddr;


    /**
     * 服务器端口号8100连接阿里云时ProductSecret
     */
    private String targetPort;

    /**
     * MQTT用户名
     */
    private String targetMqttuser;

    /**
     * MQTT密码
     */
    private String targetMqttpass;


    /**
     * 心跳间隔时间(秒) 60
     */
    private int heart;

    /**
     * 业务流水号000001(流水号示例长度不限)
     */
    private String seq;

    /**
     * 端口号(1-N)
     */
    private Integer port1;

    /**
     * 当前端口输出最大功率(0-1000W)
     */
    private Integer power1;

    /**
     * 当前端口输出时间秒(1-9999999)
     */
    private Integer outTime1;

    /**
     * 端口状态( 0端口关闭，1端口输出)
     */
    private Integer btStat1;

    /**
     * 端口累计输出电能(单位(W*MIN)瓦分钟)
     */
    private String energy1;

    /**
     * 端口号(1-N)
     */
    private Integer port2;

    /**
     * 当前端口输出最大功率(0-1000W)
     */
    private Integer power2;

    /**
     * 当前端口输出时间秒(1-9999999)
     */
    private Integer outTime2;

    /**
     * 端口状态( 0端口关闭，1端口输出)
     */
    private Integer btStat2;

    /**
     * 端口累计输出电能(单位(W*MIN)瓦分钟)
     */
    private String energy2;

    /**
     * 最近访问时间
     */
    @TableField(exist = false)
    private String latestTime;
}
