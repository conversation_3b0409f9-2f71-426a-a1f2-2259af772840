package cn.aguyao.module.charging.controller.admin.banner.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 移动端banner Response VO")
@Data
@ExcelIgnoreUnannotated
public class BannerRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13933")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "标签")
    @ExcelProperty("标签")
    private String tag;

    @Schema(description = "跳转链接")
    @ExcelProperty("跳转链接")
    private String jumpLink;

    @Schema(description = "图片链接", example = "https://www.iocoder.cn")
    @ExcelProperty("图片链接")
    private String imageUrl;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}