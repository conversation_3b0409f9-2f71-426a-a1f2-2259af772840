package cn.aguyao.module.charging.controller.app.schemecollect.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 收费方案—收费套餐 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppSchemeCollectRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12603")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "方案id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11194")
    @ExcelProperty("方案id")
    private Long schemeId;

    @Schema(description = "收费金额")
    @ExcelProperty("收费金额")
    private BigDecimal amount;

    @Schema(description = "描述")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "排序", example = "1")
    private Integer sort;
}
