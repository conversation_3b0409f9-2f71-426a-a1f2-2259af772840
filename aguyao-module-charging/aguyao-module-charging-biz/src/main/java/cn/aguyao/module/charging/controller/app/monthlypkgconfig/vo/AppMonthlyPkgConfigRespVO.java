package cn.aguyao.module.charging.controller.app.monthlypkgconfig.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 包月配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppMonthlyPkgConfigRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10348")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "小区id", example = "29573")
    @ExcelProperty("小区id")
    private Long communityId;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    @Schema(description = "排序", example = "2")
    private Integer sort;

    @Schema(description = "价格", example = "2")
    private String price;

    @Schema(description = "提示", example = "2")
    private String title;
}
