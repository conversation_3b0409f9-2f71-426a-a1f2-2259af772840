package cn.aguyao.module.charging.controller.app.chargerecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 充电记录，消费记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppChargeRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "25604")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "用户id")
    @ExcelProperty("用户id")
    private Long mbeId;

    @Schema(description = "用户编号")
    @ExcelProperty("用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    @ExcelProperty("用户手机号")
    private String mobile;

    /**
     * 充电时间_开始充电的时间
     */
    private LocalDateTime chargeTimeStart;
    /**
     * 充电时间_结束充电的时间
     */
    private LocalDateTime chargeTimeEnd;

    @Schema(description = "充电小区id", example = "3424")
    @ExcelProperty("充电小区id")
    private Long chargeCommunityId;

    @Schema(description = "楼栋id", example = "14702")
    @ExcelProperty("楼栋id")
    private Long buildingsId;

    @Schema(description = "设备id", example = "26557")
    @ExcelProperty("设备id")
    private Long deviceId;

    @Schema(description = "设备号", example = "26557")
    @ExcelProperty("设备号")
    private String device;

    @Schema(description = "消费金额")
    @ExcelProperty("消费金额")
    private BigDecimal amount;

    /**
     * 实际支付金额
     */
    @Schema(description = "实际支付金额")
    @ExcelProperty("实际支付金额")
    private BigDecimal actualAmount;

    @Schema(description = "支付来源，消费来源")
    @ExcelProperty("支付来源，消费来源")
    private Integer paySource;

    @Schema(description = "进行状态", example = "1")
    @ExcelProperty("进行状态")
    private Integer proceedStatus;

    @Schema(description = "瓦数，消耗的瓦数")
    @ExcelProperty("瓦数，消耗的瓦数")
    private BigDecimal wattage;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    @Schema(description = "功率")
    @ExcelProperty("功率")
    private Integer power;

    @Schema(description = "实际充电时间")
    @ExcelProperty("实际充电时间")
    private String actualChargeTime;

    @Schema(description = "充电时间")
    @ExcelProperty("充电时间")
    private String discountChargeTime;

    @Schema(description = "小区")
    @ExcelProperty("小区")
    private String communityName;

    @Schema(description = "楼栋")
    @ExcelProperty("楼栋")
    private String buildingName;

    @Schema(description = "分摊比例")
    @ExcelProperty("分摊比例")
    private BigDecimal profitSharingPoints;

    @Schema(description = "分摊金额")
    @ExcelProperty("分摊金额")
    private BigDecimal shareBenefit;

    @Schema(description = "用电度数")
    @ExcelProperty("用电度数")
    private BigDecimal kilowatt;

    @Schema(description = "服务费")
    @ExcelProperty("服务费")
    private BigDecimal serviceAmt;
}
