package cn.aguyao.module.charging.controller.admin.building;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.building.vo.BuildingPageReqVO;
import cn.aguyao.module.charging.controller.admin.building.vo.BuildingRespVO;
import cn.aguyao.module.charging.controller.admin.building.vo.BuildingSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.building.BuildingDO;
import cn.aguyao.module.charging.service.building.BuildingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 楼栋")
@RestController
@RequestMapping("/charging/building")
@Validated
public class BuildingController {

    @Resource
    private BuildingService buildingService;

    @PostMapping("/create")
    @Operation(summary = "创建楼栋")
    @PreAuthorize("@ss.hasPermission('charging:building:create')")
    public CommonResult<Long> createBuilding(@Valid @RequestBody BuildingSaveReqVO createReqVO) {
        return success(buildingService.createBuilding(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新楼栋")
    @PreAuthorize("@ss.hasPermission('charging:building:update')")
    public CommonResult<Boolean> updateBuilding(@Valid @RequestBody BuildingSaveReqVO updateReqVO) {
        buildingService.updateBuilding(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除楼栋")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:building:delete')")
    public CommonResult<Boolean> deleteBuilding(@RequestParam("id") Long id) {
        buildingService.deleteBuilding(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得楼栋")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:building:query')")
    public CommonResult<BuildingRespVO> getBuilding(@RequestParam("id") Long id) {
        BuildingDO building = buildingService.getBuilding(id);
        return success(BeanUtils.toBean(building, BuildingRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得楼栋分页")
    @PreAuthorize("@ss.hasPermission('charging:building:query')")
    public CommonResult<PageResult<BuildingRespVO>> getBuildingPage(@Valid BuildingPageReqVO pageReqVO) {
        PageResult<BuildingDO> pageResult = buildingService.getBuildingPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BuildingRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出楼栋 Excel")
    @PreAuthorize("@ss.hasPermission('charging:building:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBuildingExcel(@Valid BuildingPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BuildingDO> list = buildingService.getBuildingPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "楼栋.xls", "数据", BuildingRespVO.class,
                        BeanUtils.toBean(list, BuildingRespVO.class));
    }

    /**
     * 根据小区id，获取楼栋列表
     * @param communityId
     * @return
     */
    @GetMapping("/list")
    @Operation(summary = "根据小区id，获取楼栋列表")
    @Parameter(name = "communityId", description = "小区id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:building:query')")
    public CommonResult<List<BuildingRespVO>> list(@RequestParam("communityId") Long communityId) {
        List<BuildingDO> list = buildingService.getBuildingList(communityId);
        return success(BeanUtils.toBean(list, BuildingRespVO.class));
    }

}
