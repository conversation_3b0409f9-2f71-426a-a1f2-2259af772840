package cn.aguyao.module.charging.controller.admin.community.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 小区 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CommunityRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1376")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "小区名称", example = "赵六")
    @ExcelProperty("小区名称")
    private String name;

    @Schema(description = "楼栋数")
    @ExcelProperty("楼栋数")
    private Integer buildingsNum;

    @Schema(description = "设备数")
    @ExcelProperty("设备数")
    private Integer deviceNum;

    @Schema(description = "用户数，会员数")
    @ExcelProperty("用户数，会员数")
    private Integer mbeNum;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "经度")
    @ExcelProperty("经度")
    private String longitude;

    @Schema(description = "纬度")
    @ExcelProperty("纬度")
    private String latitude;

    @Schema(description = "合作方id")
    @ExcelProperty("合作方id")
    private Long partnerId;

    @Schema(description = "合作方")
    @ExcelProperty("合作方")
    private String partnerName;

    @Schema(description = "地址")
    @ExcelProperty("地址")
    private String address;

    @Schema(description = "地址")
    @ExcelProperty("地址")
    private BigDecimal profitSharingPoints;

//
//    @Schema(description = "方案id")
//    @ExcelProperty("方案id")
//    private String schemeId;
//
//    @Schema(description = "方案名称")
//    @ExcelProperty("方案名称")
//    private String schemeName;
}
