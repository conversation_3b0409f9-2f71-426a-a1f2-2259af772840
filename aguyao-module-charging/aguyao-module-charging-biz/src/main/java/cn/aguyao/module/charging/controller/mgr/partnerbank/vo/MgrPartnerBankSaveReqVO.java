package cn.aguyao.module.charging.controller.mgr.partnerbank.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理端小程序 - 伙伴银行新增/修改 Request VO")
@Data
public class MgrPartnerBankSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16954")
    private Long id;

    @Schema(description = "合作伙伴id", example = "22868")
    private Long partnerId;

    @Schema(description = "银行卡号")
    private String cardNo;

    @Schema(description = "开户行")
    private String bankDeposit;

    @Schema(description = "银行名称", example = "张三")
    private String bankName;


    @Schema(description = "真实姓名", example = "张三")
    private String realName;

}
