package cn.aguyao.module.charging.controller.mgr.withdrawalrecord.vo;

import cn.aguyao.framework.desensitize.core.slider.annotation.BankCardDesensitize;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理端小程序 - 提现记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MgrWithdrawalRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "32603")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "主管id")
    private Long partnerId;

    @Schema(description = "主管手机")
    @ExcelProperty("主管手机")
    private String partnerMobile;

    @Schema(description = "申请时间")
    @ExcelProperty("申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "申请金额")
    @ExcelProperty("申请金额")
    private BigDecimal applyAmount;

    @Schema(description = "提现银行名称", example = "芋艿")
    @ExcelProperty("提现银行名称")
    private String bankName;

    @Schema(description = "银行卡号")
    @ExcelProperty("银行卡号")
    @BankCardDesensitize(prefixKeep = 4, suffixKeep = 3, replacer = "*")
    private String bankCardNum;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "申请状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("申请状态")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
