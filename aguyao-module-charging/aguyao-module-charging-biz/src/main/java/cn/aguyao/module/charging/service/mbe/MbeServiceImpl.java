package cn.aguyao.module.charging.service.mbe;

import cn.aguyao.framework.common.enums.CommonStatusEnum;
import cn.aguyao.framework.common.enums.UserTypeEnum;
import cn.aguyao.framework.common.exception.ServiceException;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.mbe.vo.MbePageReqVO;
import cn.aguyao.module.charging.controller.admin.mbe.vo.MbeSaveReqVO;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordSaveReqVO;
import cn.aguyao.module.charging.controller.mgr.mbe.vo.MgrCommunitySimpleVO;
import cn.aguyao.module.charging.controller.mgr.mbe.vo.MgrMbeCountRespVO;
import cn.aguyao.module.charging.controller.mgr.mbe.vo.MgrMbeDetailRespVO;
import cn.aguyao.module.charging.controller.mgr.mbe.vo.MgrMbePageReqVO;
import cn.aguyao.module.charging.dal.dataobject.community.CommunityDO;
import cn.aguyao.module.charging.dal.dataobject.membermonthly.MemberMonthlyDO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgconfig.MonthlyPkgConfigDO;
import cn.aguyao.module.charging.dal.dataobject.partner.PartnerDO;
import cn.aguyao.module.charging.dal.dataobject.rechargediscountconfig.RechargeDiscountConfigDO;
import cn.aguyao.module.charging.dal.dataobject.rechargerecord.RechargeRecordDO;
import cn.aguyao.module.charging.dal.mysql.mbe.MbeMapper;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.enums.*;
import cn.aguyao.module.charging.service.community.CommunityService;
import cn.aguyao.module.charging.service.membermonthly.MemberMonthlyService;
import cn.aguyao.module.charging.service.monthlypkgconfig.MonthlyPkgConfigService;
import cn.aguyao.module.charging.service.monthlypkgrecord.MonthlyPkgRecordService;
import cn.aguyao.module.charging.service.partner.PartnerService;
import cn.aguyao.module.charging.service.rechargediscountconfig.RechargeDiscountConfigService;
import cn.aguyao.module.charging.service.rechargerecord.RechargeRecordService;
import cn.aguyao.module.charging.service.refundrecord.RefundRecordService;
import cn.aguyao.module.pay.api.app.PayAppApi;
import cn.aguyao.module.pay.api.app.dto.PayAppRespDTO;
import cn.aguyao.module.pay.api.order.PayOrderApi;
import cn.aguyao.module.pay.api.refund.PayRefundApi;
import cn.aguyao.module.pay.api.refund.dto.PayRefundCreateReqDTO;
import cn.aguyao.module.system.api.serial.SerialApi;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.*;

/**
 * 会员，即充电的用户 Service 实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Validated
public class MbeServiceImpl implements MbeService {

    @Resource
    private MbeMapper memberMapper;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private CommunityService communityService;

    @Resource
    private SerialApi serialApi;

    @Resource
    private RefundRecordService refundRecordService;

    @Resource
    private MonthlyPkgRecordService monthlyPkgRecordService;

    @Resource
    private MonthlyPkgConfigService monthlyPkgConfigService;

    @Resource
    private RechargeDiscountConfigService rechargeDiscountConfigService;


    @Resource
    private PartnerService partnerService;

    @Lazy
    @Resource
    private RechargeRecordService rechargeRecordService;

    @Resource
    private PayOrderApi payOrderApi;

    @Resource
    private PayAppApi payAppApi;


    @Resource
    private PayRefundApi payRefundApi;

    @Resource
    private MemberMonthlyService memberMonthlyService;



    @Override
    public Long createMember(MbeSaveReqVO createReqVO) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_HY);

        // 插入
        MbeDO member = BeanUtils.toBean(createReqVO, MbeDO.class);
        member.setCode(code);
        memberMapper.insert(member);
        // 返回
        return member.getId();
    }

    @Override
    public void updateMember(MbeSaveReqVO updateReqVO) {
        // 校验存在
        validateMemberExists(updateReqVO.getId());
        // 更新
        MbeDO updateObj = BeanUtils.toBean(updateReqVO, MbeDO.class);
        memberMapper.updateById(updateObj);
    }

    @Override
    public void updateMember(MbeDO mbeDO) {
        mbeDO.setUpdateTime(LocalDateTime.now());
        memberMapper.updateById(mbeDO);
    }

    @Override
    public void deleteMember(Long id) {
        // 校验存在
        validateMemberExists(id);
        // 删除
        memberMapper.deleteById(id);
    }

    private void validateMemberExists(Long id) {
        if (memberMapper.selectById(id) == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }
    }

    @Override
    public MbeDO getMember(Long id) {
        MbeDO mbeDo = memberMapper.selectById(id);

        if (Objects.isNull(mbeDo)) {
            return null;
        }

        if (Objects.nonNull(mbeDo.getCurrentCommunityId())) {
            CommunityDO communityDO = communityService.getCommunity(mbeDo.getCurrentCommunityId());
            if (Objects.nonNull(communityDO)) {
                mbeDo.setBelongCommunityName(communityDO.getName());
            }
        }
        if (Objects.nonNull(mbeDo.getMonthlyPassCommunityId())) {
            CommunityDO communityDO = communityService.getCommunity(mbeDo.getMonthlyPassCommunityId());
            if (Objects.nonNull(communityDO)) {
                mbeDo.setMonthlyPassCommunityName(communityDO.getName());
            }
        }

        return mbeDo;
    }

    @Override
    public PageResult<MbeDO> getMemberPage(MbePageReqVO pageReqVO) {
        Page<MbeDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        IPage<MbeDO> ipage = memberMapper.selectPage(page, pageReqVO);
        return new PageResult<>(ipage.getRecords(), ipage.getTotal());
    }

    @Override
    public MbeDO getUserByMobile(String mobile) {
        return memberMapper.getUserByMobile(mobile);
    }

    @Override
    public int updateUserLogin(Long userId, String clientIP) {
        return memberMapper.updateUserLogin(userId, clientIP);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    public MbeDO createUserIfAbsent(String mobile, String registerIp, Integer terminal) {
        // 用户已经存在
        MbeDO user = memberMapper.selectByMobile(mobile);
        if (user != null) {
            return user;
        }
        // 用户不存在，则进行创建
        return createUser(mobile, null, null, registerIp, terminal);
    }

    @Override
    public CommonResult<String> refundConfirm(Long mpId, String reason) {
        // 余额扣减
        MbeDO member = this.selectMemberByMpUserId(mpId);
        if (Objects.isNull(member)) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        // 改成冻结状态，并扣减余额 todo todo
        BigDecimal applyAmount = member.getRechargeBalance();

        // 如果余额小于等于0，则不扣减
        if (applyAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("-------没有余额可以扣减------------mpId：{}", mpId);
            return CommonResult.error(500, "没有可退的余额");
        }

        // 查询该用户的所有充值记录
        List<RechargeRecordDO> list = rechargeRecordService.findByMpId(mpId);
        if (CollUtil.isEmpty(list)) {
            log.info("--------------没有可退的订单-----------mpId: {}", mpId);
            return CommonResult.error(500, "没有可退的订单");
        }

        BigDecimal tmpApplyAmount = applyAmount;
        // 遍历充值记录，分单退款
        for (RechargeRecordDO recordDO : list) {
            // 支付的金额
            BigDecimal payAmt = recordDO.getAmount();
            // 如果申请的金额小于等于支付的金额，则全额退款
            if (tmpApplyAmount.compareTo(payAmt) <= 0) {
                createRefundRecord(member, recordDO.getCode(), tmpApplyAmount, reason);
                tmpApplyAmount = BigDecimal.ZERO;
                // 结束循环
                break;
            } else {
                // 如果申请的金额大于支付的金额，则部分退款
                createRefundRecord(member, recordDO.getCode(), payAmt, reason);
                tmpApplyAmount = tmpApplyAmount.subtract(payAmt);
            }
        }

        // 更新用户余额为零， 这里正常赋值0就可以了，但是这里为了严谨，还是减去剩余的金额， 存在可用订单不够的情况。
        member.setRechargeBalance(tmpApplyAmount);
        memberMapper.updateById(member);
        return CommonResult.success(null, "提交成功");
    }

    /**
     * 创建退款记录
     * @param member
     * @param refundAmount 退款金额
     * @param reason
     */
    private void createRefundRecord(MbeDO member, String refundCode, BigDecimal refundAmount, String reason) {
        // 新建一条扣减记录
        String code = serialApi.getCode(PrefixConstants.PREFIX_TK);
        RefundRecordSaveReqVO req = new RefundRecordSaveReqVO();
        req.setCode(code);
        req.setReason(reason);
        req.setOrderNo(refundCode);
        req.setMpId(member.getMpUserId());
        req.setMobile(member.getMobile());
        req.setMbeCode(member.getCode());
        req.setApplyAmount(refundAmount);
        req.setRefundSuccessAmount(BigDecimal.ZERO);
        req.setType(RefundEnum.RefundTypeEnum.REFUND_TYPE_0.getValue());
        req.setStatus(RefundEnum.RefundStatusEnum.REFUND_STATUS_0.getValue());
        req.setApplyStatus(ApplyStatusEnum.RefundStatusEnum.APPLYING_0.getValue());
        req.setApprovalStatus(ApprovalStatusEnum.RefundStatusEnum.APPLYING_0.getValue());

        refundRecordService.createRefundRecord(req);

        // 退款
        log.info("------------------账户余额退款------------------refundAmount: {}", refundAmount);
        PayAppRespDTO payApp = payAppApi.getByName(PayAppConst.XMYSJ);
        PayRefundCreateReqDTO dto = new PayRefundCreateReqDTO();
        dto.setAppId(payApp.getId());
        dto.setReason("账户余额退款");
        dto.setUserIp(getClientIP());
        dto.setMerchantOrderId(refundCode);
        dto.setPrice(refundAmount.multiply(new BigDecimal(100)).intValue());

//        String tkCode = "TK"+System.currentTimeMillis();
        dto.setMerchantRefundId(code);

        log.info("账户余额，开始执行退款业务，参数：{}", JSON.toJSONString(dto));
        Long id = payRefundApi.createRefund(dto);
        log.info("账户余额，退款成功，退款单号：{}, 退款业务Id：{}", code, id);
    }

    @Override
    public Map<String, Object> getMemberInfo(Long id) {
        MbeDO member = this.getMember(id);
        if (Objects.isNull(member)) {
            throw exception(MEMBER_NOT_EXISTS);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("code", member.getCode());
        map.put("balance", member.getRechargeBalance());
        map.put("giftBalance", member.getGiftBalance());

        Long communityId = member.getCurrentCommunityId() != null ? member.getCurrentCommunityId() : member.getBelongCommunityId();
        if (Objects.nonNull(communityId)) {
            MemberMonthlyDO memberMonthlyDO = memberMonthlyService.findByMpIdAndCommunityId(member.getMpUserId(), communityId);
            if (Objects.nonNull(memberMonthlyDO)) {
                String date = DateUtil.format(memberMonthlyDO.getExpiration(), DatePattern.NORM_DATE_PATTERN);
                map.put("monthlyPassDuration", date);      // 包月时长
            } else {
                map.put("monthlyPassDuration", "");
            }
        }  else {
            map.put("monthlyPassDuration", "");
        }

        // 当前小区信息
        CommunityDO communityDO = null;
        if (Objects.nonNull(member.getCurrentCommunityId())) {
            communityDO = communityService.getCommunity(member.getCurrentCommunityId());
        } else if (Objects.nonNull(member.getBelongCommunityId())) {
            communityDO = communityService.getCommunity(member.getBelongCommunityId());
            if (Objects.nonNull(communityDO)) {
                member.setCurrentCommunityId(communityDO.getId());
                this.updateMember(member);
            }
        }

        if (Objects.nonNull(communityDO)) {
            map.put("communityId", communityDO.getId());
            map.put("communityName", communityDO.getName());
        }

        return map;
    }



    private MbeDO createUser(String mobile, String nickname, String avtar,
                                    String registerIp, Integer terminal) {
        // 生成密码
        String password = IdUtil.fastSimpleUUID();
        // 插入用户
        MbeDO user = new MbeDO();
        user.setMobile(mobile);
        user.setStatus(CommonStatusEnum.ENABLE.getStatus()); // 默认开启
        user.setPassword(encodePassword(password)); // 加密密码
        user.setRegisterIp(registerIp).setRegisterTerminal(terminal);
        user.setNickname(nickname).setAvatar(avtar); // 基础信息
        if (StrUtil.isEmpty(nickname)) {
            // 昵称为空时，随机一个名字，避免一些依赖 nickname 的逻辑报错，或者有点丑。例如说，短信发送有昵称时~
            user.setNickname("用户" + RandomUtil.randomNumbers(6));
        }
        memberMapper.insert(user);

        // 发送 MQ 消息：用户创建 todo
//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//
//            @Override
//            public void afterCommit() {
//                memberUserProducer.sendUserCreateMessage(user.getId());
//            }
//
//        });
        return user;
    }

    /**
     * 对密码进行加密
     *
     * @param password 密码
     * @return 加密后的密码
     */
    private String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }






    /////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////// 管理端 ////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////
    /**
     * 统计信息
     * @param communityName 小区名称
     * @return
     */
    @Override
    public MgrMbeCountRespVO statisticsInfo(String communityName) {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        PartnerDO partner = this.partnerService.getPartnerByMpId(mpId);

        if (Objects.isNull(partner)) {
            throw exception(PARTNER_NOT_EXISTS);
        }

//        List<MgrCommunitySimpleVO> communityList = memberMapper.statisticsInfo(partner.getId(), communityName);
        MgrMbeCountRespVO userCountAll = memberMapper.statisticsInfoAll(partner.getId(), communityName);
//        if (Objects.nonNull(userCountAll)) {
//            userCountAll.setCommunityUserList(communityList);
//        }
        return userCountAll;
    }

    /**
     * 统计详情
     * @return
     */
    @Override
    public PageResult<MgrMbeDetailRespVO> statisticsDetail(MgrMbePageReqVO pageReqVO) {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();

        // 校验商户是否存在 todo todo
        PartnerDO partner = partnerService.getPartnerByMpId(mpId);
        if (Objects.isNull(partner)) {
            throw exception(PARTNER_NOT_EXISTS);
        }

        Page<?> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        IPage<MgrMbeDetailRespVO> iPage = memberMapper.statisticsDetail(page, pageReqVO);

        iPage.getRecords().forEach(item -> {
            if (Objects.nonNull(item.getMobile())) {
                item.setMobile(StrUtil.hide(item.getMobile(), 3, 7));
            }
            if (Objects.nonNull(item.getCreateTime())) {
                item.setSubscribeTime(DateUtil.format(item.getCreateTime(), DatePattern.NORM_DATE_PATTERN));
            }
            if (Objects.isNull(item.getRechargeBalance())) {
                item.setRechargeBalance(BigDecimal.ZERO);
            }
            if (Objects.isNull(item.getGiftBalance())) {
                item.setGiftBalance(BigDecimal.ZERO);
            }
        });

        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }

    @Override
    public void createMbeIfAbsent(Long mpUserId, String mobile, UserTypeEnum member) {

        MbeDO entity = memberMapper.selectOne(MbeDO::getMobile, mobile);

        if (Objects.nonNull(entity) && Objects.isNull(entity.getMpUserId())) {
            // 用户已存在，但未绑定微信
            log.info("用户已存在，但未绑定微信，更新绑定微信用户");
            entity.setMpUserId(mpUserId);
            memberMapper.updateById(entity);
            return;
        } else if (Objects.nonNull(entity) && Objects.equals(entity.getMpUserId(), mpUserId)) {
            log.info("用户已存在，并且已绑定当前微信用户");
            return; // 用户已存在
        } else if (Objects.nonNull(entity) && !Objects.equals(entity.getMpUserId(), mpUserId)) {
            String msg = "小程序已绑定其他手机号，请联系客服修改";
            log.info(msg);
            throw new ServiceException(1, msg);
        } else {
            MbeDO entity2 = memberMapper.selectOne(MbeDO::getMpUserId, mpUserId);
            if (Objects.nonNull(entity2)) {
                String msg = "小程序已绑定其他手机号，请联系客服修改.";
                log.info(msg);
                throw new ServiceException(1, msg);
            }
            // 用户不存在，创建新用户
            log.info("用户不存在，创建新用户");
            MbeDO mbeDO = new MbeDO();
            mbeDO.setMobile(mobile);
            mbeDO.setMpUserId(mpUserId);
            mbeDO.setGiftBalance(BigDecimal.ZERO);
            mbeDO.setRechargeBalance(BigDecimal.ZERO);

            String code = serialApi.getCode(PrefixConstants.PREFIX_HY);
            mbeDO.setCode(code);
            memberMapper.insert(mbeDO);
        }
    }

    @Override
    public MbeDO selectMemberByMpUserId(Long mpUserId) {
        return memberMapper.selectOne(MbeDO::getMpUserId, mpUserId);
    }

    /**
     * 充值 更新余额
     * @param mpId
     * @param proId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMbeBalance(Long mpId, Long proId) {
        log.info("----------进入更新会员余额操作");
        RechargeDiscountConfigDO config = rechargeDiscountConfigService.getRechargeDiscountConfig(proId);
        log.info("--------------产品配置： {}", JSON.toJSONString(config));
        MbeDO mbe = this.selectMemberByMpUserId(mpId);

        if (Objects.isNull(mbe)) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        if (Objects.isNull(config)) {
            throw exception(RECHARGE_DISCOUNT_CONFIG_NOT_EXISTS);
        }

        log.info("----------更新余额前的会员数据：{}", JSON.toJSONString(mbe));
        mbe.setUpdateTime(LocalDateTime.now());

        // 赠送余额
        BigDecimal giftAmountMbe = Objects.isNull(mbe.getGiftBalance()) ? BigDecimal.ZERO : mbe.getGiftBalance();
        BigDecimal giftAmountConfig = Objects.isNull(config.getGiftAmount()) ? BigDecimal.ZERO : config.getGiftAmount();
        BigDecimal giftAmount = giftAmountMbe.add(giftAmountConfig);
        mbe.setGiftBalance(giftAmount);

        BigDecimal amountMbe = Objects.isNull(mbe.getRechargeBalance()) ? BigDecimal.ZERO : mbe.getRechargeBalance();
        BigDecimal aConfig = Objects.isNull(config.getAmount()) ? BigDecimal.ZERO : config.getAmount();
        BigDecimal amount = amountMbe.add(aConfig);
        mbe.setRechargeBalance(amount);

        log.info("----------(待更新)更新余额后的会员数据：{}", JSON.toJSONString(mbe));
        memberMapper.updateById(mbe);

        log.info("------------------更新成功后的数据， mbe = {}", JSON.toJSONString(mbe));
    }


    @Override
    public void updateMbeMonthlyPkg(Long mpId, Long proId) {
        MonthlyPkgConfigDO config = monthlyPkgConfigService.getMonthlyPkgConfig(proId);
        MbeDO mbe = this.selectMemberByMpUserId(mpId);

        if (Objects.isNull(mbe)) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        if (Objects.isNull(config)) {
            throw exception(MONTHLY_PKG_CONFIG_NOT_EXISTS);
        }

        // 自动延期就好了
        Integer num = config.getMonthNum();

        // local date time 转 date
        Date date = null;
        // 如果会员中的过期时间为空或者已经过期了，则从当前时间开始计算过期时间
        if (Objects.isNull(mbe.getMonthlyPassDuration()) || mbe.getMonthlyPassDuration().isBefore(LocalDateTime.now())) {
            date = DateUtil.date();
        } else {
            date = DateUtils.localDateTime2Date(mbe.getMonthlyPassDuration());
        }

        // 新的过期时间
        Date newPassDuration = DateUtils.addMonths(date, num);
        LocalDateTime dateTime = DateUtil.toLocalDateTime(newPassDuration);
        mbe.setMonthlyPassDuration(dateTime);
        mbe.setUpdateTime(LocalDateTime.now());

        memberMapper.updateById(mbe);
    }

    @Override
    public void refundHandleBalance(Long mpId, Integer refundPrice) {
        MbeDO mbeDO = this.selectMemberByMpUserId(mpId);
        if (Objects.isNull(mbeDO)) {
            return ;
        }

        if (mbeDO.getRechargeBalance().compareTo(BigDecimal.ZERO) <= 0) {
            // 充值金额为0，不处理
            log.info("-------------充值金额为0，不处理-------------------");
            return ;
        }

        BigDecimal refundAmt = new BigDecimal(refundPrice).divide(new BigDecimal(100));
        if (mbeDO.getRechargeBalance().compareTo(refundAmt) <= 0) {
            mbeDO.setRechargeBalance(BigDecimal.ZERO);
        } else {
            mbeDO.setRechargeBalance(mbeDO.getRechargeBalance().subtract(refundAmt));
        }
        // 赠送金额是否退回，暂时不退了
        memberMapper.updateById(mbeDO);
    }

    /**
     * 更新用户所属小区
     * @param id    用户id
     * @param communityId    小区id
     */
    @Override
    public void updateBelongCommunityId(Long id, Long communityId) {
        MbeDO member = this.getMember(id);
        if (Objects.isNull(member)) {
            log.error("----------------------id为：{}的会员信息不存在", id);
            return ;
        }
        member.setBelongCommunityId(communityId);
        if (Objects.isNull(member.getCurrentCommunityId())) {
            // 首次绑定小区
            member.setCurrentCommunityId(communityId);
        }
        memberMapper.updateById(member);
    }

    /**
     * 更新用户选择的小区
     * @param id    用户id
     * @param communityId    小区id
     */
    @Override
    public void updateCurrentCommunityId(Long id, Long communityId) {
        MbeDO member = this.getMember(id);
        if (Objects.isNull(member)) {
            log.error("----------------------id为：{}的会员信息不存在", id);
            return ;
        }
        member.setCurrentCommunityId(communityId);
        if (Objects.isNull(member.getBelongCommunityId())) {
            // 首次绑定小区
            member.setBelongCommunityId(communityId);
        }
        memberMapper.updateById(member);
    }

    @Override
    public Boolean ispartner() {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        PartnerDO partner = partnerService.getPartnerByMpId(mpId);
        if (Objects.nonNull(partner)) {
            if (Objects.equals(0, partner.getStatus())) {
                // 状态正常
                return true;
            } else {
                // 状态异常
                log.info("--------------------合作已失效，无权访问--------------");
            }
        }
        return false;
    }

    @Override
    public PageResult<MgrCommunitySimpleVO> statisticsList(MgrMbePageReqVO pageReqVO) {
        Page<?> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        IPage<MgrCommunitySimpleVO> iPage = memberMapper.statisticsInfo(page, pageReqVO);

        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }

    @Override
    public Long getMbeCountByCommunityId(Long id) {
        return memberMapper.getMbeCountByCommunityId(id);
    }

    /**
     * 根据mpId 和 小区id判断是否有效包月
     * @param mpId
     * @param communityId
     * @return
     */
    @Override
    public Boolean isValidMonthly(Long mpId, Long communityId) {
        log.info("-----------------------mpId:{}, communityId:{} ", mpId, communityId);
        MemberMonthlyDO memberMonthlyDO = memberMonthlyService.findByMpIdAndCommunityId(mpId, communityId);
        // 当前用户、小区有包月记录， 并且未过期，则有效包月
        if (Objects.nonNull(memberMonthlyDO)
                && Objects.nonNull(memberMonthlyDO.getExpiration())
                && memberMonthlyDO.getExpiration().isAfter(LocalDateTime.now())) {
            log.info("--------------------mpId:{}, 有效期包月true", mpId);
            return true;
        }
        return false;
    }
}
