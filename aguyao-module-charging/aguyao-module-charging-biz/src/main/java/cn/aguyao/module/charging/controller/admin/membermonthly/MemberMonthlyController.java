package cn.aguyao.module.charging.controller.admin.membermonthly;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.membermonthly.vo.MemberMonthlyPageReqVO;
import cn.aguyao.module.charging.controller.admin.membermonthly.vo.MemberMonthlyRespVO;
import cn.aguyao.module.charging.controller.admin.membermonthly.vo.MemberMonthlySaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.membermonthly.MemberMonthlyDO;
import cn.aguyao.module.charging.service.membermonthly.MemberMonthlyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 用户和小区包月的关系")
@RestController
@RequestMapping("/charging/member-monthly")
@Validated
public class MemberMonthlyController {

    @Resource
    private MemberMonthlyService memberMonthlyService;

    @PostMapping("/create")
    @Operation(summary = "创建用户和小区包月的关系")
    @PreAuthorize("@ss.hasPermission('charging:member-monthly:create')")
    public CommonResult<Long> createMemberMonthly(@Valid @RequestBody MemberMonthlySaveReqVO createReqVO) {
        return success(memberMonthlyService.createMemberMonthly(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户和小区包月的关系")
    @PreAuthorize("@ss.hasPermission('charging:member-monthly:update')")
    public CommonResult<Boolean> updateMemberMonthly(@Valid @RequestBody MemberMonthlySaveReqVO updateReqVO) {
        memberMonthlyService.updateMemberMonthly(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户和小区包月的关系")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:member-monthly:delete')")
    public CommonResult<Boolean> deleteMemberMonthly(@RequestParam("id") Long id) {
        memberMonthlyService.deleteMemberMonthly(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户和小区包月的关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:member-monthly:query')")
    public CommonResult<MemberMonthlyRespVO> getMemberMonthly(@RequestParam("id") Long id) {
        MemberMonthlyDO memberMonthly = memberMonthlyService.getMemberMonthly(id);
        return success(BeanUtils.toBean(memberMonthly, MemberMonthlyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户和小区包月的关系分页")
    @PreAuthorize("@ss.hasPermission('charging:member-monthly:query')")
    public CommonResult<PageResult<MemberMonthlyRespVO>> getMemberMonthlyPage(@Valid MemberMonthlyPageReqVO pageReqVO) {
        PageResult<MemberMonthlyDO> pageResult = memberMonthlyService.getMemberMonthlyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MemberMonthlyRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户和小区包月的关系 Excel")
    @PreAuthorize("@ss.hasPermission('charging:member-monthly:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMemberMonthlyExcel(@Valid MemberMonthlyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MemberMonthlyDO> list = memberMonthlyService.getMemberMonthlyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户和小区包月的关系.xls", "数据", MemberMonthlyRespVO.class,
                        BeanUtils.toBean(list, MemberMonthlyRespVO.class));
    }

}