package cn.aguyao.module.charging.controller.admin.insureconfig;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.insureconfig.vo.InsureConfigPageReqVO;
import cn.aguyao.module.charging.controller.admin.insureconfig.vo.InsureConfigRespVO;
import cn.aguyao.module.charging.controller.admin.insureconfig.vo.InsureConfigSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.insureconfig.InsureConfigDO;
import cn.aguyao.module.charging.service.insureconfig.InsureConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 投保配置")
@RestController
@RequestMapping("/charging/insure-config")
@Validated
public class InsureConfigController {

    @Resource
    private InsureConfigService insureConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建投保配置")
    @PreAuthorize("@ss.hasPermission('charging:insure-config:create')")
    public CommonResult<Long> createInsureConfig(@Valid @RequestBody InsureConfigSaveReqVO createReqVO) {
        return success(insureConfigService.createInsureConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新投保配置")
    @PreAuthorize("@ss.hasPermission('charging:insure-config:update')")
    public CommonResult<Boolean> updateInsureConfig(@Valid @RequestBody InsureConfigSaveReqVO updateReqVO) {
        insureConfigService.updateInsureConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除投保配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:insure-config:delete')")
    public CommonResult<Boolean> deleteInsureConfig(@RequestParam("id") Long id) {
        insureConfigService.deleteInsureConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得投保配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:insure-config:query')")
    public CommonResult<InsureConfigRespVO> getInsureConfig(@RequestParam("id") Long id) {
        InsureConfigDO insureConfig = insureConfigService.getInsureConfig(id);
        return success(BeanUtils.toBean(insureConfig, InsureConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得投保配置分页")
    @PreAuthorize("@ss.hasPermission('charging:insure-config:query')")
    public CommonResult<PageResult<InsureConfigRespVO>> getInsureConfigPage(@Valid InsureConfigPageReqVO pageReqVO) {
        PageResult<InsureConfigDO> pageResult = insureConfigService.getInsureConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InsureConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出投保配置 Excel")
    @PreAuthorize("@ss.hasPermission('charging:insure-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInsureConfigExcel(@Valid InsureConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InsureConfigDO> list = insureConfigService.getInsureConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "投保配置.xls", "数据", InsureConfigRespVO.class,
                        BeanUtils.toBean(list, InsureConfigRespVO.class));
    }

}