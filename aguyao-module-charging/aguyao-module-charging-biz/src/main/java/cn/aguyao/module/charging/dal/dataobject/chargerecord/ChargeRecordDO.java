package cn.aguyao.module.charging.dal.dataobject.chargerecord;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充电记录，消费记录 DO
 *
 * <AUTHOR>
 */
@TableName("charging_charge_record")
@KeySequence("charging_charge_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChargeRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 编号
     */
    private String code;
    /**
     * 微信用户id
     */
    private Long mpId;
    /**
     * 用户id
     */
    private Long mbeId;
    /**
     * 用户编号
     */
    private String mbeCode;
    /**
     * 用户手机号
     */
    private String mobile;
    /**
     * 充电时间_开始充电的时间
     */
    private LocalDateTime chargeTimeStart;
    /**
     * 充电时间_结束充电的时间
     */
    private LocalDateTime chargeTimeEnd;
    /**
     * 充电小区id
     */
    private Long chargeCommunityId;
    /**
     * 楼栋id
     */
    private Long buildingsId;
//    /**
//     * 设备id
//     */
//    private Long deviceId;
    /**
     * 设备编号
     */
    private String device;
    /**
     * 消费金额, 预付金额
     */
    private BigDecimal amount;
    /**
     * 实际支付金额
     */
    private BigDecimal actualAmount;
    /**
     * 消费类型，1：单次消费（时间）；2：临时消费（金额）；
     */
    private Integer type;
    /**
     * 支付来源，消费来源
     */
    private Integer paySource;
    /**
     * 进行状态,
     * BtStat端口状态(
     * 0待机状态
     * 1端口正在输出
     * 2输出时间到，
     * 3充电完成，
     * 4插头拨出，
     * 5功率过大，
     * 6端口异常，
     * 7高温报警)
     * 枚举： DeviceBtStatEnum
     */
    private Integer proceedStatus;
    /**
     * 瓦数，消耗的瓦数
     */
    private BigDecimal wattage;
    /**
     * 千瓦·时，消耗的度数
     */
    private BigDecimal kilowatt;
    /**
     * 备注
     */
    private String remark;
    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;

    /**
     * 端口（1 & 2）
     */
    private Integer port;

    /**
     * 输出时间1000秒(最大59940秒)
     */
    private Integer time;

    /**
     * 输出功率0-1000W，0为不限制功率,充满自停。
     */
    private Integer power;

    /**
     * 自启动功能0为关闭自启动，1为打开自启动
     */
    private Integer autoOpen;

    /**
     * Energy端口累计输出电能(单位(W*MIN)瓦分钟(一分钟输出的功率) /60等于瓦时)
     */
    private String energy;
    /**
     * Energy端口累计输出电能(单位(W*MIN)瓦分钟(一分钟输出的功率) /60等于瓦时)
     */
    private String energyStart;
    /**
     * Energy端口累计输出电能(单位(W*MIN)瓦分钟(一分钟输出的功率) /60等于瓦时)
     */
    private String energyEnd;

    /**
     * 预计充电时间，单位：小时
     */
    private Integer estimatedChargingTime;

    /**
     * 实际充电时间（理论上等于预计充电时间的秒数，但充满自停除外），单位：秒
     */
    private String actualChargeTime;

    /**
     * 优惠后的时间
     */
    private String discountChargeTime;

    /**
     * 守护充电标识，0：未守护；1：已守护；
     */
    @Schema(description = "守护充电标识，0：未守护；1：已守护；")
    private Integer guardFalg;

    /**
     * 守护充电金额，单位：元
     */
    private BigDecimal guardAmt;

    /**
     * 停止充电原因
     */
    private String stopReason;

    /**
     * 最后一次上报内容
     */
    private String lastReportContent;

    /**
     * 守护充电金额，默认0.09元
     */
    public static final BigDecimal GUARD_AMT = new BigDecimal("0.09");


    /**
     * 充电状态，0:待充电； 1：充电中； 2：充电完成
     */
    public static final Integer PROCEED_STATUS_0 = 0;

    /**
     * 充电状态，1：充电中； 2：充电完成
     */
    public static final Integer PROCEED_STATUS_1 = 1;
    /**
     * 充电状态，1：充电中； 2：充电完成
     */
    public static final Integer PROCEED_STATUS_2 = 2;


//    /**
//     * 充电模式type，1：单次充电； 2：临时充电
//     */
//    public static final Integer TYPE_1 = 1;
//    /**
//     * 充电模式type，1：单次充电； 2：临时充电
//     */
//    public static final Integer TYPE_2 = 2;

    /**
     * 单笔分润金额
     */
    private BigDecimal shareBenefit;


    /**
     * 充电小区名称
     */
    @TableField(exist = false)
    private String chargeCommunityName;

    /**
     * 楼栋名称
     */
    @TableField(exist = false)
    private String buildingsName;


    /**
     * 服务费
     */
    private BigDecimal serviceAmt;

}
