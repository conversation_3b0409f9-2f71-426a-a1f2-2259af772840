package cn.aguyao.module.charging.controller.admin.partner;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.partner.vo.PartnerPageReqVO;
import cn.aguyao.module.charging.controller.admin.partner.vo.PartnerRespVO;
import cn.aguyao.module.charging.controller.admin.partner.vo.PartnerSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.partner.PartnerDO;
import cn.aguyao.module.charging.service.partner.PartnerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合作伙伴——小区管理人员（物业）")
@RestController
@RequestMapping("/charging/partner")
@Validated
public class PartnerController {

    @Resource
    private PartnerService partnerService;

    @PostMapping("/create")
    @Operation(summary = "创建合作伙伴——小区管理人员（物业）")
    @PreAuthorize("@ss.hasPermission('charging:partner:create')")
    public CommonResult<Long> createPartner(@Valid @RequestBody PartnerSaveReqVO createReqVO) {
        return success(partnerService.createPartner(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新合作伙伴——小区管理人员（物业）")
    @PreAuthorize("@ss.hasPermission('charging:partner:update')")
    public CommonResult<Boolean> updatePartner(@Valid @RequestBody PartnerSaveReqVO updateReqVO) {
        partnerService.updatePartner(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除合作伙伴——小区管理人员（物业）")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:partner:delete')")
    public CommonResult<Boolean> deletePartner(@RequestParam("id") Long id) {
        partnerService.deletePartner(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得合作伙伴——小区管理人员（物业）")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:partner:query')")
    public CommonResult<PartnerRespVO> getPartner(@RequestParam("id") Long id) {
        PartnerDO partner = partnerService.getPartner(id);
        return success(BeanUtils.toBean(partner, PartnerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得合作伙伴——小区管理人员（物业）分页")
    @PreAuthorize("@ss.hasPermission('charging:partner:query')")
    public CommonResult<PageResult<PartnerRespVO>> getPartnerPage(@Valid PartnerPageReqVO pageReqVO) {
        PageResult<PartnerDO> pageResult = partnerService.getPartnerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PartnerRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出合作伙伴——小区管理人员（物业） Excel")
    @PreAuthorize("@ss.hasPermission('charging:partner:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPartnerExcel(@Valid PartnerPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PartnerDO> list = partnerService.getPartnerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "合作伙伴.xls", "数据", PartnerRespVO.class,
                        BeanUtils.toBean(list, PartnerRespVO.class));
    }

}
