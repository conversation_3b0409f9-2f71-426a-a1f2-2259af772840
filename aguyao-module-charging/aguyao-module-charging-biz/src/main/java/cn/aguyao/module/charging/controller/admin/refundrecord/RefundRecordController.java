package cn.aguyao.module.charging.controller.admin.refundrecord;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordRespVO;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.refundrecord.RefundRecordDO;
import cn.aguyao.module.charging.service.refundrecord.RefundRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 退款记录")
@RestController
@RequestMapping("/charging/refund-record")
@Validated
public class RefundRecordController {

    @Resource
    private RefundRecordService refundRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建退款记录")
    @PreAuthorize("@ss.hasPermission('charging:refund-record:create')")
    public CommonResult<Long> createRefundRecord(@Valid @RequestBody RefundRecordSaveReqVO createReqVO) {
        return success(refundRecordService.createRefundRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新退款记录")
    @PreAuthorize("@ss.hasPermission('charging:refund-record:update')")
    public CommonResult<Boolean> updateRefundRecord(@Valid @RequestBody RefundRecordSaveReqVO updateReqVO) {
        refundRecordService.updateRefundRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除退款记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:refund-record:delete')")
    public CommonResult<Boolean> deleteRefundRecord(@RequestParam("id") Long id) {
        refundRecordService.deleteRefundRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得退款记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:refund-record:query')")
    public CommonResult<RefundRecordRespVO> getRefundRecord(@RequestParam("id") Long id) {
        RefundRecordDO refundRecord = refundRecordService.getRefundRecord(id);
        return success(BeanUtils.toBean(refundRecord, RefundRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得退款记录分页")
    @PreAuthorize("@ss.hasPermission('charging:refund-record:query')")
    public CommonResult<PageResult<RefundRecordRespVO>> getRefundRecordPage(@Valid RefundRecordPageReqVO pageReqVO) {
        PageResult<RefundRecordDO> pageResult = refundRecordService.getRefundRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RefundRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出退款记录 Excel")
    @PreAuthorize("@ss.hasPermission('charging:refund-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRefundRecordExcel(@Valid RefundRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RefundRecordDO> list = refundRecordService.getRefundRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "退款记录.xls", "数据", RefundRecordRespVO.class,
                        BeanUtils.toBean(list, RefundRecordRespVO.class));
    }


    @PutMapping("/refund")
    @Operation(summary = "重新发起退款")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:refund-record:update')")
    public CommonResult<Boolean> handleRefund(@RequestParam("id") Long id) {
        refundRecordService.handleRefund(id);
        return success(true);
    }

}
