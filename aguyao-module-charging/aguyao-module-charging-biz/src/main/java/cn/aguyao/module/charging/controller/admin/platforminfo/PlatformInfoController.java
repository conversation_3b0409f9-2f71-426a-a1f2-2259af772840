package cn.aguyao.module.charging.controller.admin.platforminfo;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.platforminfo.vo.PlatformInfoPageReqVO;
import cn.aguyao.module.charging.controller.admin.platforminfo.vo.PlatformInfoRespVO;
import cn.aguyao.module.charging.controller.admin.platforminfo.vo.PlatformInfoSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.platforminfo.PlatformInfoDO;
import cn.aguyao.module.charging.service.platforminfo.PlatformInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 平台信息")
@RestController
@RequestMapping("/charging/platform-info")
@Validated
public class PlatformInfoController {

    @Resource
    private PlatformInfoService platformInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建平台信息")
    @PreAuthorize("@ss.hasPermission('charging:platform-info:create')")
    public CommonResult<Long> createPlatformInfo(@Valid @RequestBody PlatformInfoSaveReqVO createReqVO) {
        return success(platformInfoService.createPlatformInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新平台信息")
    @PreAuthorize("@ss.hasPermission('charging:platform-info:update')")
    public CommonResult<Boolean> updatePlatformInfo(@Valid @RequestBody PlatformInfoSaveReqVO updateReqVO) {
        platformInfoService.updatePlatformInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除平台信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:platform-info:delete')")
    public CommonResult<Boolean> deletePlatformInfo(@RequestParam("id") Long id) {
        platformInfoService.deletePlatformInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得平台信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:platform-info:query')")
    public CommonResult<PlatformInfoRespVO> getPlatformInfo(@RequestParam("id") Long id) {
        PlatformInfoDO platformInfo = platformInfoService.getPlatformInfo(id);
        return success(BeanUtils.toBean(platformInfo, PlatformInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得平台信息分页")
    @PreAuthorize("@ss.hasPermission('charging:platform-info:query')")
    public CommonResult<PageResult<PlatformInfoRespVO>> getPlatformInfoPage(@Valid PlatformInfoPageReqVO pageReqVO) {
        PageResult<PlatformInfoDO> pageResult = platformInfoService.getPlatformInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PlatformInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出平台信息 Excel")
    @PreAuthorize("@ss.hasPermission('charging:platform-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPlatformInfoExcel(@Valid PlatformInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PlatformInfoDO> list = platformInfoService.getPlatformInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "平台信息.xls", "数据", PlatformInfoRespVO.class,
                        BeanUtils.toBean(list, PlatformInfoRespVO.class));
    }

}