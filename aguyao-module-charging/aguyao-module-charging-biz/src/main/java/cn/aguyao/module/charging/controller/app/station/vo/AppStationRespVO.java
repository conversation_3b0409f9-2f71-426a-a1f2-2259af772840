package cn.aguyao.module.charging.controller.app.station.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 APP - 电站 Response VO")
@Data
public class AppStationRespVO {


    /**
     * 距离
     */
    private Long distance;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 小区id
     */
    private Long id;

    /**
     * 小区名称
     */
    private String name ;

    /**
     * 小区地址
     */
    private String address;

    /**
     * 设备总数
     */
    private Integer totalDevice;

    /**
     * 空闲设备数
     */
    private Integer idleDevice;

     /**
     * 已使用设备数
     */
    private Integer useDevice;

}
