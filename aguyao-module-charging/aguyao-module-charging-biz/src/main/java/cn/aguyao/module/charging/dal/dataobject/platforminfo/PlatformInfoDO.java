package cn.aguyao.module.charging.dal.dataobject.platforminfo;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 平台信息 DO
 *
 * <AUTHOR>
 */
@TableName("charging_platform_info")
@KeySequence("charging_platform_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformInfoDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 平台编码
     */
    private String code;
    /**
     * 公司名称
     */
    private String name;
    /**
     * 客服热线
     */
    private String customerServicePhone;
    /**
     * 备注
     */
    private String remark;
    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;

}
