package cn.aguyao.module.charging.service.chargerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordDetailRespVO;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordRespVO;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordSaveReqVO;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordSaveReqVO;
import cn.aguyao.module.charging.controller.app.chargerecord.vo.AppChargeRecordRespVO;
import cn.aguyao.module.charging.controller.mgr.chargerecord.vo.MgrChargeRecordRespVO;
import cn.aguyao.module.charging.dal.dataobject.building.BuildingDO;
import cn.aguyao.module.charging.dal.dataobject.chargerecord.ChargeRecordDO;
import cn.aguyao.module.charging.dal.dataobject.community.CommunityDO;
import cn.aguyao.module.charging.dal.dataobject.device.DeviceDO;
import cn.aguyao.module.charging.dal.dataobject.incomerecord.IncomeRecordDO;
import cn.aguyao.module.charging.dal.dataobject.partner.PartnerDO;
import cn.aguyao.module.charging.dal.dataobject.rechargerecord.RechargeRecordDO;
import cn.aguyao.module.charging.dal.dataobject.scheme.SchemeDO;
import cn.aguyao.module.charging.dal.dataobject.schemepower.SchemePowerDO;
import cn.aguyao.module.charging.dal.dataobject.schemetime.SchemeTimeDO;
import cn.aguyao.module.charging.dal.mysql.chargerecord.ChargeRecordMapper;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.enums.*;
import cn.aguyao.module.charging.service.MqttService;
import cn.aguyao.module.charging.service.RedisDistributedLock;
import cn.aguyao.module.charging.service.RedisService;
import cn.aguyao.module.charging.service.building.BuildingService;
import cn.aguyao.module.charging.service.community.CommunityService;
import cn.aguyao.module.charging.service.device.DeviceService;
import cn.aguyao.module.charging.service.incomerecord.IncomeRecordService;
import cn.aguyao.module.charging.service.mbe.MbeService;
import cn.aguyao.module.charging.service.monthlypkgrecord.MonthlyPkgRecordService;
import cn.aguyao.module.charging.service.partner.PartnerService;
import cn.aguyao.module.charging.service.rechargerecord.RechargeRecordService;
import cn.aguyao.module.charging.service.refundrecord.RefundRecordService;
import cn.aguyao.module.charging.service.scheme.SchemeService;
import cn.aguyao.module.charging.service.schemepower.SchemePowerService;
import cn.aguyao.module.charging.service.schemetime.SchemeTimeService;
import cn.aguyao.module.infra.api.config.ConfigApi;
import cn.aguyao.module.infra.api.config.dto.ConfigRespDTO;
import cn.aguyao.module.pay.api.app.PayAppApi;
import cn.aguyao.module.pay.api.app.dto.PayAppRespDTO;
import cn.aguyao.module.pay.api.notify.PayNotifyApi;
import cn.aguyao.module.pay.api.notify.dto.PayRefundNotifyRespDTO;
import cn.aguyao.module.pay.api.order.PayOrderApi;
import cn.aguyao.module.pay.api.order.dto.PayOrderNotifyRespDTO;
import cn.aguyao.module.pay.api.refund.PayRefundApi;
import cn.aguyao.module.pay.api.refund.dto.PayRefundCreateReqDTO;
import cn.aguyao.module.pay.api.refund.dto.PayRefundRespDTO;
import cn.aguyao.module.system.api.serial.SerialApi;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.log4j.Log4j2;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.CHARGE_RECORD_NOT_EXISTS;

/**
 * 充电记录，消费记录 Service 实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Validated
public class ChargeRecordServiceImpl implements ChargeRecordService {

    // 免费时长300秒，5分钟
    private static final int FREE_DURATION_SEC = 300;

    // 默认最小功率100瓦
    private static final int MIN_POWER_DEFAULT = 100;

    @Resource
    private ChargeRecordMapper chargeRecordMapper;

    @Resource
    private SerialApi serialApi;

    @Resource
    private ConfigApi configApi;

//    @Lazy
    @Resource
    private MbeService memberService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private RedisService redisService; // RedisTemplate redisTemplate;

    @Resource
    private CommunityService communityService;

    @Resource
    private SchemeService schemeService;

    @Resource
    private MqttService mqttService;

    @Resource
    private SchemePowerService schemePowerService;

    @Resource
    private SchemeTimeService schemeTimeService;

    @Resource
    private PayOrderApi payOrderApi;

    @Resource
    private PayAppApi payAppApi;

    @Resource
    private PayNotifyApi payNotifyApi;

    @Resource
    private PayRefundApi payRefundApi;

    @Resource
    private RefundRecordService refundRecordService;

    @Resource
    private BuildingService buildingService;

    @Resource
    private MonthlyPkgRecordService monthlyPkgRecordService;

    @Resource
    private RechargeRecordService rechargeRecordService;


    @Resource
    private IncomeRecordService incomeRecordService;

    @Lazy
    @Resource
    private PartnerService partnerService;



    @Override
    public Long createChargeRecord(ChargeRecordSaveReqVO createReqVO) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_CD);
        // 插入
        ChargeRecordDO chargeRecord = BeanUtils.toBean(createReqVO, ChargeRecordDO.class);
        chargeRecord.setCode(code);
        chargeRecordMapper.insert(chargeRecord);
        // 返回
        return chargeRecord.getId();
    }

    @Override
    public Long createChargeRecord(ChargeRecordDO chargeRecord) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_CD);
        chargeRecord.setCode(code);
        chargeRecordMapper.insert(chargeRecord);
        // 返回
        return chargeRecord.getId();
    }

    @Override
    public ChargeRecordDO findByRecordCode(String recordCode) {
        if (Objects.isNull(recordCode)) {
            return null;
        }
        return chargeRecordMapper.selectByRecordCode(recordCode);
    }


    @Override
    public Long createOrUpdateChargeRecord(JSONObject msgObj) throws Exception {
        String device = msgObj.getString("DEVICE");
        Integer port = msgObj.getInteger("PORT");
        // 这里的seq是透传过去的用户ID,mp_id
        String seq = msgObj.getString("SEQ");

        Map<String, Object> map = MqttService.parseSeq(seq);
        String recordCode = Objects.isNull(map.get("recordCode")) ? null : String.valueOf(map.get("recordCode"));

        // find by reocrd code
        ChargeRecordDO recordDO = this.findByRecordCode(recordCode);
        if (Objects.nonNull(recordDO)) {
            this.updateChargeRecord(msgObj);
            return recordDO.getId();
        } else {
            return this.createChargeRecord(msgObj);
        }
    }

    /**
     *
     * @param msgObj 打开端口的消息
     * {
     *  "DEVICE":"1234567890",
     *  "CMD":3,
     *  "PORT":1,
     *  "TIME":1000,
     *  "POWER":100,
     *  "AUTOOPEN":0,
     *  "SEQ":"000XXXX"
     * }
     * @return
     */
    @Override
    public Long createChargeRecord(JSONObject msgObj) throws Exception {

        String device = msgObj.getString("DEVICE");
        Integer port = msgObj.getInteger("PORT");
        // 这里的seq是透传过去的用户ID,mp_id
//        String seq = msgObj.getString("SEQ");
        Map<String, Object> map = MqttService.parseSeq(msgObj.getString("SEQ"));
        Long mpId = Long.parseLong(String.valueOf(map.get("mpId")));

        // 从指定端口获取数据
        JSONArray jsonoArray = JSONArray.parseArray(msgObj.getString("ALLDATA"));
        JSONObject jsonObject = jsonoArray.getJSONObject(port - 1);
        Integer btStat = jsonObject.getInteger("BtStat");
        Integer time = jsonObject.getInteger("OutTime");
        Integer power = jsonObject.getInteger("Power");
        String energy = jsonObject.getString("Energy");

        ChargeRecordDO chargeRecord = new ChargeRecordDO();
        chargeRecord.setDevice(device);
        chargeRecord.setPort(port);
        chargeRecord.setTime(time);
        chargeRecord.setPower(power);
        chargeRecord.setEnergy(energy);
//        chargeRecord.setAutoOpen(autoOpen);
        chargeRecord.setProceedStatus(ChargeRecordDO.PROCEED_STATUS_1);

        String code = serialApi.getCode(PrefixConstants.PREFIX_CD);
        chargeRecord.setCode(code);
        MbeDO mbeDO = memberService.selectMemberByMpUserId(mpId);
        chargeRecord.setMpId(mpId);
        chargeRecord.setMbeId(mbeDO.getId());
        chargeRecord.setMbeCode(mbeDO.getCode());
        chargeRecord.setMobile(mbeDO.getMobile());
        chargeRecord.setChargeTimeStart(LocalDateTime.now());

        // todo 默认
        chargeRecord.setPaySource(PaySourceEnum.BY_YECZ.getValue());


        DeviceDO deviceDO = deviceService.findByDevice(device);
        chargeRecord.setChargeCommunityId(deviceDO.getCommunityId());
        chargeRecord.setBuildingsId(deviceDO.getBuildingId());
        chargeRecord.setAutoOpen(DeviceAutoOpenConst.AUTO_OPEN_TRUE);    //  0为关闭自启动，1为打开自启动
        try {
            chargeRecordMapper.insert(chargeRecord);

            // 更新设备状态， 打开充电端口
            log.info("===============更新设备状态， 打开充电端口=============");
            this.saveChargingStatus2Redis(device, msgObj);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        // 返回
        return chargeRecord.getId();
    }

    @Override
    public void updateChargeRecord(ChargeRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateChargeRecordExists(updateReqVO.getId());
        // 更新
        ChargeRecordDO updateObj = BeanUtils.toBean(updateReqVO, ChargeRecordDO.class);
        chargeRecordMapper.updateById(updateObj);
    }

    @Override
    public void updateChargeRecord(JSONObject jsonObject) throws Exception {
        log.info("==================== updateChargeRecord:{}", jsonObject);

        JSONArray jsonoArray = JSONArray.parseArray(jsonObject.getString("ALLDATA"));
        // 2、主动上报； 4、关闭端口；
        Integer cmd = jsonObject.getInteger("CMD");
        String device = jsonObject.getString("DEVICE");

        Map<String, Object> map = MqttService.parseSeq(jsonObject.getString("SEQ"));
//        Long mpId = Long.parseLong(String.valueOf(map.get("mpId")));
        String recordCode = Objects.isNull(map.get("recordCode")) ? null : String.valueOf(map.get("recordCode"));

        if (Objects.equals(cmd, DeviceCmdEnum.HEARTBEAT.getValue())) {
            // 1.1、心跳
            // BtStat： 1 正在充电，2 输出时间到，3 充电完成，4 插头拨出，5 功率过大，6 端口异常，7 高温报警
            for (int i=0; i<jsonoArray.size(); i++) {
                JSONObject portObject = jsonoArray.getJSONObject(i);
                int power = portObject.getInteger("Power");
                if (power > 0) {
                    // 有电输出， 只能这么判断 todo
                    udpateChargeRecord(device, cmd, portObject.getInteger("Port"), portObject, recordCode);

                    // 更新设备状态， 打开充电端口
                    log.info("===============更新设备状态， 打开充电端口=============");
                    this.saveChargingStatus2Redis(device, jsonObject);
                }
            }
        } else if (Objects.equals(cmd, DeviceCmdEnum.OPEN_PORT.getValue())) {
            // 1.2、打开端口
            ChargeRecordDO recordDO = this.findByRecordCode(recordCode);
            JSONObject portObject = jsonoArray.getJSONObject(recordDO.getPort()-1);

            udpateChargeRecord(device, cmd, portObject.getInteger("Port"), portObject, recordCode);

            // 更新设备状态， 打开充电端口
            log.info("===============更新设备状态， 打开充电端口=============");
            this.saveChargingStatus2Redis(device, jsonObject);

        } else if (Objects.equals(cmd, DeviceCmdEnum.CLOSE_PORT.getValue()))  {
            // 1.3、关闭端口， 订单号要存在
            // SB的產家，這裡的的port從0開始，不是从1開始
            Integer port = jsonObject.getInteger("PORT");
            Integer portIndex = port - 1;
            JSONObject portObject = jsonoArray.getJSONObject(portIndex);
            udpateChargeRecord(device, DeviceCmdEnum.CLOSE_PORT.getValue(), port, portObject, recordCode);

            // 删除Redis数据
            redisService.delMaxPower(device, port);
        } else if (Objects.equals(cmd, DeviceCmdEnum.REPORT_STATUS.getValue())) {
            // 1.4、主动上报端口状态
            log.info("-------------主动上报状态， jsonoArray：{} ", jsonoArray);
            for (int i = 0; i < jsonoArray.size(); i++) {
                JSONObject portObject = jsonoArray.getJSONObject(i);
                int btStat = portObject.getInteger("BtStat");
                // 2输出时间到，3充电完成，4插头拨出， 5功率过大，6端口异常，7高温报警
                log.info("-------------主动上报状态， ");
                if (btStat == 2 || btStat == 3 || btStat == 4
                        || btStat == 5 || btStat == 6 || btStat == 7) {
                    log.info("-------------主动上报状态， 完成/停止充电， device：{}，  portObject： {}", device, portObject);
                    // 输出头时间到，充电完成，插拨出
                    Integer port = portObject.getInteger("Port");
                    udpateChargeRecord(device, DeviceCmdEnum.CLOSE_PORT.getValue(), port, portObject, recordCode);
                    redisService.delMaxPower(device, port);
                }
            }
        } else {
            // 其他不做处理
            log.info("=====================================", cmd);
            log.info("================={}==================", cmd);
            log.info("=====================================", cmd);
        }

    }

    /**
     *
     * @param device
     * @param port
     * @param portObject
     * @throws Exception
     */
    public void udpateChargeRecord(String device, Integer cmd, Integer port, JSONObject portObject, String recordCode) throws Exception {
        ChargeRecordDO entity = null;

        if (StrUtil.isNotBlank(recordCode)) {
            entity = this.findByRecordCode(recordCode);
        } else {
            // 加上timeout查询, 60秒是考虑网络延迟等
            Integer time = portObject.getInteger("OutTime") + 120;
            LocalDateTime dateTime = LocalDateTime.now().minusSeconds(time);
            entity = chargeRecordMapper.findByDeviceAndPort(device, port, ChargeRecordDO.PROCEED_STATUS_1, dateTime);
        }

        if (Objects.isNull(entity)) {
            log.info("---------------未查询到订单信息--------------");
            return;
        }

        // 关闭端口，充电结束
        if (DeviceCmdEnum.CLOSE_PORT.getValue().equals(cmd)) {
//            String key = RedisKeyConstants.DEVICE_CHARGING_STATUS + device;
//            JSONObject value = (JSONObject)redisTemplate.opsForValue().get(key);

            // 关闭端口， 更新设备状态
            // 从指定端口获取数据
//            Integer btStat = portObject.getInteger("BtStat");
//            Integer power = portObject.getInteger("Power");
            String energy = portObject.getString("Energy");
            entity.setEnergyEnd(energy);
            if (Objects.equals(entity.getEnergy(), 0)) {
                // 功率是0， 第一次上报功率
                entity.setWattage(BigDecimal.ZERO);
                entity.setKilowatt(BigDecimal.ZERO);
            } else if (Objects.nonNull(entity.getEnergy())) {
                Integer wattage = Integer.parseInt(energy) - Integer.parseInt(entity.getEnergy());
                BigDecimal watt = new BigDecimal(wattage);
                entity.setWattage(watt);
                entity.setKilowatt(watt.divide(new BigDecimal("60"), 5, RoundingMode.HALF_UP)
                        .divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP));
            }

            JSONObject value = redisService.getMaxPower(device, port);

            log.info("----------------value: {}", value);
            if (Objects.isNull(value)) {
                log.info("---------------redis中未查询到订单信息--------------");
                return;
            }
//            JSONObject data = value.getJSONObject("data");
            Integer maxPower = value.getInteger("maxPower");

            entity.setPower(maxPower);
            entity.setEnergy(energy);
            entity.setProceedStatus(ChargeRecordDO.PROCEED_STATUS_2);

            entity.setChargeTimeEnd(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());

            // 实际时间
            Duration duration = Duration.between(entity.getChargeTimeStart(), LocalDateTime.now());
            int time = (int)duration.getSeconds();
            entity.setTime(time);

            String timeStr = this.calcChargeTime(duration);

            Duration duration2 = Duration.between(entity.getChargeTimeStart(), LocalDateTime.now().minusSeconds(300L));
            String timeStr2 = this.calcChargeTime(duration2);

//            long hours = duration.toHours();
//            long minutes = duration.toMinutes() % 60;
//            long seconds = duration.getSeconds() % 60;
//
//            String timeStr = "";
//            if (hours == 0) {
//                timeStr +=  "00";
//            } else if (hours <10) {
//                timeStr += "0" + hours;
//            } else {
//                timeStr += hours;
//            }
//
//            if (minutes == 0) {
//                timeStr += ":00";
//            } else if (minutes <10) {
//                timeStr += ":0" + minutes;
//            } else {
//                timeStr += ":" + minutes;
//            }
//
//            if (seconds == 0) {
//                timeStr += ":00";
//            } else if (seconds <10) {
//                timeStr += ":0" + seconds;
//            } else {
//                timeStr += ":" + seconds;
//            }

            entity.setActualChargeTime(timeStr);
            entity.setDiscountChargeTime(timeStr2);

            // 如果已经有reason则不再更新， 否则更新原因字段
            if (StrUtil.isBlank(entity.getStopReason())) {
                // btStat 可以再做个二次判断
                Integer btStat = portObject.getInteger("BtStat");
                String desc = DeviceBtStatEnum.getDescByValue(btStat);
                String reason = String.format("cmd：%S；指令说明：%S；  btStat：%S； 状态说明：%S", cmd, DeviceCmdEnum.getDescByValue(cmd), btStat, desc);
                entity.setStopReason(reason);
                entity.setLastReportContent(portObject.toJSONString());
            }

            chargeRecordMapper.updateById(entity);

            // 费用处理
            this.calcChargeAmount(entity, time, maxPower);

        } else if (DeviceCmdEnum.OPEN_PORT.getValue().equals(cmd)) {
            Integer btStat = portObject.getInteger("BtStat");
            Integer time = portObject.getInteger("OutTime");
            Integer power = portObject.getInteger("Power");
            String energy = portObject.getString("Energy");

            entity.setTime(time);
            entity.setPower(power);
            entity.setEnergyStart(energy);
            entity.setEnergy(energy);
            entity.setProceedStatus(ChargeRecordDO.PROCEED_STATUS_1);

            entity.setChargeTimeStart(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());

            // 主动上报充电端口状态
            MbeDO mbeDO = memberService.selectMemberByMpUserId(entity.getMpId());
            entity.setMbeId(mbeDO.getId());
            entity.setMbeCode(mbeDO.getCode());
            entity.setMobile(mbeDO.getMobile());
            entity.setChargeTimeStart(LocalDateTime.now());

            DeviceDO deviceDO = deviceService.findByDevice(device);
            entity.setChargeCommunityId(deviceDO.getCommunityId());
            entity.setBuildingsId(deviceDO.getBuildingId());
            entity.setAutoOpen(DeviceAutoOpenConst.AUTO_OPEN_TRUE);    //  0为关闭自启动，1为打开自启动

            chargeRecordMapper.updateById(entity);
        }
//        else if (DeviceCmdEnum.REPORT_STATUS.getValue().equals(cmd)) {
//            // 主动上报充电端口状态， 能走到这边说明是充电完成，插头拨出，异常告警等情况
//            log.info("----------主动上报充电端口状态， 能走到这边说明是充电完成，插头拨出，异常告警等情况-----------");
//
//            // btStat 可以再做个二次判断 todo
//            Integer btStat = portObject.getInteger("BtStat");
//            // 此时的Power可能是0，所以不能用
////            Integer power = portObject.getInteger("Power");
//            String energy = portObject.getString("Energy");
//            entity.setEnergyEnd(energy);
//
//            if (Objects.equals(entity.getEnergy(), 0)) {
//                // 功率是0， 第一次上报功率
//                entity.setWattage(BigDecimal.ZERO);
//                entity.setKilowatt(BigDecimal.ZERO);
//            } else if (Objects.nonNull(entity.getEnergy())) {
//                Integer wattage = Integer.parseInt(energy) - Integer.parseInt(entity.getEnergy());
//                BigDecimal watt = new BigDecimal(wattage);
//                entity.setWattage(watt);
//                entity.setKilowatt(watt.divide(new BigDecimal("60"), 5, RoundingMode.HALF_UP)
//                        .divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP));
//            }
//
//            JSONObject value = redisService.getMaxPower(device, port);
//            log.info("----------------value: {}", value);
//            if (Objects.isNull(value)) {
//                log.info("---------------redis中未查询到订单信息--------------");
//                return;
//            }
////            JSONObject data = value.getJSONObject("data");
//            Integer maxPower = value.getInteger("maxPower");
//            entity.setPower(maxPower);
//            entity.setEnergy(energy);
//            entity.setProceedStatus(ChargeRecordDO.PROCEED_STATUS_2);   // 0
//
//            entity.setChargeTimeEnd(LocalDateTime.now());
//            entity.setUpdateTime(LocalDateTime.now());
//
//            // 实际时间
//            Duration duration = Duration.between(entity.getChargeTimeStart(), LocalDateTime.now());
//            int time = (int)duration.getSeconds();
//            entity.setTime(time);
//
//            long hours = duration.toHours();
//            long minutes = duration.toMinutes() % 60;
//            long seconds = duration.getSeconds() % 60;
//
//            String timeStr = "";
//            if (hours == 0) {
//                timeStr +=  "00";
//            } else if (hours <10) {
//                timeStr += "0" + hours;
//            } else {
//                timeStr += hours;
//            }
//
//            if (minutes == 0) {
//                timeStr += ":00";
//            } else if (minutes <10) {
//                timeStr += ":0" + minutes;
//            } else {
//                timeStr += ":" + minutes;
//            }
//
//            if (seconds == 0) {
//                timeStr += ":00";
//            } else if (seconds <10) {
//                timeStr += ":0" + seconds;
//            } else {
//                timeStr += ":" + seconds;
//            }
//
//            entity.setActualChargeTime(timeStr);
//
//
//            String desc = DeviceBtStatEnum.getDescByValue(btStat);
//            String reason = String.format("主动上报端口状态， cmd：%S；  btStat：%S； desc：%S", cmd, btStat, desc);
//            entity.setStopReason(reason);
//            entity.setLastReportContent(portObject.toJSONString());
//
//            chargeRecordMapper.updateById(entity);
//
//            // 费用处理
//            this.calcChargeAmount(entity, time, maxPower);
//        }

    }

    /**
     * 计算充电时间
     * @return
     */
    private String calcChargeTime(Duration duration) {
        // 如果结束时间小于开始时间，则不计算
        if (duration.getSeconds() <= 0) {
            return "00:00:00";
        }

        // 实际时间
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        long seconds = duration.getSeconds() % 60;

        String timeStr = "";
        if (hours == 0) {
            timeStr +=  "00";
        } else if (hours <10) {
            timeStr += "0" + hours;
        } else {
            timeStr += hours;
        }

        if (minutes == 0) {
            timeStr += ":00";
        } else if (minutes <10) {
            timeStr += ":0" + minutes;
        } else {
            timeStr += ":" + minutes;
        }

        if (seconds == 0) {
            timeStr += ":00";
        } else if (seconds <10) {
            timeStr += ":0" + seconds;
        } else {
            timeStr += ":" + seconds;
        }

        return timeStr;
    }


    @Override
    public void deleteChargeRecord(Long id) {
        // 校验存在
        validateChargeRecordExists(id);
        // 删除
        chargeRecordMapper.deleteById(id);
    }

    private void validateChargeRecordExists(Long id) {
        if (chargeRecordMapper.selectById(id) == null) {
            throw exception(CHARGE_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public ChargeRecordDO getChargeRecord(Long id) {
        ChargeRecordDO entity = chargeRecordMapper.selectById(id);
        if (Objects.nonNull(entity)) {
            if (Objects.nonNull(entity.getChargeCommunityId())) {
                CommunityDO community = communityService.getCommunity(entity.getChargeCommunityId());
                Optional.ofNullable(community).ifPresent(m -> entity.setChargeCommunityName(community.getName()));
            }

            if (Objects.nonNull(entity.getBuildingsId())) {
                BuildingDO building = buildingService.getBuilding(entity.getBuildingsId());
                Optional.ofNullable(building).ifPresent(m -> entity.setBuildingsName(building.getName()));
            }
        }
        return entity;
    }

    @Override
    public PageResult<ChargeRecordDO> getChargeRecordPage(ChargeRecordPageReqVO pageReqVO) {

        boolean flag = false;
        List<Long> list = new ArrayList<>();
        if (StrUtil.isNotBlank(pageReqVO.getChargeCommunityName())) {
            list = communityService.getCommunityList(pageReqVO.getChargeCommunityName())
                    .stream().map(CommunityDO::getId).collect(Collectors.toList());
            flag = true;
        }

        if (BooleanUtil.isTrue(flag) && CollUtil.isEmpty(list)) {
            return new PageResult<ChargeRecordDO>();
        }
        PageResult<ChargeRecordDO> page = chargeRecordMapper.selectPage(pageReqVO, list);
        page.getList().forEach(item -> {
            if (Objects.nonNull(item.getChargeCommunityId())) {
                CommunityDO community = communityService.getCommunity(item.getChargeCommunityId());
                if (Objects.nonNull(community)) {
                    item.setChargeCommunityName(community.getName());
                }
            }

            if (Objects.nonNull(item.getBuildingsId())) {
                BuildingDO building = buildingService.getBuilding(item.getBuildingsId());
                if (Objects.nonNull(building)) {
                    item.setBuildingsName(building.getName());
                }
            }

        });
        return page;
    }

    @Override
    public PageResult<MgrChargeRecordRespVO> getChargeRecordPage4Mgr(ChargeRecordPageReqVO reqVO) {
        IPage iPage = new Page(reqVO.getPageNo(), reqVO.getPageSize());
        IPage<MgrChargeRecordRespVO> page = chargeRecordMapper.getChargeRecordPage4Mgr(iPage, reqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public ChargeRecordDO lastOneByMpId(Long mpId) {
        return chargeRecordMapper.lastOneByMpId(mpId);
    }

    @Override
    public ChargeRecordDetailRespVO detailById(Long id) {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();

        ChargeRecordDetailRespVO respVO = null;
        ChargeRecordDO record = this.getChargeRecord(id);
        if (Objects.nonNull(record)) {
            respVO = BeanUtils.toBean(record, ChargeRecordDetailRespVO.class);
            respVO.setChargeTime(record.getChargeTimeStart());
            // 获取小区信息
            CommunityDO community = communityService.getCommunity(record.getChargeCommunityId());
            if (Objects.nonNull(community)) {
                // 获取楼栋信息
                respVO.setChargeCommunityName(community.getName());
            }

            if (Objects.nonNull(record.getChargeTimeStart())) {
                // 转换为 Unix 时间戳（秒）
                long seconds  = DateUtil.currentSeconds() - record.getChargeTimeStart().toInstant(ZoneOffset.of("+8")).getEpochSecond();
                respVO.setSeconds(seconds);
            }

            // 不是包月消费的
            if (!PaySourceEnum.BY_BYXF.getValue().equals(record.getPaySource())) {
                BigDecimal amount = record.getAmount();
                BigDecimal used = record.getActualAmount() == null ? BigDecimal.ZERO : record.getActualAmount();
                respVO.setBalance(amount.subtract(used));
            }

            // 剩余金额
//            MbeDO member = memberService.selectMemberByMpUserId(mpId);
//            if (Objects.nonNull(member)) {
//                respVO.setBalance(member.getGiftBalance().add(member.getRechargeBalance()));
//            }
        }
        return respVO;
    }

    @Override
    public ChargeRecordDO findByDeviceAndPort(String device, Integer port, Integer proceedStatus) {
        return chargeRecordMapper.findByDeviceAndPort2(device, port, proceedStatus);
    }


    /**
     * 计算是否超时，是否结束充电
     * @param device 设备编号
     * @param port 充电的端口号
     * @param timeout 已经充电了的时间（单位：秒）
     * @return
     */
    @Override
    public Boolean calcTimeout(String device, Integer port, Integer timeout, Map<String, String> map) throws Exception {
        // 查询充值金额、套餐模式（按电量 还是时间段）
        Integer time = timeout + 70;
        LocalDateTime dateTime = LocalDateTime.now().minusSeconds(time);
        ChargeRecordDO chargeRecordDO = chargeRecordMapper.findByDeviceAndPort(device, port, ChargeRecordDO.PROCEED_STATUS_1, dateTime);
        if (Objects.isNull(chargeRecordDO)) {
            // 已经结束
            log.error("===============【calcTimeout】: 没有查询到充电记录， 数据异常");
            map.put("reason", "原因：订单不存在");
            return true;
        }

//        String key = RedisKeyConstants.DEVICE_CHARGING_STATUS + device;
//        JSONObject value = (JSONObject)redisTemplate.opsForValue().get(key);
        JSONObject value = redisService.getMaxPower(device, port);
        JSONObject data = value.getJSONObject("data");
        Integer maxPower = value.getInteger("maxPower");

        // 充电模式type, 2/3：单次消费； 4：临时消费； 1：包月消费
        if (chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_BYXF.getValue())) {
            // 包月消费, 这里需要判断边界
            Boolean result = memberService.isValidMonthly(chargeRecordDO.getMpId(), chargeRecordDO.getChargeCommunityId());
            if (!result) {
                // 未包月或者包月已经过期了
//                map.put("reason", "原因：未购买包月或者包月已经过期； 消费方式：" + PaySourceEnum.BY_BYXF.getDesc());
                map.put("reason", "包月已过期");
                return true;
            }
//            MbeDO member = memberService.selectMemberByMpUserId(chargeRecordDO.getMpId());
//            if (Objects.isNull(member.getMonthlyPassDuration()) || member.getMonthlyPassDuration().isBefore(LocalDateTime.now())) {
//                // 未包月或者包月已经过期了
//                map.put("reason", "原因：未购买包月或者包月已经过期； 消费方式：" + PaySourceEnum.BY_BYXF.getDesc());
//                return true;
//            }
            return false;
        } else if (chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_ZSYE.getValue()) ||
                chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_YECZ.getValue())) {
            // 赠送余额 + 余额充值
            // 单次消费
            SchemeDO schemeDO = schemeService.selectByDevice(device);
            if (Objects.nonNull(schemeDO)) {
                MbeDO member = memberService.selectMemberByMpUserId(chargeRecordDO.getMpId());
                BigDecimal balance = member.getGiftBalance().add(member.getRechargeBalance());
                // 如果有守护充电，则需要再扣除守护的费用
                if (GuardChargeEnum.GUARD_CHARGE_1.getValue().equals(chargeRecordDO.getGuardFalg())) {
                    balance = balance.subtract(ChargeRecordDO.GUARD_AMT);
                }
                if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_1.getValue())) {
                    // 功率范围
                    BigDecimal price = selectPriceByPower(maxPower, schemeDO);
                    int tmpTimeout = timeout > data.getInteger("OutTime") ? timeout : data.getInteger("OutTime");
                    tmpTimeout = tmpTimeout - FREE_DURATION_SEC;
                    Integer remainingSec = calcRemainingTime4Power(balance, price, tmpTimeout);
                    // 剩余时间 小于等于0 说明余额不足，需要关闭端口
                    if (remainingSec <= 0) {
                        // 如果支付费用不够，则直接关闭端口
                        String reason = String.format("【原因】 余额不足； 【消费方式】%S； 【计费方式】%S",
                                PaySourceEnum.BY_YECZ.getDesc(), ChargeModeEnum.CHARGE_MODE_1.getDesc());
                        map.put("reason", reason);
                        return true;
                    }

                    // 按时间计算
                    // 单位：小时
                    Integer estimatedChargingTime = chargeRecordDO.getEstimatedChargingTime();
                    if (Objects.nonNull(estimatedChargingTime)) {
                        Integer sec = estimatedChargingTime * 3600;
                        if ((timeout - FREE_DURATION_SEC) >= sec ) {
                            // 已经充电的大于等于预计充电时间
                            String reason = String.format("【原因】 超过预计充电时间； 【消费方式】%S； 【计费方式】%S",
                                    PaySourceEnum.BY_YECZ.getDesc(), ChargeModeEnum.CHARGE_MODE_1.getDesc());
                            map.put("reason", reason);
                            return true;
                        }
                    }

                    // 除以上两种请情况，其他都是可以继续充电
                    return false;
                } else if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_2.getValue())) {
                    // 按电量计费, 跟时间段有关
                    List<SchemeTimeDO> list = schemeTimeService.findBySchemeId(schemeDO.getId());

                    LocalDateTime currentTime = LocalDateTime.now();
                    LocalTime currentHour = currentTime.toLocalTime();
                    // 元/度
                    BigDecimal pricePerHour = getPricePerHour(list, currentHour, ChargeModeEnum.CHARGE_MODE_2);
//                    if (pricePerHour == 0) {
//                        // 如果当前时间段不在定义的计费时间段内，跳出循环，等待进入有效时间段
//                        break;
//                    }
                    // 计算总度数
                    BigDecimal totalDegree = balance.divide(pricePerHour, 2, RoundingMode.HALF_UP);
                    // 功率 转 千瓦
                    BigDecimal tmpPower = new BigDecimal(maxPower).divide(new BigDecimal("1000"), 5, RoundingMode.HALF_UP);
                    long sec = totalDegree.divide(tmpPower, 2, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("3600")).longValue(); // 将小时转换为秒
                    log.info("totalDegree = {}； sec = {}；  currentTime = {}； maxPower = {}； timeout = {}",
                            totalDegree, sec, currentTime, maxPower, timeout);

                    if ((timeout - FREE_DURATION_SEC) >= sec ) {
                        // 可充电时长 小于等于 已充电时长，不能继续充电
                        String reason = String.format("【原因】 费用不足； 【消费方式】%S； 【计费方式】%S",
                                PaySourceEnum.BY_YECZ.getDesc(), ChargeModeEnum.CHARGE_MODE_2.getDesc());
                        map.put("reason", reason);
                        return true;
                    }
                } else if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_3.getValue())) {
                    // 按电量计费, 跟时间段有关
                    List<SchemeTimeDO> list = schemeTimeService.findBySchemeId(schemeDO.getId());

                    LocalDateTime currentTime = LocalDateTime.now();
                    LocalTime currentHour = currentTime.toLocalTime();
                    BigDecimal pricePerHour = getPricePerHour(list, currentHour, ChargeModeEnum.CHARGE_MODE_3);
                    BigDecimal servicePrice = selectPriceByPower(maxPower, schemeDO);
                    // 总的价格
                    BigDecimal totalPrice = pricePerHour.add(servicePrice);
                    // 计算出来的是可用的总度数
                    BigDecimal totalDegree = balance.divide(totalPrice, 5, RoundingMode.HALF_UP);
                    long sec = totalDegree.multiply(new BigDecimal("1000"))       // 将瓦转换成千万, 先乘1000再除，不然小数位影响比较大
                            .divide(new BigDecimal(maxPower), 5, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("3600")).longValue(); // 将小时转换为秒
                    System.out.println("------------- device = " + device + ";  totalDegree = " + totalDegree +
                            ";  sec = " + sec + ";  currentTime = " + currentTime + "; timeout = " + timeout +
                            "; maxPower = " + maxPower);

                    // 已用时长 - 免费时长 >= 可用时长
                    if ((timeout - FREE_DURATION_SEC) >= sec ) {
                        // 可用时长 小于等于已用时长，需要关闭端口
                        String reason = String.format("【原因】 费用不足； 【消费方式】%S； 【计费方式】%S",
                                PaySourceEnum.BY_WXZF.getDesc(), ChargeModeEnum.CHARGE_MODE_3.getDesc());
                        map.put("reason", reason);
                        return true;
                    }
                    return false;
                }
            }

        } else if (chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_WXZF.getValue())) { // 微信支付，临时充电
            // 临时消费，现金支付，按时间计算
            // todo todo
//            // 、根据设备查询套餐信息
//            SchemeDO schemeDO = schemeService.selectByDevice(device);
//            if (Objects.nonNull(schemeDO)) {
//                SchemeRespVO respVO = schemeService.getSchemeWithDetail(schemeDO.getId());
//                List<SchemeTimeSaveReqVO> timeItems = respVO.getTimeItems();
//                List<SchemePowerSaveReqVO> powerItems = respVO.getPowerItems();
//
//                // 落在哪个区间

            // 赠送余额 + 余额充值
            // 单次消费
            SchemeDO schemeDO = schemeService.selectByDevice(device);
            if (Objects.nonNull(schemeDO)) {
                // amount 是用户支付的金额
                BigDecimal balance = chargeRecordDO.getAmount();
//                // 如果有守护充电，则需要再扣除守护的费用
//                if (GuardChargeEnum.GUARD_CHARGE_1.getValue().equals(chargeRecordDO.getGuardFalg())) {
//                    balance = balance.subtract(ChargeRecordDO.GUARD_AMT);
//                }

                if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_1.getValue())) {
                    // 功率范围
                    BigDecimal price = selectPriceByPower(maxPower, schemeDO);
                    int tmpTimeout = timeout > data.getInteger("OutTime") ? timeout : data.getInteger("OutTime");
                    tmpTimeout = tmpTimeout - FREE_DURATION_SEC;
                    Integer remainingSec = calcRemainingTime4Power(balance, price, tmpTimeout);
                    // 剩余时间 小于等于0 说明余额不足，需要关闭端口
                    if (remainingSec <= 0) {
                        // 如果支付费用不够，则直接关闭端口
                        String reason = String.format("【原因】 费用不足； 【消费方式】%S； 【计费方式】%S",
                                PaySourceEnum.BY_WXZF.getDesc(), ChargeModeEnum.CHARGE_MODE_1.getDesc());
                        map.put("reason", reason);
                        return true;
                    }

                    // 按时间计算
                    // 单位：小时
                    Integer estimatedChargingTime = chargeRecordDO.getEstimatedChargingTime();
                    Integer sec = estimatedChargingTime * 3600;
                    if ((timeout - FREE_DURATION_SEC) >= sec ) {
                        // 已经充电的大于等于预计充电时间
                        String reason = String.format("【原因】 超过预计充电时间； 【消费方式】%S； 【计费方式】%S",
                                PaySourceEnum.BY_WXZF.getDesc(), ChargeModeEnum.CHARGE_MODE_1.getDesc());
                        map.put("reason", reason);
                        return true;
                    }
                    // 除以上两种请情况，其他都是可以继续充电
                    return false;
                } else if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_2.getValue())) {
                    // 按电量计费, 跟时间段有关
                    List<SchemeTimeDO> list = schemeTimeService.findBySchemeId(schemeDO.getId());

                    LocalDateTime currentTime = LocalDateTime.now();
                    LocalTime currentHour = currentTime.toLocalTime();
                    BigDecimal pricePerHour = getPricePerHour(list, currentHour, ChargeModeEnum.CHARGE_MODE_2);
//                    if (pricePerHour == 0) {
//                        // 如果当前时间段不在定义的计费时间段内，跳出循环，等待进入有效时间段
//                        break;
//                    }
                    // 计算出来的是可用的总度数
                    BigDecimal totalDegree = balance.divide(pricePerHour, 5, RoundingMode.HALF_UP);
                    long sec = totalDegree.multiply(new BigDecimal("1000"))       // 将瓦转换成千万, 先乘1000再除，不然小数位影响比较大
                            .divide(new BigDecimal(maxPower), 5, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("3600")).longValue(); // 将小时转换为秒
                    System.out.println("------------- device = " + device + ";  totalDegree = " + totalDegree +
                            ";  sec = " + sec + ";  currentTime = " + currentTime + "; timeout = " + timeout +
                            "; maxPower = " + maxPower);

                    // 已用时长 - 免费时长 >= 可用时长
                    if ((timeout - FREE_DURATION_SEC) >= sec ) {
                        // 可用时长 小于等于已用时长，需要关闭端口
                        String reason = String.format("【原因】 费用不足； 【消费方式】%S； 【计费方式】%S",
                                PaySourceEnum.BY_WXZF.getDesc(), ChargeModeEnum.CHARGE_MODE_2.getDesc());
                        map.put("reason", reason);
                        return true;
                    }
                    return false;
                } else if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_3.getValue())) {
                    // 按电量计费, 跟时间段有关
                    List<SchemeTimeDO> list = schemeTimeService.findBySchemeId(schemeDO.getId());

                    LocalDateTime currentTime = LocalDateTime.now();
                    LocalTime currentHour = currentTime.toLocalTime();
                    BigDecimal pricePerHour = getPricePerHour(list, currentHour, ChargeModeEnum.CHARGE_MODE_3);
                    BigDecimal servicePrice = selectPriceByPower(maxPower, schemeDO);
                    // 总的价格
                    BigDecimal totalPrice = pricePerHour.add(servicePrice);
                    // 计算出来的是可用的总度数
                    BigDecimal totalDegree = balance.divide(totalPrice, 5, RoundingMode.HALF_UP);
                    long sec = totalDegree.multiply(new BigDecimal("1000"))       // 将瓦转换成千万, 先乘1000再除，不然小数位影响比较大
                            .divide(new BigDecimal(maxPower), 5, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("3600")).longValue(); // 将小时转换为秒
                    System.out.println("------------- device = " + device + ";  totalDegree = " + totalDegree +
                            ";  sec = " + sec + ";  currentTime = " + currentTime + "; timeout = " + timeout +
                            "; maxPower = " + maxPower);

                    // 已用时长 - 免费时长 >= 可用时长
                    if ((timeout - FREE_DURATION_SEC) >= sec ) {
                        // 可用时长 小于等于已用时长，需要关闭端口
                        String reason = String.format("【原因】 费用不足； 【消费方式】%S； 【计费方式】%S",
                                PaySourceEnum.BY_WXZF.getDesc(), ChargeModeEnum.CHARGE_MODE_3.getDesc());
                        map.put("reason", reason);
                        return true;
                    }
                    return false;
                }
            }
        }

        return null;
    }

    // test test test
    public static void charge(double rechargeAmount) {
        LocalDateTime currentTime = LocalDateTime.now();
//        record.setStartTime(currentTime);
        double remainingAmount = rechargeAmount;
        System.out.println("---------start: "+remainingAmount);

        int i = 0;
        while (remainingAmount > 0) {
            LocalTime currentHour = currentTime.toLocalTime();
            double pricePerHour = getPricePerHour(currentHour);
            if (pricePerHour == 0) {
                // 如果当前时间段不在定义的计费时间段内，跳出循环，等待进入有效时间段
                break;
            }
            double chargeDurationInHour = Math.min(1, remainingAmount / pricePerHour);
            long sec = (long)(chargeDurationInHour * 3600); // 将小时转换为秒
            currentTime = currentTime.plusSeconds(sec);

            remainingAmount -= pricePerHour * chargeDurationInHour;
//            record.setChargedAmount(rechargeAmount - remainingAmount);
//            record.setEndTime(currentTime);

            i ++;
            System.out.println("---------i = " + i + ";  pricePerHour = " + pricePerHour + ";  remainingAmount = "+remainingAmount);
        }

//        return record;
    }

    public static void main(String[] args) {

        // 假设当前日期是 2025-04-16
        LocalDateTime currentDateTime = LocalDateTime.of(2025, 4, 16, 23, 59, 59);
        // 构造大于 23:59:59 的时间，这里进入到第二天的 00:00:01
        LocalDateTime nextDayDateTime = currentDateTime.plusNanos(99000000);
        System.out.println("大于 23:59:59 的时间: " + nextDayDateTime);



        String startTime = "0:00";
        String endTime = "23:59";
        if (startTime.split(":")[0].length() <= 1) {
            startTime = "0" + startTime;
        }
        if (endTime.split(":")[0].length() <= 1) {
            endTime = "0" + endTime;
        }
        // 兼容历史数据
        if (endTime.contains("24:00")) {
            endTime = "23:59";
        }

        // 定义日期时间格式
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 解析日期时间字符串
        LocalDateTime ldt = LocalDateTime.parse("2025-04-12 22:48:59", formatter1);
        LocalDateTime tempTime = nextDayDateTime; // ldt; // LocalDateTime.now();
        LocalTime time = tempTime.toLocalTime();
        time = LocalTime.parse(time.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
//        if ((time.isAfter(LocalTime.parse(startTime + ":00", formatter)) || time.equals(LocalTime.parse(startTime, formatter)))
        startTime = startTime + ":00";
        endTime = endTime + ":59";
        if ((time.isAfter(LocalTime.parse(startTime, formatter)) || time.equals(LocalTime.parse(startTime, formatter)))
                && (time.isBefore(LocalTime.parse(endTime, formatter)) || time.equals(LocalTime.parse(endTime, formatter)))) {
            System.out.println("--------------");
        }

        System.out.println("1111111");

//        charge(10.8);

//        Long e = DateUtil.currentSeconds();
//        Long s = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
//        Long s1 = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).getEpochSecond();
//
//        System.out.println(e);
//        System.out.println(s);
//        System.out.println(s1);
//
//
//        LocalDateTime start = LocalDateTime.now().minusSeconds(120L);
//        LocalDateTime end = LocalDateTime.now().minusSeconds(300L);
//        Duration duration2 = Duration.between(start, end);
//
//        ChargeRecordDO chargeRecordDO = new ChargeRecordDO();
//        chargeRecordDO.setId(43L);
//
//        LocalDateTime dateTime = LocalDateTime.parse("2025-01-25 11:12:31", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//        chargeRecordDO.setChargeTimeStart(dateTime);
//        chargeRecordDO.setPaySource(PaySourceEnum.BY_ZSYE.getValue());
//        chargeRecordDO.setType(ConsumeWayEnum.CONSUME_WAY_1.getValue());
//
//        Boolean flag = LocalDateTime.now().isAfter(dateTime.plusMinutes(5));
//        System.out.println("--------" + flag);
//
//        Integer timeout = 7200;
//        Integer power = 80;
//        new ChargeRecordServiceImpl().calcChargeAmount4Test(chargeRecordDO, timeout, power);



//        BigDecimal hours = new BigDecimal("204").divide(new BigDecimal("3600"),5, RoundingMode.HALF_UP);
//        System.out.println(hours);



        /////////////////////////
//        String startTime = "02:30";
//        // 定义合适的格式化器
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
//        try {
//            LocalTime time = LocalTime.parse(startTime, formatter);
//            System.out.println("解析成功，时间为: " + time);
//        } catch (java.time.format.DateTimeParseException e) {
//            System.out.println("解析失败: " + e.getMessage());
//        }
    }

    private static final double PRICE_0_TO_6 = 0.65; // 0点 - 6点的单价
    private static final double PRICE_6_TO_8 = 0.85; // 6点 - 8点的单价
    private static final double PRICE_8_TO_10 = 1.15; // 8点 - 10点的单价
    private static final double PRICE_10_TO_18 = 0.95; // 10点 - 18点的单价
    private static final double PRICE_18_TO_24 = 0.65; // 18点 - 24点的单价
    private static double getPricePerHour(LocalTime time) {
        if (time.isAfter(LocalTime.of(0, 0)) && time.isBefore(LocalTime.of(6, 0))) {
            return PRICE_0_TO_6;
        } else if (time.isAfter(LocalTime.of(6, 0)) && time.isBefore(LocalTime.of(8, 0))) {
            return PRICE_6_TO_8;
        } else if (time.isAfter(LocalTime.of(8, 0)) && time.isBefore(LocalTime.of(10, 0))) {
            return PRICE_8_TO_10;
        } else if (time.isAfter(LocalTime.of(10, 0)) && time.isBefore(LocalTime.of(18, 0))) {
            return PRICE_10_TO_18;
        } else if (time.isAfter(LocalTime.of(18, 0)) && time.isBefore(LocalTime.of(24, 0))) {
            return PRICE_18_TO_24;
        }
        return 0;
    }
    /**
     * 计算当前时间段的单价
     * @param list
     * @param time
     * @return
     */
    private BigDecimal getPricePerHour(List<SchemeTimeDO> list, LocalTime time, ChargeModeEnum chargeModeEnum) {

        // LocalTime 类型的带有时间戳
        time = LocalTime.parse(time.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        for (SchemeTimeDO item : list) {
            String startTime = item.getStartTime();
            String endTime = item.getEndTime();
            if (startTime.split(":")[0].length() <= 1) {
                startTime = "0" + startTime;
            }
            if (endTime.split(":")[0].length() <= 1) {
                endTime = "0" + endTime;
            }
            // 兼容历史数据
            if (endTime.contains("24:00")) {
                endTime = "23:59";
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
            startTime = startTime + ":00";
            endTime = endTime + ":59";

            LocalTime sTime = LocalTime.parse(startTime, formatter);
            LocalTime eTime = LocalTime.parse(endTime, formatter);
            if ((time.isAfter(sTime) || time.equals(sTime))
                    && (time.isBefore(eTime) || time.equals(eTime))) {
                // 电费
                BigDecimal eprice = item.getEprice();
                if (chargeModeEnum.equals(ChargeModeEnum.CHARGE_MODE_2)) {
                    // 服务费
                    BigDecimal sprice = item.getSprice();
                    // 总费用 = 电费 + 服务费
//                    BigDecimal totalPrice = eprice.add(sprice);
                    return eprice.add(sprice);

                } else if (chargeModeEnum.equals(ChargeModeEnum.CHARGE_MODE_3)) {
                    return eprice;
                }
            }
        }

        return BigDecimal.ZERO;
    }



    /**
     * 按时间计费方式的，根据功率查询价格,
     * @param power
     * @return
     */
    private BigDecimal selectPriceByPower(Integer power, SchemeDO schemeDO) {
        List<SchemePowerDO> list = schemePowerService.findBySchemeId(schemeDO.getId());
        for (SchemePowerDO item : list) {
            if (power >= item.getStartPower() && power < item.getEndPower()) {
                return item.getAmount();
            }
        }
        log.info("------------------------未设置正确的服务费------------------");
        return BigDecimal.ZERO;
    }

    /**
     * 计算剩余时间
     * @param payAmt 用户支付金额&总的余额
     * @param price 每千瓦时的价格
     * @param outTime 已用时间
     * @return
     */
    private Integer calcRemainingTime4Power(BigDecimal payAmt, BigDecimal price, Integer outTime) {
        // 用户支付金额 / 每小时的价格 = 时间（小时） * 3600 = 总时间（秒）
        int totalSec = payAmt.divide(price, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(3600)).intValue();
        int remainingSec = totalSec - outTime;
        return remainingSec;
    }


    /**
     * 保存充电状态
     * @param device
     * @param jsonObject
     */
    @Override
    public void saveChargingStatus2Redis(String device, JSONObject jsonObject) throws Exception  {

//        System.out.println("saveChargingStatus2Redis--------------------【device：{}】----【jsonObject：{}】: ",device, jsonObject);
        log.info("saveChargingStatus2Redis--------------------【device：{}】----【jsonObject：{}】: ",device, jsonObject);
        Integer cmd = jsonObject.getInteger("CMD");
//        String key = RedisKeyConstants.DEVICE_CHARGING_STATUS + device;
        if (DeviceCmdEnum.OPEN_PORT.getValue().equals(cmd)) {
            Integer port = jsonObject.getInteger("PORT");
            JSONArray jsonoArray = JSONArray.parseArray(jsonObject.getString("ALLDATA"));
            JSONObject jObject = jsonoArray.getJSONObject(port - 1);
            // 首次
//            jObject.put("MaxPower", jObject.getInteger("Power"));

            JSONObject value = new JSONObject();
            value.put("num", 0);
            value.put("port", port);
            value.put("data", jObject);
            value.put("minPowerNum", 0);
            value.put("minPower", MIN_POWER_DEFAULT);
            value.put("cmd", DeviceCmdEnum.OPEN_PORT.getValue());
            value.put("maxPower", jObject.getInteger("Power"));
            value.put("fiveMaxPower", jObject.getInteger("Power")); // 5分钟最大功率

//            redisTemplate.opsForValue().set(key, value);
            redisService.setMaxPower(device, port, value);
        } else if (DeviceCmdEnum.HEARTBEAT.getValue().equals(cmd)) {
            // 心跳上报
            // 数据是否存在，存在打开端口，但是没有添加到Redis的情况
//            JSONObject value = (JSONObject)redisTemplate.opsForValue().get(key);

            JSONArray jsonoArray = JSONArray.parseArray(jsonObject.getString("ALLDATA"));
            for (int i=0; i<jsonoArray.size(); i++) {
                JSONObject jObject = jsonoArray.getJSONObject(i);
                Integer btStat = jObject.getInteger("BtStat");
                Integer port = jObject.getInteger("Port");
                if (DeviceBtStatEnum.BT_STAT_0.getValue().equals(btStat)) {
                    // 待机，付钱了，但是设备未插入，此时，不是 上报状态，而是心跳上报
                    Integer time = jObject.getInteger("OutTime") + 150;
                    LocalDateTime dateTime = LocalDateTime.now().minusSeconds(time);
                    ChargeRecordDO chargeRecordDO = chargeRecordMapper.findByDeviceAndPort(device, port, ChargeRecordDO.PROCEED_STATUS_1, dateTime);
                    // 数据不存在，则忽略处理
                    if (Objects.isNull(chargeRecordDO)) {
                        continue;
                    }
                }

                log.info("心跳检查， 上报的jObject： {}", jObject);
                JSONObject value = redisService.getMaxPower(device, port);
                log.info("心跳检查， redis中value： {}", value);
                if (Objects.nonNull(value)) {
                    // 存在，则更新
                    Integer num = value.getInteger("num");
                    Integer oldPower = value.getInteger("maxPower");
                    Integer fiveMaxPower = value.getInteger("fiveMaxPower");    // 5分钟最大功率

                    // 新的
                    Integer newPower = jObject.getInteger("Power");
//                    Integer btStat = jObject.getInteger("BtStat");

                    // 判断、设值
                    Integer maxPower = newPower > oldPower ? newPower : oldPower;
                    value.put("maxPower", maxPower);
                    value.put("data", jObject);


                    // 计算剩余时间
                    if ( num > 5 ) {
                        // 只是为了测试功率稳定的时间，
                        num ++;
                        value.put("num", num);

                        // 加个时间判断，比如：超过3分钟就重新计算一次
                        int tmpMinPowerNum = 0;
                        // 如果连续功率小于30w，则自动关闭端口
                        if (newPower < 30) {
                            Integer minPower = value.getInteger("minPower");
                            Integer newMinPower = minPower < newPower ? minPower : newPower;
                            Integer minPowerNum = value.getInteger("minPowerNum") != null ? value.getInteger("minPowerNum") + 1 : 1;
                            value.put("minPower", newMinPower);
                            value.put("minPowerNum", minPowerNum);
                            tmpMinPowerNum = minPowerNum;
                            log.info("---------功率小于30，power：{}， minPowerNum：{}", newMinPower, minPowerNum);
                        }
                        redisService.setMaxPower(device, port, value);

                        // 计算是否充电结束了
                        // 充电结束条件： 1、充电时间超过了设定的最大时长； 2、功率小于设定的最小功率； 3、金额不够；
                        Boolean closePort = false;
                        //
                        Map<String, String> map = new HashMap<>();
                        if (tmpMinPowerNum >= 30) {
                            // 超过30次低功率，则自动关闭端口
                            closePort = true;
                            log.info("功率太低，并且大于等于30次，所以关闭端口, value：{}", value);
                        } else {
                            closePort = this.calcTimeout(device, port, jObject.getInteger("OutTime"), map);
                            log.info("时间到或者费用不足等，是否需要关闭端口closePort：{}", closePort);
                        }
                        if (BooleanUtil.isTrue(closePort)) {
                            // +150 考慮到充电桩上报心跳的时间差，防止端口关闭太快
                            String reason = map.get("reason");
                            Integer time = jObject.getInteger("OutTime") + 150;
                            LocalDateTime dateTime = LocalDateTime.now().minusSeconds(time);
                            ChargeRecordDO chargeRecordDO = chargeRecordMapper.findByDeviceAndPort(device, port, ChargeRecordDO.PROCEED_STATUS_1, dateTime);
                            if (Objects.nonNull(chargeRecordDO)) {
                                // 计算费用
//                            this.calcChargeAmount(chargeRecordDO);
                                try {
                                    String seq = MqttService.buildSeq(chargeRecordDO.getMpId(), chargeRecordDO.getCode());
                                    // 结束了，则调用关闭端口的接口
                                    log.info("reason：{}, 关闭端口。 num: {}；  device：{}；  port：{}；    seq：{}； newPower：{}",
                                            reason, num, device, port, seq, newPower);
                                    mqttService.closePort(device, port, seq);

                                    chargeRecordDO.setStopReason(reason);
                                    chargeRecordDO.setLastReportContent(jsonObject.toJSONString());
                                    chargeRecordMapper.updateById(chargeRecordDO);
                                } catch (MqttException e) {
                                    log.error(e.getMessage(), e);
                                    throw new RuntimeException(e);
                                }
                            } else {
                                log.info("=============订单不存在或者已经结束订单了=================");
                                log.info("device: {}； port：{}； dateTime：{}", device, port, dateTime);
                                redisService.delMaxPower(device, port);
                            }
                        }
                    } else {
                        num ++;
                        value.put("num", num);
                        fiveMaxPower = newPower > fiveMaxPower ? newPower : fiveMaxPower;
                        value.put("fiveMaxPower", fiveMaxPower);

                        // 前5分钟如果功率都小于30w，则认为是待机状态，自动关闭端口
                        // 刚开始如果功率过低，则直接关闭端口
                        if (newPower < 30 && num >= 5) {
                            // +150 考慮到充电桩上报心跳的时间差，防止端口关闭太快
                            Integer time = jObject.getInteger("OutTime") + 150;
                            LocalDateTime dateTime = LocalDateTime.now().minusSeconds(time);
                            ChargeRecordDO chargeRecordDO = chargeRecordMapper.findByDeviceAndPort(device, port, ChargeRecordDO.PROCEED_STATUS_1, dateTime);
                            if (Objects.nonNull(chargeRecordDO)) {
                                try {
                                    String seq = MqttService.buildSeq(chargeRecordDO.getMpId(), chargeRecordDO.getCode());
                                    // 结束了，则调用关闭端口的接口
                                    log.info("前5次，功率太低所以关闭端口。 num: {}；  device：{}；  port：{}；    seq：{}； newPower：{}",
                                            num, device, port, seq, newPower);
                                    mqttService.closePort(device, port, seq);
                                } catch (MqttException e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        }

                        redisService.setMaxPower(device, port, value);
                    }
                } else {
                    // redis 中没有数据，判断上报的端口是否打开了
                    log.info("==============心跳上报，但是没有记录到打开端口数据的情况=============");
                    if (DeviceBtStatEnum.BT_STAT_1.getValue().equals(btStat)) {
                        log.info("==============心跳上报，jObject： {} ", jObject.toJSONString());
                        JSONObject val = new JSONObject();
                        val.put("num", 1);
                        val.put("data", jObject);
                        val.put("port", port);
                        val.put("minPowerNum", 0);
                        val.put("minPower", MIN_POWER_DEFAULT);
                        val.put("cmd", DeviceCmdEnum.OPEN_PORT.getValue());
                        val.put("maxPower", jObject.getInteger("Power"));
                        val.put("fiveMaxPower", jObject.getInteger("Power")); // 5分钟最大功率

                        redisService.setMaxPower(device, port, val);
                    }
                }
            }

        }

    }

    /**
     * 关闭端口前计算费用
     * @param chargeRecordDO
     * @param timeout 充电时长，单位：秒
     */
    @Override
    public void calcChargeAmount(ChargeRecordDO chargeRecordDO, Integer timeout, Integer power) {
        // 转成分钟数
        Integer minutes = (timeout / 60) + (timeout % 60 > 0 ? 1 : 0);

        log.info("实际充电时长minutes: {}", minutes);

        // minutes 用于计算费用，所以会迭代到0分钟
        Integer tmpMinutes = minutes;

        // 5分钟的免费时长
        minutes = minutes - 5;
        if (minutes < 0) {
            minutes = 0;
        }

        // 1、支付方式
        if (chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_BYXF.getValue())) {
            // 包月消费，则不需要计算费用
            chargeRecordDO.setActualAmount(BigDecimal.ZERO);
            chargeRecordDO.setServiceAmt(BigDecimal.ZERO);

            // 更新订单金额
            chargeRecordMapper.updateById(chargeRecordDO);
        } else  {
            // 赠送余额 + 充值余额
            LocalDateTime currentTime = chargeRecordDO.getChargeTimeStart();
            // 5分钟的免费时长
            currentTime = currentTime.plusMinutes(5);
            log.info("-------------------免费5分钟后的时长minutes: {}", minutes);

            // 下单支付的金额, 如果实际金额大于下单支付的金额，则取下单支付金额即可，相差1分钱左右
            // 兼容历史数据
            BigDecimal payAmt = Objects.isNull(chargeRecordDO.getAmount()) ? BigDecimal.ZERO : chargeRecordDO.getAmount();
            SchemeDO schemeDO = schemeService.selectByDevice(chargeRecordDO.getDevice());
            if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_1.getValue())) {
                // 功率范围
                BigDecimal price = selectPriceByPower(power, schemeDO);
                BigDecimal hours = new BigDecimal(timeout).divide(new BigDecimal("3600"),5, RoundingMode.HALF_UP);
                BigDecimal totalAmt = price.multiply(hours).setScale(2, RoundingMode.HALF_UP);
                log.info("--------------按时间计费，实际支付金额为totalAmt：{}",totalAmt);
                // 实际金额大于支付金额
                if (totalAmt.compareTo(payAmt) > 0) {
                    totalAmt = payAmt;
                    log.info("--------------按时间计费，实际支付金额>下单支付金额，则取下单支付金额：{}",totalAmt);
                }
                chargeRecordDO.setActualAmount(totalAmt);
            } else if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_2.getValue())) {
                List<SchemeTimeDO> list = schemeTimeService.findBySchemeId(schemeDO.getId());
                LocalDateTime tempTime = currentTime;
                BigDecimal totalAmt = BigDecimal.ZERO;
                while (minutes > 0) {
                    LocalTime currentHour = tempTime.toLocalTime();
                    // 每度电（千瓦时）的价格p
                    BigDecimal pricePerDegree = getPricePerHour(list, currentHour, ChargeModeEnum.CHARGE_MODE_2);
                    // 千瓦 tempPower,功率P
                    BigDecimal tempPower = new BigDecimal(power).divide(new BigDecimal(1000), 5, RoundingMode.HALF_UP);
                    // 时间t=1/60
                    BigDecimal preMinute = new BigDecimal(1).divide(new BigDecimal(60), 5, RoundingMode.HALF_UP);
                    // 每分钟的费用 = 每小时价格 / 60
//                    BigDecimal pricePerMinute = pricePerHour.divide(new BigDecimal(60), 5, RoundingMode.HALF_UP);
                    BigDecimal pricePerMinute = tempPower.multiply(preMinute).multiply(pricePerDegree);
                    // 往前移一分钟
                    tempTime = tempTime.plusSeconds(60);
                    // 每分钟费用累加
                    totalAmt = totalAmt.add(pricePerMinute);
                    minutes--;
                }
                log.info("--------------保留两位小数之前的金额为totalAmt：{}",totalAmt);
                totalAmt = totalAmt.setScale(2, RoundingMode.HALF_UP);
                log.info("--------------保留两位小数之后的金额为totalAmt：{}",totalAmt);

                // 实际金额大于支付金额
                if (totalAmt.compareTo(payAmt) > 0) {
                    totalAmt = payAmt;
                    log.info("--------------实际支付金额>下单支付金额，则取下单支付金额：",totalAmt);
                }
                chargeRecordDO.setActualAmount(totalAmt);

                log.info("---------- chargeRecordId: {},  startTime: {},  endTime: {},  schemeId: {},  schemeType: {},  总费用：{}",
                        chargeRecordDO.getId(), currentTime, tempTime, schemeDO.getId(), schemeDO.getType(), totalAmt);
            } else if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_3.getValue())) {
                List<SchemeTimeDO> list = schemeTimeService.findBySchemeId(schemeDO.getId());
                LocalDateTime tempTime = currentTime;
                BigDecimal totalAmt = BigDecimal.ZERO;
                // 计算电费
                // 千瓦 tempPower,功率P
                BigDecimal tempPower = new BigDecimal(power).divide(new BigDecimal(1000), 5, RoundingMode.HALF_UP);
                while (minutes > 0) {
                    LocalTime currentHour = tempTime.toLocalTime();
                    // 每度电（千瓦时）的价格
                    BigDecimal pricePerDegree = getPricePerHour(list, currentHour, ChargeModeEnum.CHARGE_MODE_3);

                    // 时间t=1/60
                    BigDecimal preMinute = new BigDecimal(1).divide(new BigDecimal(60), 5, RoundingMode.HALF_UP);
                    // 每分钟的费用 = 每小时价格 / 60
//                    BigDecimal pricePerMinute = pricePerHour.divide(new BigDecimal(60), 5, RoundingMode.HALF_UP);
                    BigDecimal pricePerMinute = tempPower.multiply(preMinute).multiply(pricePerDegree);
                    // 往前移一分钟
                    tempTime = tempTime.plusSeconds(60);
                    // 每分钟费用累加
                    totalAmt = totalAmt.add(pricePerMinute);
                    minutes--;
                }
                // 计算服务费
                BigDecimal servicePrice = selectPriceByPower(power, schemeDO);
                BigDecimal hours = BigDecimal.ZERO;
                if ((tmpMinutes - 5) > 0) {
                    hours = new BigDecimal(tmpMinutes - 5).divide(new BigDecimal(60), 5, RoundingMode.HALF_UP);
                }
                BigDecimal totalServiceAmt = servicePrice.multiply(hours).setScale(2, RoundingMode.HALF_UP);
                if (totalServiceAmt.compareTo(BigDecimal.ZERO) == -1) {
                    // 小于0
                    totalServiceAmt = BigDecimal.ZERO;
                }

                BigDecimal kilowatt = tempPower.multiply(hours).setScale(4, RoundingMode.HALF_UP);

                totalAmt = totalAmt.add(totalServiceAmt).setScale(2, RoundingMode.HALF_UP);
                log.info("--------------保留两位小数之后的金额为totalAmt：{}",totalAmt);

                // 实际金额大于支付金额
                if (totalAmt.compareTo(payAmt) > 0) {
                    totalAmt = payAmt;
                    log.info("--------------实际支付金额>下单支付金额，则取下单支付金额：",totalAmt);
                }
                // 参考用电量
                chargeRecordDO.setKilowatt(kilowatt);
                chargeRecordDO.setActualAmount(totalAmt);
                chargeRecordDO.setServiceAmt(totalServiceAmt);

                log.info("---------- chargeRecordId: {},  startTime: {},  endTime: {},  schemeId: {},  schemeType: {}, ServiceAmt:{},   总费用：{}",
                        chargeRecordDO.getId(), currentTime, tempTime, schemeDO.getId(), schemeDO.getType(), totalServiceAmt, totalAmt);
            }

            // 余额 和 赠送金额
            if (chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_ZSYE.getValue())
                    || chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_YECZ.getValue())) {
                // 更新余额 todo 扣减顺序
                MbeDO mbeDO = memberService.selectMemberByMpUserId(chargeRecordDO.getMpId());
                BigDecimal diff;
                log.info("--------------【更新前】更新用户余额， rechargeBalance：{}， giftBalance：{}", mbeDO.getRechargeBalance(), mbeDO.getGiftBalance());
                if (mbeDO.getRechargeBalance().compareTo(chargeRecordDO.getActualAmount()) < 0) {
                    diff = chargeRecordDO.getActualAmount().subtract(mbeDO.getRechargeBalance());
                    mbeDO.setRechargeBalance(BigDecimal.ZERO);
                    mbeDO.setGiftBalance(mbeDO.getGiftBalance().subtract(diff));
                } else {
                    diff = mbeDO.getRechargeBalance().subtract(chargeRecordDO.getActualAmount());
                    mbeDO.setRechargeBalance(diff);
                }
                memberService.updateMember(mbeDO);
                log.info("--------------【更新后】更新用户余额， rechargeBalance：{}， giftBalance：{}", mbeDO.getRechargeBalance(), mbeDO.getGiftBalance());
            } else if (chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_WXZF.getValue())) {
                // 微信支付

                BigDecimal diff = BigDecimal.ZERO;
                // 是否守护充电，
//                Boolean flag = LocalDateTime.now().isAfter(chargeRecordDO.getChargeTimeStart().plusMinutes(5));
                log.info("------calcChargeAmount, 计算充电费用 minutes: {}, 守护充电标志：{}", minutes, chargeRecordDO.getGuardFalg());
                log.info("------calcChargeAmount, 支付金额：{}， 实际充电金额：{}", payAmt, chargeRecordDO.getActualAmount());
                if (GuardChargeEnum.GUARD_CHARGE_1.getValue().equals(chargeRecordDO.getGuardFalg())) {
                    // 是否超过5分钟了
                    if (tmpMinutes > 5) {
                        // 0.09不退
                        // 守护充电， 且超过5分钟了
                        log.info("-----calcChargeAmount, 1--------");
                        diff = payAmt.subtract(chargeRecordDO.getActualAmount());
                    } else {
                        // 0.09退
                        log.info("-----calcChargeAmount, 2--------");
                        diff = payAmt.subtract(chargeRecordDO.getActualAmount()).add(chargeRecordDO.getGuardAmt());
                    }
                } else {
                    // 未守护充电
                    log.info("-----calcChargeAmount, 3--------");
                    diff = payAmt.subtract(chargeRecordDO.getActualAmount());
                }

                if (diff.compareTo(BigDecimal.ZERO) > 0) {
                    // 退款
                    log.info("------------------有剩余费用，开始执行退款业务------------------amt: {}", diff);

                    // 新建一条扣减记录
                    String code = serialApi.getCode(PrefixConstants.PREFIX_TK);
                    RefundRecordSaveReqVO req = new RefundRecordSaveReqVO();
                    req.setCode(code);
                    req.setReason("剩余金额退款");
                    req.setOrderNo(chargeRecordDO.getCode());
                    req.setMpId(chargeRecordDO.getMpId());
                    req.setMobile(chargeRecordDO.getMobile());
                    req.setMbeCode(chargeRecordDO.getMbeCode());
                    req.setApplyAmount(diff);
                    req.setRefundSuccessAmount(BigDecimal.ZERO);
                    req.setType(RefundEnum.RefundTypeEnum.REFUND_TYPE_2.getValue());
                    req.setStatus(RefundEnum.RefundStatusEnum.REFUND_STATUS_0.getValue());
                    req.setApplyStatus(ApplyStatusEnum.RefundStatusEnum.APPLYING_0.getValue());
                    req.setApprovalStatus(ApprovalStatusEnum.RefundStatusEnum.APPLYING_0.getValue());

                    refundRecordService.createRefundRecord(req);

                    // 退款
                    PayAppRespDTO payApp = payAppApi.getByName(PayAppConst.XMYSJ);
                    PayRefundCreateReqDTO dto = new PayRefundCreateReqDTO();
                    dto.setAppId(payApp.getId());
                    dto.setReason("剩余金额退款");
                    dto.setUserIp(getClientIP());
                    dto.setMerchantOrderId(chargeRecordDO.getCode());
                    dto.setPrice(diff.multiply(new BigDecimal(100)).intValue());

//                    String tkCode = "TK"+System.currentTimeMillis();
                    dto.setMerchantRefundId(code);

                    log.info("开始执行退款业务，参数：{}", JSON.toJSONString(dto));
                    Long id = payRefundApi.createRefund(dto);
                    log.info("退款成功，退款单号：{}, 退款业务Id：{}", code, id);
                }
            }

            //
            log.info("-----------------开始计算分成金额");
            IncomeRecordDO incomeRecord = new IncomeRecordDO();
            this.calcShareAmt(chargeRecordDO, incomeRecord);
            log.info("------------------分成金额：{}", chargeRecordDO.getShareBenefit());

            // 记录分润记录
            CommunityDO communityDO = communityService.findByDevice(chargeRecordDO.getDevice());
            if (Objects.nonNull(communityDO)) {
                incomeRecord.setPartnerId(communityDO.getPartnerId());
            }

            incomeRecord.setMpId(chargeRecordDO.getMpId());
            incomeRecord.setMbeId(chargeRecordDO.getMbeId());
            incomeRecord.setMobile(chargeRecordDO.getMobile());
            incomeRecord.setMbeCode(chargeRecordDO.getMbeCode());
            incomeRecord.setAmount(chargeRecordDO.getActualAmount());
            incomeRecord.setCommunityId(chargeRecordDO.getChargeCommunityId());
            incomeRecord.setShareBenefit(chargeRecordDO.getShareBenefit());
            incomeRecordService.createIncomeRecord(incomeRecord);

            // 更新商户可提现金额
            log.info("-------------------------开始更新商户可提现金额-------------------");
            if (Objects.nonNull(communityDO.getPartnerId())) {
                PartnerDO partner = partnerService.getPartner(communityDO.getPartnerId());
                if (Objects.nonNull(partner)) {
                    // 分润金额
                    BigDecimal amount = incomeRecord.getShareBenefit() == null ? BigDecimal.ZERO : incomeRecord.getShareBenefit();
                    BigDecimal canWithdraw = partner.getCanWithdraw() == null ? BigDecimal.ZERO : partner.getCanWithdraw();
                    partner.setCanWithdraw(canWithdraw.add(amount));
                    partnerService.updatePartner(partner);
                    log.info("-------更新商户可提现金额完成， 更新可提前金额为：{}，  更新后可提现金额为：{}--------", canWithdraw, partner.getCanWithdraw());
                } else {
                    log.info("----------------------小区：{}， 商户负责人信息不存在");
                }
            } else {
                log.info("----------------------小区：{}， 未配置商户负责人");
            }

            // 更新订单金额
            chargeRecordMapper.updateById(chargeRecordDO);
        }
    }

    /**
     * 分润 = （充电总额 - 电费） * 分成比例
     * 例如： 17.208 = （100 - 0.533元/度 * 80度） * 30%
     * @param chargeRecordDO
     */
    private void calcShareAmt(ChargeRecordDO chargeRecordDO, IncomeRecordDO incomeRecord) {

        CommunityDO community = communityService.getCommunity(chargeRecordDO.getChargeCommunityId());
        if (Objects.nonNull(community) && Objects.nonNull(community.getProfitSharingPoints())) {
            // 这里的分成是百分比， 如：30%， 所以乘以0.01即可
            BigDecimal profitSharingPoints = community.getProfitSharingPoints().multiply(new BigDecimal("0.01"));

            // 消费0元
            if (chargeRecordDO.getActualAmount().compareTo(BigDecimal.ZERO) <= 0) {
                chargeRecordDO.setShareBenefit(BigDecimal.ZERO);
                incomeRecord.setRate(profitSharingPoints);
                return;
            } else {
                // 充电总额
                BigDecimal totalAmt = chargeRecordDO.getActualAmount();
                // 电费单价
                ConfigRespDTO config = configApi.getConfigByKey("electricity");
                BigDecimal electricity = new BigDecimal(config.getValue());
                // 电费总额
                BigDecimal totalElec = electricity.multiply(chargeRecordDO.getKilowatt());

                // 最终，分成金额
                BigDecimal shareBenefit = totalAmt.subtract(totalElec).multiply(profitSharingPoints).setScale(3, RoundingMode.HALF_UP);
                chargeRecordDO.setShareBenefit(shareBenefit);

                incomeRecord.setRate(profitSharingPoints);
            }
        }
    }


    /**
     * 测试用例
     * @param chargeRecordDO
     * @param timeout
     * @param power
     */
    private void calcChargeAmount4Test(ChargeRecordDO chargeRecordDO, Integer timeout, Integer power) {
        // 转成分钟数
        Integer minutes = (timeout / 60) + (timeout % 60 > 0 ? 1 : 0);
        // 1、支付方式
        if (chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_BYXF.getValue())) {
            // 包月消费，则不需要计算费用
            chargeRecordDO.setActualAmount(BigDecimal.ZERO);
        } else if (chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_ZSYE.getValue())
                || chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_YECZ.getValue())) {
            // 赠送余额 + 充值余额
            LocalDateTime currentTime = chargeRecordDO.getChargeTimeStart();
            System.out.println("---------minutes: "+minutes);

            // todo
            SchemeDO schemeDO = new SchemeDO();
            schemeDO.setId(12L);
            schemeDO.setType(ChargeModeEnum.CHARGE_MODE_2.getValue());
            if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_1.getValue())) {
                // 功率范围
                BigDecimal price = selectPriceByPower(power, schemeDO);
                Integer hours = timeout / 3600;
                BigDecimal totalAmt = price.multiply(new BigDecimal(hours));
                chargeRecordDO.setActualAmount(totalAmt);
            } else if (schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_2.getValue())) {
                // todo
//                List<SchemeTimeDO> list = schemeTimeService.findBySchemeId(schemeDO.getId());
                List<SchemeTimeDO> list = buildList();
                LocalDateTime tempTime = currentTime;
                BigDecimal totalAmt = BigDecimal.ZERO;
                while (minutes > 0) {
                    LocalTime currentHour = tempTime.toLocalTime();
                    BigDecimal pricePerHour = getPricePerHour(list, currentHour, ChargeModeEnum.CHARGE_MODE_2);
                    // 每分钟的价格 = 每小时价格 / 60
                    BigDecimal pricePerMinute = pricePerHour.divide(new BigDecimal(60), 5, RoundingMode.HALF_UP);
                    // 往前移一分钟
                    tempTime = tempTime.plusSeconds(60);
                    // 每分钟费用累加
                    totalAmt = totalAmt.add(pricePerMinute);
                    minutes--;
                }
                BigDecimal rt = totalAmt.setScale(2, RoundingMode.HALF_UP);
                System.out.println("totalAmt: " + rt);
                chargeRecordDO.setActualAmount(rt);
                log.info("chargeRecordId: {},  startTime: {},  endTime: {},  schemeId: {},  schemeType: {},  总费用：{}",
                        chargeRecordDO.getId(), currentTime, tempTime, schemeDO.getId(), schemeDO.getType(), rt);
            }


        } else if (chargeRecordDO.getPaySource().equals(PaySourceEnum.BY_WXZF.getValue())) {
            // 微信支付
        }
    }

    // test
    private List<SchemeTimeDO> buildList() {
        List<SchemeTimeDO> list = new ArrayList<>();

        SchemeTimeDO entity1 = new SchemeTimeDO();
        entity1.setStartTime("0:00");
        entity1.setEndTime("8:00");
        entity1.setEprice(new BigDecimal("0.35"));
        entity1.setSprice(new BigDecimal("0.35"));

        SchemeTimeDO entity2 = new SchemeTimeDO();
        entity2.setStartTime("8:00");
        entity2.setEndTime("18:00");
        entity2.setEprice(new BigDecimal("0.65"));
        entity2.setSprice(new BigDecimal("0.65"));

        SchemeTimeDO entity3 = new SchemeTimeDO();
        entity3.setStartTime("18:00");
        entity3.setEndTime("24:00");
        entity3.setEprice(new BigDecimal("0.55"));
        entity3.setSprice(new BigDecimal("0.55"));

        list.add(entity1);
        list.add(entity2);
        list.add(entity3);

        return list;
    }

    /**
     * 支付结果通知
     * @param channelId
     * @param params
     * @param body
     * @return
     */
    @Override
    public Boolean notifyOrder(Long channelId, Map<String, String> params, String body) throws Exception {
        PayOrderNotifyRespDTO respDTO = payOrderApi.orderNotify(channelId, params, body);

        String json = JSON.toJSON(respDTO).toString();
        System.out.println("========notifyOrder========");
        System.out.println(json);
        log.info("支付结果通知：{}", json);

        if (Objects.nonNull(respDTO)) {
            RedisDistributedLock lock = redisService.getLock(respDTO.getOutTradeNo());
            try {
                if (lock.tryLock(3, TimeUnit.SECONDS)) {

                    // 分类
                    String code = respDTO.getOutTradeNo();
                    if (code.contains(PrefixConstants.PREFIX_CD)) {
                        // 临时充电
                        ChargeRecordDO chargeRecordDO = chargeRecordMapper.findByCode(code);
                        // 如果已经通知过了，则不再处理
                        if (chargeRecordDO.getStatus().equals(OrderStatusEnum.PAID.getValue())) {
                            log.info("充电订单已经通知过了，不再处理");
                            return true;
                        }

                        if (respDTO.getSuccess()) {
                            // 充值成功，开始充电
                            chargeRecordDO.setRemark("充值成功");
                            chargeRecordDO.setStatus(OrderStatusEnum.PAID.getValue());
                            String seq = MqttService.buildSeq(chargeRecordDO.getMpId(), chargeRecordDO.getCode());
                            mqttService.openPort(chargeRecordDO.getDevice(), chargeRecordDO.getPort(), seq);
                        } else {
                            // 充值失败，更新订单状态
                            chargeRecordDO.setRemark("充值失败");
                            chargeRecordDO.setStatus(OrderStatusEnum.PAY_FAIL.getValue());
                        }
                        chargeRecordMapper.updateById(chargeRecordDO);
                    } else if (code.contains(PrefixConstants.PREFIX_BY)) {
                        // 包月充值
                        monthlyPkgRecordService.notifyHandle(respDTO);
                    } else if (code.contains(PrefixConstants.PREFIX_CZ)) {
                        // 余额充值
                        rechargeRecordService.notifyHandle(respDTO);
                    }

                } else {
                    log.info("---------------------获取锁失败，稍后重试--------------outTradeNo = {} ", respDTO.getOutTradeNo());
                    throw new RuntimeException("系统繁忙，请稍后再试");
                }
            } finally {
                lock.unlock();
            }
        }
        return true;
    }

    @Override
    public Boolean notifyRefund(Long channelId,  Map<String, String> map, String body) throws Exception {
        PayRefundNotifyRespDTO dto = payNotifyApi.refundNotify(channelId, map, body);

        // 1、根据退款单获取充退款记录
        PayRefundRespDTO refund = payRefundApi.getRefundByNo(dto.getOutRefundNo());
        if (Objects.isNull(refund)) {
            return dto.getSuccess();
        }

        // 退款成功
        if (dto.getSuccess()) {
            // 2、根据退款单获取，退款的原订单类型
            if (refund.getMerchantOrderId().contains(PrefixConstants.PREFIX_CD)) {
                // 临时充电
                log.info("------------------临时充电退款通知-------------------");
                refundRecordService.notifyHandle(refund.getMerchantOrderId(), refund.getRefundPrice(), RefundEnum.RefundStatusEnum.REFUND_STATUS_1);
            } else if (refund.getMerchantOrderId().contains(PrefixConstants.PREFIX_CZ)) {
                log.info("------------------余额充值退款通-------------------");
                RechargeRecordDO recordDO = rechargeRecordService.findByCode(refund.getMerchantOrderId());
                if (Objects.nonNull(recordDO)) {
                    // 余额退款处理，todo 处理重复推送的情况
                    memberService.refundHandleBalance(recordDO.getMpId(), refund.getRefundPrice());
                    refundRecordService.notifyHandle(refund.getMerchantOrderId(), refund.getRefundPrice(), RefundEnum.RefundStatusEnum.REFUND_STATUS_1);
                }
            } else if (refund.getMerchantOrderId().contains(PrefixConstants.PREFIX_BY)) {
                log.info("------------------包月充电退款通知todo-------------------");
            } else {
                log.info("------------------------未知的退款订单类型---------------------------");
            }
        } else {
            refundRecordService.notifyHandle(refund.getMerchantOrderId(), refund.getRefundPrice(), RefundEnum.RefundStatusEnum.REFUND_STATUS_2);
        }
        return dto.getSuccess();
    }

    @Override
    public ChargeRecordDO findByMpIdAndStatus(Long mpId, int type, Integer[] paySource, int proceedStatus) {
        return chargeRecordMapper.findByMpIdAndStatus(mpId, type, paySource, proceedStatus);
    }

    @Override
    public Long getUseNumByCommunityId(Long communityId, Integer[] processStatus) {
        return chargeRecordMapper.getUseNumByCommunityId(communityId, processStatus);
    }

    @Override
    public ChargeRecordDO findByMpIdAndDeviceInfo(Long mpId, int type, Integer[] paySource, int proceedStatus, String device, Integer port) {
        return chargeRecordMapper.findByMpIdAndDeviceInfo(mpId, type, paySource, proceedStatus, device, port);
    }

    @Override
    public PageResult<AppChargeRecordRespVO> getChargeRecordPage4App(ChargeRecordPageReqVO reqVO) {
        IPage<AppChargeRecordRespVO> iPage = new Page<AppChargeRecordRespVO>(reqVO.getPageNo(), reqVO.getPageSize());
        IPage<AppChargeRecordRespVO> page = chargeRecordMapper.getChargeRecordPage4App(iPage, reqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<ChargeRecordRespVO> getChargeRecord4Export(ChargeRecordPageReqVO pageReqVO) {
        return chargeRecordMapper.getChargeRecord4Export(pageReqVO);
    }
}
