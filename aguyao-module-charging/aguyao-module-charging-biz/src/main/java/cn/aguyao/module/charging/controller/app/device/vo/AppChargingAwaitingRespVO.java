package cn.aguyao.module.charging.controller.app.device.vo;

import cn.aguyao.module.charging.controller.app.schemecollect.vo.AppSchemeCollectRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 待充电
 */
@Schema(description = "用户端 - Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppChargingAwaitingRespVO {




    ////////////// 用户基本信息
    @Schema(description = "微信用户id")
    private Long mpId;

    @Schema(description = "会员id")
    private Long mbeId;

    @Schema(description = "会员编号")
    private String mbeCode;

    @Schema(description = "基本余额")
    private BigDecimal rechargeBalance;

    @Schema(description = "赠送余额")
    private BigDecimal giftBalance;

    ////////////// 充电费率
    private String rateInfo;


    ////////////// 单次充电套餐（按时间计费）
    @Schema(description = "单次充电套餐")
    private List<AppSchemeCollectRespVO> singleChargingSetmeal;


    ////////////// 临时充电套餐（按电量计费）
    @Schema(description = "临时充电套餐")
    private List<AppSchemeCollectRespVO> tempChargingSetmeal;


}
