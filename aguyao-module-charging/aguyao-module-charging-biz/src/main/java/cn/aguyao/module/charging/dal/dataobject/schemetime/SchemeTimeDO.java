package cn.aguyao.module.charging.dal.dataobject.schemetime;

import cn.aguyao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 收费方案—时段电费 DO
 *
 * <AUTHOR>
 */
@TableName("charging_scheme_time")
@KeySequence("charging_scheme_time_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchemeTimeDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 方案id
     */
    private Long schemeId;
    /**
     * 序号
     */
    private Integer sort;
    /**
     * 开始时段，左侧（包含）
     */
    private String startTime;
    /**
     * 结束时段，右侧（不含）
     */
    private String endTime;
    /**
     * 电费 energy price（单位：元）
     */
    private BigDecimal eprice;
    /**
     * 服务费 service price（单位：元）
     */
    private BigDecimal sprice;
    /**
     * 备注
     */
    private String remark;

}
