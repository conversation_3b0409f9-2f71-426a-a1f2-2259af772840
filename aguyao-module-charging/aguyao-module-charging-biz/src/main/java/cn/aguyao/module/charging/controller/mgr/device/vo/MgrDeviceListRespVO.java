package cn.aguyao.module.charging.controller.mgr.device.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MgrDeviceListRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10825")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "设备号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10825")
    @ExcelProperty("设备号")
    private String device;

    @Schema(description = "端口1状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "10825")
    @ExcelProperty("端口1状态")
    private Integer btStat1;

    @Schema(description = "端口2状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "10825")
    @ExcelProperty("端口2状态")
    private Integer btStat2;

    @Schema(description = "联网状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("联网状态")
    private Integer networkStatus;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
