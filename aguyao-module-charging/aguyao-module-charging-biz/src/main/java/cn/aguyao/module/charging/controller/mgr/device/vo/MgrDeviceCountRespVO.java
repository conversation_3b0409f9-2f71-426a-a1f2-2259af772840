package cn.aguyao.module.charging.controller.mgr.device.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 设备 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MgrDeviceCountRespVO {

    /**
     * 空闲设备数
     */
    @Schema(description = "空闲设备数")
    private Integer idleCount;

    /**
     * 使用设备数
     */
    @Schema(description = "使用设备数")
    private Integer occupiedCount;

    /**
     * 故障设备数
     */
    @Schema(description = "故障设备数")
    private Integer faultCount;

    /**
     * 离线设备数
     */
    @Schema(description = "离线设备数")
    private Integer offlineCount;

}
