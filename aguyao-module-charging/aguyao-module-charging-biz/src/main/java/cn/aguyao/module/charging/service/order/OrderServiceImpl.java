package cn.aguyao.module.charging.service.order;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.servlet.ServletUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.app.order.vo.AppRechargeRespVO;
import cn.aguyao.module.charging.controller.app.order.vo.AppSingleChargeReqVO;
import cn.aguyao.module.charging.controller.app.order.vo.AppTempChargeReqVO;
import cn.aguyao.module.charging.controller.app.order.vo.AppTempChargeRespVO;
import cn.aguyao.module.charging.dal.dataobject.chargerecord.ChargeRecordDO;
import cn.aguyao.module.charging.dal.dataobject.device.DeviceDO;
import cn.aguyao.module.charging.dal.dataobject.insurerecord.InsureRecordDO;
import cn.aguyao.module.charging.dal.dataobject.membermonthly.MemberMonthlyDO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgconfig.MonthlyPkgConfigDO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgrecord.MonthlyPkgRecordDO;
import cn.aguyao.module.charging.dal.dataobject.rechargediscountconfig.RechargeDiscountConfigDO;
import cn.aguyao.module.charging.dal.dataobject.rechargerecord.RechargeRecordDO;
import cn.aguyao.module.charging.dal.dataobject.user.MpUserDO;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.enums.*;
import cn.aguyao.module.charging.service.MqttService;
import cn.aguyao.module.charging.service.chargerecord.ChargeRecordService;
import cn.aguyao.module.charging.service.device.DeviceService;
import cn.aguyao.module.charging.service.insurerecord.InsureRecordService;
import cn.aguyao.module.charging.service.mbe.MbeService;
import cn.aguyao.module.charging.service.membermonthly.MemberMonthlyService;
import cn.aguyao.module.charging.service.monthlypkgconfig.MonthlyPkgConfigService;
import cn.aguyao.module.charging.service.monthlypkgrecord.MonthlyPkgRecordService;
import cn.aguyao.module.charging.service.mpuser.MpUserService;
import cn.aguyao.module.charging.service.rechargediscountconfig.RechargeDiscountConfigService;
import cn.aguyao.module.charging.service.rechargerecord.RechargeRecordService;
import cn.aguyao.module.charging.service.refundrecord.RefundRecordService;
import cn.aguyao.module.pay.api.app.PayAppApi;
import cn.aguyao.module.pay.api.app.dto.PayAppRespDTO;
import cn.aguyao.module.pay.api.order.PayOrderApi;
import cn.aguyao.module.pay.api.order.dto.PayOrderCreateReqDTO;
import cn.aguyao.module.pay.api.order.dto.PayOrderSubmitReqDTO;
import cn.aguyao.module.pay.api.order.dto.PayOrderSubmitRespDTO;
import cn.aguyao.module.system.api.serial.SerialApi;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.MONTHLY_PKG_CONFIG_NOT_EXISTS;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.RECHARGE_DISCOUNT_CONFIG_NOT_EXISTS;

/**
 * 订单 Service 实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Validated
public class OrderServiceImpl implements OrderService {


    @Resource
    private SerialApi serialApi;

    @Resource
    private PayOrderApi payOrderApi;

    @Resource
    private PayAppApi payAppApi;

    @Resource
    private MbeService mbeService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private MpUserService mpUserService;

    @Resource
    private MqttService mqttService;

//    @Lazy
    @Resource
    private ChargeRecordService chargeRecordService;

    @Resource
    private RefundRecordService refundRecordService;

    @Resource
    private MonthlyPkgRecordService monthlyPkgRecordService;

    @Resource
    private MonthlyPkgConfigService monthlyPkgConfigService;

    @Resource
    private RechargeRecordService rechargeRecordService;

    @Resource
    private InsureRecordService insureRecordService;

    @Resource
    private MemberMonthlyService memberMonthlyService;

    @Resource
    private RechargeDiscountConfigService rechargeDiscountConfigService;



    /**
     * 单次充电
     * @param reqVO
     * @return
     */
    @Override
    public CommonResult<String> singleCharge(AppSingleChargeReqVO reqVO) throws Exception {

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();

        // 是否有进行中的订单
        int proceedStatus = ChargeRecordDO.PROCEED_STATUS_1;
        int type = ConsumeWayEnum.CONSUME_WAY_1.getValue();
        Integer[] paySources = { PaySourceEnum.BY_BYXF.getValue() };
        ChargeRecordDO chargeRecord = chargeRecordService.findByMpIdAndStatus(mpId, type, paySources, proceedStatus);
        if (Objects.nonNull(chargeRecord)) {
            // 有包月的充电订单，不能同时充电
            return CommonResult.error(1, "有在充电的包月订单");
        }

        log.info("单次充电，mpId：{}， 请求参数：{}", mpId, JSONObject.toJSONString(reqVO));

        Integer[] paySources2 = { PaySourceEnum.BY_ZSYE.getValue(), PaySourceEnum.BY_YECZ.getValue() };
        ChargeRecordDO chargeRecord2 = chargeRecordService.findByMpIdAndDeviceInfo(mpId, type, paySources2, proceedStatus, reqVO.getDevice(), reqVO.getPort());

        if (Objects.nonNull(chargeRecord2)) {
            // 有余额的充电订单，不能同时充电
            return CommonResult.error(1, "该端口正在充电");
        }

        // 1、是否有未过期的包月
        Boolean isValid = false;
        DeviceDO deviceDO = deviceService.findByDevice(reqVO.getDevice());
        if (Objects.nonNull(deviceDO) && Objects.nonNull(deviceDO.getCommunityId())) {
            MemberMonthlyDO memberMonthlyDO = memberMonthlyService.findByMpIdAndCommunityId(mpId, deviceDO.getCommunityId());
            if (Objects.nonNull(memberMonthlyDO)
                    && Objects.nonNull(memberMonthlyDO.getExpiration())
                    && memberMonthlyDO.getExpiration().isAfter(LocalDateTime.now())) {
                isValid = true;
            } else {
                isValid = false;
            }
        }

        if (BooleanUtil.isTrue(isValid)) {
            log.info("----------------单次充电之包月充电，mpId：{}， 请求参数：{}", mpId, JSONObject.toJSONString(reqVO));
            // 包月中，直接开始充电
            String remark = "包月中，直接充电";
            return this.createRecordAndOpenPort(mpId, reqVO, PaySourceEnum.BY_BYXF, BigDecimal.ZERO, remark);
        }

        // 2、是否有余额（包含赠送余额）
        MbeDO mbeDO = mbeService.selectMemberByMpUserId(mpId);
        // 如果有守护充电，则余额至少大于0.09元，否则无法守护充电
        if (Objects.nonNull(mbeDO)) {
            // 余额判断，是否足够
            Boolean isEnough = false;
            // 余额 + 赠送余额 = 总的费用
            BigDecimal total = mbeDO.getRechargeBalance().add(mbeDO.getGiftBalance());
            if (GuardChargeEnum.GUARD_CHARGE_0.getValue().equals(reqVO.getGuardFlag())
                    && total.compareTo(BigDecimal.ZERO) > 0 ) {
                isEnough = true;
            } else if (GuardChargeEnum.GUARD_CHARGE_1.getValue().equals(reqVO.getGuardFlag())
                    && total.compareTo(BigDecimal.valueOf(0.09)) > 0) {
                isEnough = true;
            }

            if (BooleanUtil.isTrue(isEnough)) {
                // 优先使用充值余额
                PaySourceEnum paySource = PaySourceEnum.BY_YECZ;
                if (mbeDO.getRechargeBalance().compareTo(BigDecimal.ZERO) <= 0) {
                    paySource = PaySourceEnum.BY_ZSYE;
                }
                String remark = "余额足够，直接充电";
                return this.createRecordAndOpenPort(mpId, reqVO, paySource, total, remark);
            }
        }

        // 3.1、账户余额不足提示
        return CommonResult.error(1, "账户余额不足");
    }

    public CommonResult<String> createRecordAndOpenPort(Long mpId, AppSingleChargeReqVO reqVO, PaySourceEnum paySourceEnum,
                                                        BigDecimal total, String remark) throws Exception {
        ChargeRecordDO chargeRecord = new ChargeRecordDO();
        chargeRecord.setMpId(mpId);
        chargeRecord.setDevice(reqVO.getDevice());
        chargeRecord.setPort(reqVO.getPort());
        if (0 == reqVO.getType()) {
            // 充满自停
            chargeRecord.setEstimatedChargingTime(0);
        } else {
            chargeRecord.setEstimatedChargingTime(reqVO.getTime());
        }

        if (GuardChargeEnum.GUARD_CHARGE_0.getValue().equals(reqVO.getGuardFlag())) {
            // 未选守护充电
            chargeRecord.setGuardAmt(BigDecimal.ZERO);
            chargeRecord.setGuardFalg(GuardChargeEnum.GUARD_CHARGE_0.getValue());
        } else if (GuardChargeEnum.GUARD_CHARGE_1.getValue().equals(reqVO.getGuardFlag())) {
            // 已选守护充电
            chargeRecord.setGuardAmt(ChargeRecordDO.GUARD_AMT);
            chargeRecord.setGuardFalg(GuardChargeEnum.GUARD_CHARGE_1.getValue());
        }
        chargeRecord.setType(ConsumeWayEnum.CONSUME_WAY_1.getValue());
        chargeRecord.setPaySource(paySourceEnum.getValue());
        // 用户总的余额等于用户下单的金额
        chargeRecord.setAmount(total);
        chargeRecord.setRemark(remark);

        Long recordId = chargeRecordService.createChargeRecord(chargeRecord);
        ChargeRecordDO entity = chargeRecordService.getChargeRecord(recordId);

        // 添加守护充电记录
        if (GuardChargeEnum.GUARD_CHARGE_1.getValue().equals(reqVO.getGuardFlag())) {
            InsureRecordDO insureRecord = new InsureRecordDO();
            insureRecord.setMpId(mpId);
            insureRecord.setChargeRecordId(entity.getId());
            insureRecord.setChargeRecordCode(entity.getCode());
            insureRecord.setAmount(chargeRecord.getGuardAmt());
            insureRecordService.createInsureRecord(insureRecord);
        }

        MbeDO mbeDO = mbeService.selectMemberByMpUserId(mpId);
        if (Objects.nonNull(mbeDO) && Objects.isNull(mbeDO.getBelongCommunityId())) {
            // 更新归属小区ID
            DeviceDO deviceDO = deviceService.findByDevice(reqVO.getDevice());
            if (Objects.nonNull(deviceDO) && Objects.nonNull(deviceDO.getCommunityId())) {
                mbeService.updateBelongCommunityId(mbeDO.getId(), deviceDO.getCommunityId());
            }
        }

        Map<String, Object> map = new HashMap<>();
        map.put("mpId", mpId);
        map.put("recordId", recordId);
        map.put("recordCode", entity.getCode());
//        String seq = mpId + "_" + entity.getCode();
//        String json = JSON.toJSONString(map);
        String seq = MqttService.buildSeq(mpId, entity.getCode());
        mqttService.openPort(reqVO.getDevice(), reqVO.getPort(), seq);

        return CommonResult.success("充电开始");
    }

    /**
     * 2、临时充电
     * @param reqVO
     * @return
     * @throws Exception
     */
    @Override
    public AppTempChargeRespVO tempCharge(AppTempChargeReqVO reqVO) throws Exception {

        log.info("临时充电开始, reqVO: {}", JSONObject.toJSONString(reqVO));

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();

        MbeDO mbeDO = mbeService.selectMemberByMpUserId(mpId);
        if (Objects.nonNull(mbeDO) && Objects.isNull(mbeDO.getBelongCommunityId())) {
            // 更新归属小区ID
            DeviceDO deviceDO = deviceService.findByDevice(reqVO.getDevice());
            if (Objects.nonNull(deviceDO) && Objects.nonNull(deviceDO.getCommunityId())) {
                mbeService.updateBelongCommunityId(mbeDO.getId(), deviceDO.getCommunityId());
            }
        }

        // 1、生成临时单
        ChargeRecordDO chargeRecord = new ChargeRecordDO();
        chargeRecord.setMpId(mpId);
        chargeRecord.setDevice(reqVO.getDevice());
        chargeRecord.setPort(reqVO.getPort());
        chargeRecord.setAmount(reqVO.getAmount());
        chargeRecord.setProceedStatus(ChargeRecordDO.PROCEED_STATUS_0);
        chargeRecord.setType(ConsumeWayEnum.CONSUME_WAY_2.getValue());
        chargeRecord.setPaySource(PaySourceEnum.BY_WXZF.getValue());

        if (GuardChargeEnum.GUARD_CHARGE_0.getValue().equals(reqVO.getGuardFlag())) {
            // 未选守护充电
            chargeRecord.setGuardAmt(BigDecimal.ZERO);
            chargeRecord.setGuardFalg(GuardChargeEnum.GUARD_CHARGE_0.getValue());
        } else if (GuardChargeEnum.GUARD_CHARGE_1.getValue().equals(reqVO.getGuardFlag())) {
            // 已选守护充电
            chargeRecord.setGuardAmt(ChargeRecordDO.GUARD_AMT);
            chargeRecord.setGuardFalg(GuardChargeEnum.GUARD_CHARGE_1.getValue());
        }

        Long recordId = chargeRecordService.createChargeRecord(chargeRecord);
        ChargeRecordDO entity = chargeRecordService.getChargeRecord(recordId);

        // 添加守护充电记录
        if (GuardChargeEnum.GUARD_CHARGE_1.getValue().equals(reqVO.getGuardFlag())) {
            InsureRecordDO insureRecord = new InsureRecordDO();
            insureRecord.setMpId(mpId);
            insureRecord.setChargeRecordId(entity.getId());
            insureRecord.setChargeRecordCode(entity.getCode());
            insureRecord.setAmount(chargeRecord.getGuardAmt());
            insureRecordService.createInsureRecord(insureRecord);
        }

        // 实际支付的金额
        BigDecimal payAmt = entity.getAmount().add(entity.getGuardAmt());

        // 2、生成支付单
        // 构造支付参数
        PayOrderCreateReqDTO reqDTO = new PayOrderCreateReqDTO();
        PayAppRespDTO dto = payAppApi.getByName(PayAppConst.XMYSJ);
        reqDTO.setAppId(dto.getId());
        reqDTO.setMerchantOrderId(entity.getCode());
        reqDTO.setSubject("临时充电");
        reqDTO.setBody("临时充电, code = " + entity.getCode());
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setType(OrderTypeEnum.ORDER_TYPE_LS.getValue());
        reqDTO.setExpireTime(LocalDateTime.now().plusMinutes(10));      // 10分钟过期
        reqDTO.setPrice(payAmt.multiply(BigDecimal.valueOf(100)).intValue());   // 单位：分
        Long orderId = payOrderApi.createOrder(reqDTO);

        // 3、提交支付
        MpUserDO user = mpUserService.getUser(mpId);
        PayOrderSubmitReqDTO submitReqDTO = new PayOrderSubmitReqDTO();
        submitReqDTO.setId(orderId);
        submitReqDTO.setChannelCode(PayChannelEnum.WX_LITE.getKey());
        HashMap<String, String> map = new HashMap<>();
        map.put("openid", user.getOpenid());
        submitReqDTO.setChannelExtras(map);
        PayOrderSubmitRespDTO respDTO = payOrderApi.submitOrder(submitReqDTO, ServletUtils.getClientIP());

        String content = respDTO.getDisplayContent();
        AppTempChargeRespVO result = JSONObject.parseObject(content, AppTempChargeRespVO.class);

        return result;
    }

    /**
     * 3、余额充值
     * @param proId
     * @return
     */
    @Override
    public AppRechargeRespVO balanceRecharge(Long proId) throws Exception {

        RechargeDiscountConfigDO config = rechargeDiscountConfigService.getRechargeDiscountConfig(proId);
        if (config == null) {
            throw exception(RECHARGE_DISCOUNT_CONFIG_NOT_EXISTS);
        }

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();

        // 余额充值记录
        String code = serialApi.getCode(PrefixConstants.PREFIX_CZ);
        RechargeRecordDO rechargeRecord = new RechargeRecordDO();

        MbeDO member = mbeService.selectMemberByMpUserId(mpId);
        if (Objects.nonNull(member)) {
            rechargeRecord.setMbeCode(member.getCode());
            rechargeRecord.setMobile(member.getMobile());
        }

        rechargeRecord.setStatus(0);
        rechargeRecord.setMpId(mpId);
        rechargeRecord.setCode(code);
        rechargeRecord.setProId(config.getId());
        rechargeRecord.setAmount(config.getAmount());
        rechargeRecord.setRechargeTime(LocalDateTime.now());
        rechargeRecord.setPaySource(PayChannelEnum.WX_LITE.getValue());
        Long recordId = rechargeRecordService.createRechargeRecord(rechargeRecord);

        // 构造支付参数
        PayOrderCreateReqDTO reqDTO = new PayOrderCreateReqDTO();
        PayAppRespDTO dto = payAppApi.getByName(PayAppConst.XMYSJ);
        reqDTO.setAppId(dto.getId());
        reqDTO.setMerchantOrderId(code);
        reqDTO.setSubject("余额充值");
        reqDTO.setBody("余额充值, code = " + code);
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setType(OrderTypeEnum.ORDER_TYPE_YE.getValue());
        reqDTO.setExpireTime(LocalDateTime.now().plusMinutes(10));      // 10分钟过期
        reqDTO.setPrice(config.getAmount().multiply(BigDecimal.valueOf(100)).intValue());   // 单位：分
        Long orderId = payOrderApi.createOrder(reqDTO);

        MpUserDO user = mpUserService.getUser(mpId);
        PayOrderSubmitReqDTO submitReqDTO = new PayOrderSubmitReqDTO();
        submitReqDTO.setId(orderId);
        submitReqDTO.setChannelCode(PayChannelEnum.WX_LITE.getKey());
        HashMap<String, String> map = new HashMap<>();
        map.put("openid", user.getOpenid());
        submitReqDTO.setChannelExtras(map);
        PayOrderSubmitRespDTO respDTO = payOrderApi.submitOrder(submitReqDTO, ServletUtils.getClientIP());

        String content = respDTO.getDisplayContent();
        AppRechargeRespVO result = JSONObject.parseObject(content, AppRechargeRespVO.class);

        return result;
    }

    /**
     * 4、包月充值
     * @param proId
     * @return
     */
    @Override
    public AppRechargeRespVO monthlyRecharge(Long proId) throws Exception {
        MonthlyPkgConfigDO config = monthlyPkgConfigService.getMonthlyPkgConfig(proId);
        if (config == null) {
            throw exception(MONTHLY_PKG_CONFIG_NOT_EXISTS);
        }

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();

        // 余额充值记录
        String code = serialApi.getCode(PrefixConstants.PREFIX_BY);
        MonthlyPkgRecordDO monthlyPkgRecordDO = new MonthlyPkgRecordDO();

        monthlyPkgRecordDO.setMpId(mpId);
        monthlyPkgRecordDO.setCode(code);
        monthlyPkgRecordDO.setStatus(0);
        monthlyPkgRecordDO.setProId(config.getId());
        monthlyPkgRecordDO.setAmount(config.getPrice());
        monthlyPkgRecordDO.setPurchaseTime(LocalDateTime.now());
        monthlyPkgRecordDO.setPaySource(PayChannelEnum.WX_LITE.getValue());
        monthlyPkgRecordDO.setBelongCommunityId(config.getCommunityId());
        Long recordId = monthlyPkgRecordService.createMonthlyPkgRecord(monthlyPkgRecordDO);

        // 构造支付参数
        PayOrderCreateReqDTO reqDTO = new PayOrderCreateReqDTO();
        PayAppRespDTO dto = payAppApi.getByName(PayAppConst.XMYSJ);
        reqDTO.setAppId(dto.getId());

        reqDTO.setMerchantOrderId(code);
        reqDTO.setSubject("包月充值");
        reqDTO.setBody("包月充值, code = " + code);
        reqDTO.setUserIp(ServletUtils.getClientIP());
        reqDTO.setType(OrderTypeEnum.ORDER_TYPE_BY.getValue());
        reqDTO.setExpireTime(LocalDateTime.now().plusMinutes(10));      // 10分钟过期
        reqDTO.setPrice(config.getPrice().multiply(BigDecimal.valueOf(100)).intValue());   // 单位：分
        Long orderId = payOrderApi.createOrder(reqDTO);

        MpUserDO user = mpUserService.getUser(mpId);
        PayOrderSubmitReqDTO submitReqDTO = new PayOrderSubmitReqDTO();
        submitReqDTO.setId(orderId);
        submitReqDTO.setChannelCode(PayChannelEnum.WX_LITE.getKey());
        HashMap<String, String> map = new HashMap<>();
        map.put("openid", user.getOpenid());
        submitReqDTO.setChannelExtras(map);
        PayOrderSubmitRespDTO respDTO = payOrderApi.submitOrder(submitReqDTO, ServletUtils.getClientIP());

        String content = respDTO.getDisplayContent();
        AppRechargeRespVO result = JSONObject.parseObject(content, AppRechargeRespVO.class);

        return result;
    }

    /**
     * 是否还在充电
     * @param orderId 充电订单id
     * @return Boolean
     */
    @Override
    public Boolean isCharging(Long orderId) {
        ChargeRecordDO chargeRecordDO = chargeRecordService.getChargeRecord(orderId);
        return Objects.nonNull(chargeRecordDO) &&
                chargeRecordDO.getProceedStatus().equals(ChargeRecordDO.PROCEED_STATUS_1);
    }


}
