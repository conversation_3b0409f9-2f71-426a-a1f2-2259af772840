package cn.aguyao.module.charging.controller.admin.device.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10825")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "设备编号")
    @ExcelProperty("设备编号")
    private String device;

    @Schema(description = "联网状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("联网状态")
    private Integer networkStatus;

    @Schema(description = "运行状态", example = "1")
    @ExcelProperty("运行状态")
    private Integer runningStatus;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "小区id", example = "")
    private Long communityId;


    @Schema(description = "楼栋id", example = "")
    private Long buildingId;

    @Schema(description = "目标地址", example = "")
    private String targetAddr;

    ///////////////////////////////////
    @Schema(description = "小区名称", example = "")
    private String communityName;

    @Schema(description = "楼栋名称", example = "")
    private String buildingName;

    @Schema(description = "收费方案", example = "")
    private String schemeName;

}
