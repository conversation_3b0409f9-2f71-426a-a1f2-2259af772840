package cn.aguyao.module.charging.controller.app.servicemanual.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 使用手册新增/修改 Request VO")
@Data
public class AppServiceManualSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6332")
    private Long id;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容，明细")
    private String content;

    @Schema(description = "备注", example = "随便")
    private String remark;

}
