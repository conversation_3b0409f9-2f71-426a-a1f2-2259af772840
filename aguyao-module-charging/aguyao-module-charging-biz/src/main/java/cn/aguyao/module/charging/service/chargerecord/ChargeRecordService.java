package cn.aguyao.module.charging.service.chargerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordDetailRespVO;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordRespVO;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordSaveReqVO;
import cn.aguyao.module.charging.controller.app.chargerecord.vo.AppChargeRecordRespVO;
import cn.aguyao.module.charging.controller.mgr.chargerecord.vo.MgrChargeRecordRespVO;
import cn.aguyao.module.charging.dal.dataobject.chargerecord.ChargeRecordDO;
import com.alibaba.fastjson.JSONObject;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 充电记录，消费记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ChargeRecordService {

    /**
     * 创建充电记录，消费记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createChargeRecord(@Valid ChargeRecordSaveReqVO createReqVO);

    Long createChargeRecord(ChargeRecordDO chargeRecord);

    ChargeRecordDO findByRecordCode(String recordCode);

    Long createOrUpdateChargeRecord(JSONObject msgObj) throws Exception;

    /**
     * 创建充电记录，消费记录
     * @param msgObj 打开端口的消息
     * @return
     */
    Long createChargeRecord(JSONObject msgObj) throws Exception;

    /**
     * 更新充电记录，消费记录
     *
     * @param updateReqVO 更新信息
     */
    void updateChargeRecord(@Valid ChargeRecordSaveReqVO updateReqVO);

    /**
     * 更新充电记录，消费记录
     *
     * @param msgObj 更新信息
     */
    void updateChargeRecord(JSONObject msgObj) throws Exception;


    /**
     * 删除充电记录，消费记录
     *
     * @param id 编号
     */
    void deleteChargeRecord(Long id);

    /**
     * 获得充电记录，消费记录
     *
     * @param id 编号
     * @return 充电记录，消费记录
     */
    ChargeRecordDO getChargeRecord(Long id);

    /**
     * 获得充电记录，消费记录分页
     *
     * @param pageReqVO 分页查询
     * @return 充电记录，消费记录分页
     */
    PageResult<ChargeRecordDO> getChargeRecordPage(ChargeRecordPageReqVO pageReqVO);


    /**
     * 获得充电记录，消费记录分页
     *
     * @param pageReqVO 分页查询
     * @return 充电记录，消费记录分页
     */
    PageResult<MgrChargeRecordRespVO> getChargeRecordPage4Mgr(ChargeRecordPageReqVO pageReqVO);

    /**
     * 获得用户最新的充电记录，消费记录列表
     * @param mpId
     * @return
     */
    ChargeRecordDO lastOneByMpId(Long mpId);

    /**
     * 根据id查询详情
     * @param id
     * @return
     */
    ChargeRecordDetailRespVO detailById(Long id);

    ChargeRecordDO findByDeviceAndPort(String device, Integer port, Integer proceedStatus);

    /**
     * 计算充电结束时间
     * @return
     */
    Boolean calcTimeout(String device, Integer por, Integer timeout, Map<String, String> map) throws Exception;


    /**
     * 保存充电状态
     * @param device
     * @param jsonObject
     */
    void saveChargingStatus2Redis(String device, JSONObject jsonObject) throws Exception;

    void calcChargeAmount(ChargeRecordDO chargeRecordDO, Integer timeout, Integer power);

    /**
     * 支付结果通知
     * @param channelId
     * @param params
     * @param body
     * @return
     */
    Boolean notifyOrder(Long channelId, Map<String, String> params, String body) throws Exception;

    Boolean notifyRefund(Long channelId,  Map<String, String> map, String body) throws Exception;

    /**
     * 根据用户id查询充电记录
     * @param mpId  用户id
     * @param type  充电类型，单次充电或者临时充电
     * @param paySource 支付方式
     * @param proceedStatus 充电状态
     * @return
     */
    ChargeRecordDO findByMpIdAndStatus(Long mpId, int type, Integer[] paySource, int proceedStatus);

    Long getUseNumByCommunityId(Long communityId, Integer[] processStatus);

    /**
     * 根据用户id查询充电记录
     * @param mpId  用户id
     * @param type  充电类型，单次充电或者临时充电
     * @param paySources 支付方式
     * @param proceedStatus 充电状态
     * @param device 设备编号
     * @param port 端口号
     * @return
     */
    ChargeRecordDO findByMpIdAndDeviceInfo(Long mpId, int type, Integer[] paySources, int proceedStatus, String device, Integer port);

    PageResult<AppChargeRecordRespVO> getChargeRecordPage4App(ChargeRecordPageReqVO pageReqVO);

    /**
     * 数据导出
     * @param pageReqVO 查询参数
     * @return 导出数据
     */
    List<ChargeRecordRespVO> getChargeRecord4Export(@Valid ChargeRecordPageReqVO pageReqVO);
}
