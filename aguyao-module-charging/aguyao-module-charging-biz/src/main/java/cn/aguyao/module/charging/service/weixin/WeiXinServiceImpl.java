package cn.aguyao.module.charging.service.weixin;

import cn.aguyao.framework.common.exception.ServerException;
import cn.aguyao.module.charging.controller.admin.mpuser.vo.MpUserSaveReqVO;
import cn.aguyao.module.charging.controller.app.auth.vo.AppAuthLoginRespVO;
import cn.aguyao.module.charging.controller.app.weixin.dto.SessionKeyDTO;
import cn.aguyao.module.charging.controller.app.weixin.vo.AppPhoneNumberReqVO;
import cn.aguyao.module.charging.controller.app.weixin.vo.AppUserInfoReqVO;
import cn.aguyao.module.charging.dal.dataobject.user.MpUserDO;
import cn.aguyao.module.charging.enums.RedisKeyConstants;
import cn.aguyao.module.charging.enums.WeiXinUrlConstants;
import cn.aguyao.module.charging.service.mpuser.MpUserService;
import cn.aguyao.module.charging.util.AESUtil;
import cn.aguyao.module.charging.util.HmacSha256Util;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Log4j2
@Service
public class WeiXinServiceImpl implements WeiXinService {

    @Value("${wx.miniapp.appid}")
    private String appId;

    @Value("${wx.miniapp.secret}")
    private String secret;

    @Resource
    private MpUserService mpUserService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;


    @Override
    public Long createUser(String openid, String unionid, String sessionKey) {
        // 4、保存数据库
        MpUserSaveReqVO reqVO = new MpUserSaveReqVO();
        reqVO.setOpenid(openid);
        reqVO.setUnionId(unionid);
        reqVO.setSessionKey(sessionKey);
        return mpUserService.createUser(reqVO);
    }

    /**
     * 1、获取Session_key
     * 2、获取全局access_token
     * 2、获取用户手机号
     * 3、保存到数据库
     * 4、创建token及refreshToken
     * 5、返回token
     * @param code
     * @return
     */
    @Override
    public AppAuthLoginRespVO createToken(String code) {
//        // 1、获取Session_key
//        SessionKeyDTO dto = this.jscode2session(appId, secret, code);
//        if (dto != null && BooleanUtil.isTrue(dto.getSuccess())) {
//
//            // 2、获取全局access_token(redis -> 微信服务端）
////            String accessToken = redisTemplate.opsForValue().get(RedisKeyConstants.GLOBAL_ACCESS_TOKEN_FOR_USER);
////            if (StrUtil.isBlank(accessToken)) {
////                accessToken = this.getAccessToken(appId, secret);
////            }
//
//            // 3、获取用户手机号 todo
//            String mobile = null;  // this.getPhoneNumber(code, dto.getOpenid(), accessToken);
//
//            // 4、保存数据库
//            MpUserSaveReqVO reqVO = new MpUserSaveReqVO();
//            reqVO.setOpenid(dto.getOpenid());
//            reqVO.setUnionId(dto.getUnionid());
//            reqVO.setSessionKey(dto.getSessionKey());
//            mpUserService.createUser(reqVO);
//
//            // 5、创建token及refreshToken
//
//            // 2、保存到redis todo
//            return new AppWeiXinRespVO(dto.getOpenid(), dto.getSessionKey());
//        }
        return null;
    }



    ////////////////////////////////////////////////////////////////////

    @Override
    public SessionKeyDTO jscode2session(String jsCode) throws Exception {
        return this.jscode2session(appId, secret, jsCode);
    }

    /**
     * jscode2session
     * @param appid 小程序 appId
     * @param secret 小程序 appSecret
     * @param jsCode 登录时获取的 code
     * @return
     */
    @Override
    public SessionKeyDTO jscode2session(String appid, String secret, String jsCode) throws Exception{

        // grant_type	string	是	授权类型，此处只需填写 authorization_code
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appid", appid);
        paramMap.put("secret", secret);
        paramMap.put("js_code", jsCode);
        paramMap.put("grant_type", "authorization_code");
        String result = HttpUtil.get(WeiXinUrlConstants.Applet.JSCODE2SESSION.getUrl(), paramMap);

        log.info("------------------------------------------------");
        log.info("-------- jscode2session result: {}", result);
        log.info("------------------------------------------------");

        SessionKeyDTO dto = JSON.parseObject(result, SessionKeyDTO.class);

        return dto;
    }

    @Override
    public Boolean checkSessionKey(String openid, String sessionKey) {

        String signature = HmacSha256Util.calculateHmacSha256(sessionKey);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("openid", openid);
        paramMap.put("signature", signature);
        paramMap.put("sig_method", "hmac_sha256");
        paramMap.put("access_token", getAccessToken());

        // {"errcode": 0, "errmsg": "ok"}
        String result = HttpUtil.get(WeiXinUrlConstants.Applet.CHECKSESSION.getUrl(), paramMap);
        log.info("-----------------check session key result: {}", result);
        if (StrUtil.isNotBlank(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            Integer errcode = jsonObject.getInteger("errcode");
            if (ObjectUtil.equal(errcode, 0)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public String resetUserSessionKey(String openid, String sessionKey) {
        String signature = HmacSha256Util.calculateHmacSha256(sessionKey, "");

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("openid", openid);
        paramMap.put("signature", signature);
        paramMap.put("sig_method", "hmac_sha256");
        paramMap.put("access_token", getAccessToken());

        // {"errcode": 0, "errmsg": "ok", "openid": "xxxxxxx", "session_key": "xxxxxxxx" }
        String result = HttpUtil.get(WeiXinUrlConstants.Applet.RESETUSERSESSIONKEY.getUrl(), paramMap);
        log.info("获取sessionKey返回的结果result：{}", result);
        if (StrUtil.isNotBlank(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            Integer errcode = jsonObject.getInteger("errcode");
            if (ObjectUtil.equal(errcode, 0)) {
                // 更新数据库
                String skey = jsonObject.getString("session_key");
                MpUserDO mpUserDO = mpUserService.getUserByOpenid(openid);
                mpUserDO.setSessionKey(skey);
                mpUserService.updateUserById(mpUserDO);
                return skey;
            }
        }
        return null;
    }

    /**
     * 获取微信小程序全局的access_token
     * @return
     */
    @Override
    public String getAccessToken() {
        // redis 是否存在，存在并且有效期大于5分钟，则直接返回
        String accessToken = redisTemplate.opsForValue().get(RedisKeyConstants.GLOBAL_ACCESS_TOKEN_FOR_USER);
        long expiresIn = this.getRemainingExpirationTime(RedisKeyConstants.GLOBAL_ACCESS_TOKEN_FOR_USER);
        if (StrUtil.isNotBlank(accessToken) && expiresIn > 300) {
            return accessToken;
        } else {
            return this.refreshAccessToken(appId, secret);
        }
    }

    /**
     * 获取微信小程序全局的access_token
     * @param appid
     * @param secret
     * @return
     */
    @Override
    public String getAccessToken(String appid, String secret) {
        // redis 是否存在，存在并且有效期大于5分钟，则直接返回
        String accessToken = redisTemplate.opsForValue().get(RedisKeyConstants.GLOBAL_ACCESS_TOKEN_FOR_USER);
        long expiresIn = this.getRemainingExpirationTime(RedisKeyConstants.GLOBAL_ACCESS_TOKEN_FOR_USER);
        if (StrUtil.isNotBlank(accessToken) && expiresIn > 300) {
            return accessToken;
        } else {
            return this.refreshAccessToken(appid, secret);
        }
    }

    @Override
    public String refreshAccessToken(String appid, String secret) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("appid", appid);
        paramMap.put("secret", secret);
        paramMap.put("grant_type", "client_credential");
        String result = HttpUtil.get(WeiXinUrlConstants.Applet.TOKEN.getUrl(), paramMap);
        log.info("===========【refreshAccessToken】================result: {}", result);
        if (StrUtil.isNotBlank(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            String accessToken = jsonObject.getString("access_token");
            Integer expiresIn = jsonObject.getInteger("expires_in");
            // 存储到redis, 失效时间为2小时
            redisTemplate.opsForValue().set(RedisKeyConstants.GLOBAL_ACCESS_TOKEN_FOR_USER, accessToken, expiresIn, TimeUnit.SECONDS);

            return accessToken;
        }
        return null;
    }

    /**
     * 获取剩余过期时间
     * @param key， redis key
     * @return
     */
    public long getRemainingExpirationTime(String key) {
        // 获取键的剩余过期时间，单位为秒
        return redisTemplate.getExpire(key);
    }

//    /**
//     * 获取用户手机号
//     * @param code 必填
//     * @param openid 非必填
//     * @param accessToken 必填， 接口调用凭证，该参数为 URL 参数，非 Body 参数
//     * @return
//     */
//    @Override
//    public String getPhoneNumber(String code, String openid, String accessToken) {
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("code", code);
//        paramMap.put("openid", openid);
//
//        /**
//         * {
//         *     "errcode":0,
//         *     "errmsg":"ok",
//         *     "phone_info": {
//         *         "phoneNumber":"xxxxxx",          // 用户绑定的手机号（国外手机号会有区号）
//         *         "purePhoneNumber": "xxxxxx",     // 没有区号的手机号
//         *         "countryCode": 86,               // 区号
//         *         "watermark": {
//         *             "timestamp": 1637744274,
//         *             "appid": "xxxx"
//         *         }
//         *     }
//         * }
//         */
//        String result = HttpUtil.post(WeiXinUrlConstants.Applet.TOKEN.getUrl() + accessToken, paramMap);
//        if (StrUtil.isNotBlank(result)) {
//            JSONObject jsonObject = JSON.parseObject(result);
//            // 是否成功 todo
//            if (Objects.equals(0, jsonObject.getInteger("errcode"))) {
//                JSONObject phone_info = jsonObject.getJSONObject("phone_info");
//                return phone_info.getString("purePhoneNumber");
//            }
//        }
//        return null;
//    }


    @Override
    public String getPhoneNumber(AppPhoneNumberReqVO reqVO) {

        // 从db中获取sessionKey
        MpUserDO mpUserDO = mpUserService.getUserByOpenid(reqVO.getOpenid());
        String sessionKey = mpUserDO.getSessionKey();
        if (!checkSessionKey(mpUserDO.getOpenid(), sessionKey)) {
            // 重置用户session_key
            String skey = this.resetUserSessionKey(mpUserDO.getOpenid(), sessionKey);
            if (StrUtil.isBlank(skey)) {
                throw new ServerException(500, "重置用户session_key失败");
            }
            sessionKey = skey;
        }

        try {

            log.info("==========================111=============================");
            log.info("解密前的数据：{}", JSON.toJSONString(reqVO));
            log.info("==========================222=============================");

            String decrypt = AESUtil.decrypt(sessionKey, reqVO.getIv(), reqVO.getEncryptedData());

            log.info("==========================333=============================");
            log.info("解密后的数据：{}", decrypt);
            log.info("==========================444=============================");

            JSONObject jsonObject = JSON.parseObject(decrypt);

            /**
             * {
             *     "phoneNumber": "13580006666",
             *     "purePhoneNumber": "13580006666",
             *     "countryCode": "86",
             *     "watermark":
             *     {
             *         "appid":"APPID",
             *         "timestamp":TIMESTAMP
             *     }
             * }
             */
            String purePhoneNumber = jsonObject.getString("purePhoneNumber");
            // 更新用户手机号
            mpUserDO.setMobile(purePhoneNumber);
            JSONObject watermarkObj = jsonObject.getJSONObject("watermark");
            mpUserDO.setAppId(watermarkObj.getString("appid"));
            mpUserService.updateUserById(mpUserDO);

            return purePhoneNumber;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public String getUserInfo(AppUserInfoReqVO reqVO) {
        // 保存用户信息到数据库 todo
        System.out.println(reqVO);
        log.info("用户信息：{}", reqVO);
        return null;
    }

    @Override
    public Long createUserIfAbsent(String openid, String unionid, String sessionKey) {
        // 用户已经存在
        MpUserDO user = mpUserService.getUserByOpenid(openid);
        if (user != null) {
            // 更新用户的sessionKey
            if (!StrUtil.equals(sessionKey, user.getSessionKey())) {
                user.setSessionKey(sessionKey);
                mpUserService.updateUserById(user);
            }
            return user.getId();
        }
        // 用户不存在，则进行创建
        return this.createUser(openid, unionid, sessionKey);
    }

}
