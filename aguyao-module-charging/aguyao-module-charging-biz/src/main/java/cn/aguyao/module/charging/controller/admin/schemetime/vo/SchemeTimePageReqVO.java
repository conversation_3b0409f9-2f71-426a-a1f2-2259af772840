package cn.aguyao.module.charging.controller.admin.schemetime.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 收费方案—时段电费分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SchemeTimePageReqVO extends PageParam {

    @Schema(description = "方案id", example = "26113")
    private Long schemeId;

    @Schema(description = "序号")
    private Integer sort;

    @Schema(description = "开始时段，左侧（包含）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] startTime;

    @Schema(description = "结束时段，右侧（不含）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] endTime;

    @Schema(description = "电费 energy price（单位：元）", example = "25235")
    private BigDecimal eprice;

    @Schema(description = "服务费 service price（单位：元）", example = "10524")
    private BigDecimal sprice;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}