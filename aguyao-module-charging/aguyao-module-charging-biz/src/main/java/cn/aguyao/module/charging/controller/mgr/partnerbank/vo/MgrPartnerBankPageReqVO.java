package cn.aguyao.module.charging.controller.mgr.partnerbank.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理端小程序 - 伙伴银行分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MgrPartnerBankPageReqVO extends PageParam {

    @Schema(description = "合作伙伴id", example = "22868")
    private Long partnerId;

    @Schema(description = "银行卡号")
    private String cardNo;

    @Schema(description = "开户行")
    private String bankDeposit;

    @Schema(description = "银行名称", example = "ICBC")
    private String bankName;

    @Schema(description = "真实姓名", example = "张三")
    private String realName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
