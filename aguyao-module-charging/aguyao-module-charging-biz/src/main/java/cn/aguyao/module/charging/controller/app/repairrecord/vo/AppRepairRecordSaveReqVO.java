package cn.aguyao.module.charging.controller.app.repairrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 报修记录新增/修改 Request VO")
@Data
public class AppRepairRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "21553")
    private Long id;

    @Schema(description = "保留记录编号")
    private String code;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "报修小区id", example = "30287")
    private Long communityId;

    @Schema(description = "楼栋id", example = "4449")
    private Long buildingId;

    @Schema(description = "设备编号")
    private String deviceCode;

    @Schema(description = "维修状态", example = "2")
    private Integer repairStatus;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

}
