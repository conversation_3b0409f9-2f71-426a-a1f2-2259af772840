package cn.aguyao.module.charging.service.platforminfo;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.platforminfo.vo.PlatformInfoPageReqVO;
import cn.aguyao.module.charging.controller.admin.platforminfo.vo.PlatformInfoSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.platforminfo.PlatformInfoDO;
import cn.aguyao.module.charging.dal.mysql.platforminfo.PlatformInfoMapper;
import cn.aguyao.module.charging.enums.PrefixConstants;
import cn.aguyao.module.system.api.serial.SerialApi;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.PLATFORM_INFO_NOT_EXISTS;

/**
 * 平台信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PlatformInfoServiceImpl implements PlatformInfoService {

    @Resource
    private PlatformInfoMapper platformInfoMapper;

    @Resource
    private SerialApi serialApi;

    @Override
    public Long createPlatformInfo(PlatformInfoSaveReqVO createReqVO) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_PTXX);
        // 插入
        PlatformInfoDO platformInfo = BeanUtils.toBean(createReqVO, PlatformInfoDO.class);
        platformInfo.setCode(code);
        platformInfoMapper.insert(platformInfo);
        // 返回
        return platformInfo.getId();
    }

    @Override
    public void updatePlatformInfo(PlatformInfoSaveReqVO updateReqVO) {
        // 校验存在
        validatePlatformInfoExists(updateReqVO.getId());
        // 更新
        PlatformInfoDO updateObj = BeanUtils.toBean(updateReqVO, PlatformInfoDO.class);
        platformInfoMapper.updateById(updateObj);
    }

    @Override
    public void deletePlatformInfo(Long id) {
        // 校验存在
        validatePlatformInfoExists(id);
        // 删除
        platformInfoMapper.deleteById(id);
    }

    private void validatePlatformInfoExists(Long id) {
        if (platformInfoMapper.selectById(id) == null) {
            throw exception(PLATFORM_INFO_NOT_EXISTS);
        }
    }

    @Override
    public PlatformInfoDO getPlatformInfo(Long id) {
        return platformInfoMapper.selectById(id);
    }

    @Override
    public PageResult<PlatformInfoDO> getPlatformInfoPage(PlatformInfoPageReqVO pageReqVO) {
        return platformInfoMapper.selectPage(pageReqVO);
    }

    @Override
    public Map<String, List<String>> phoneList() {
        List<PlatformInfoDO> list = platformInfoMapper.phoneList();
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        List<String> phones = list.stream()
                .map(PlatformInfoDO::getCustomerServicePhone)
                .collect(Collectors.toList());
        Map<String, List<String>> map = new HashMap<>();
        map.put("hotline", phones);

        return map;
    }

}
