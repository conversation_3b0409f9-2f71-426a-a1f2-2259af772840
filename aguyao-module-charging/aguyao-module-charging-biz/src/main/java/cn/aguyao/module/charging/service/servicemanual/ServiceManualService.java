package cn.aguyao.module.charging.service.servicemanual;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.servicemanual.vo.ServiceManualPageReqVO;
import cn.aguyao.module.charging.controller.admin.servicemanual.vo.ServiceManualSaveReqVO;
import cn.aguyao.module.charging.controller.app.servicemanual.vo.AppServiceManualPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.servicemanual.ServiceManualDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 使用手册 Service 接口
 *
 * <AUTHOR>
 */
public interface ServiceManualService {

    /**
     * 创建使用手册
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createServiceManual(@Valid ServiceManualSaveReqVO createReqVO);

    /**
     * 更新使用手册
     *
     * @param updateReqVO 更新信息
     */
    void updateServiceManual(@Valid ServiceManualSaveReqVO updateReqVO);

    /**
     * 删除使用手册
     *
     * @param id 编号
     */
    void deleteServiceManual(Long id);

    /**
     * 获得使用手册
     *
     * @param id 编号
     * @return 使用手册
     */
    ServiceManualDO getServiceManual(Long id);

    /**
     * 获得使用手册分页
     *
     * @param pageReqVO 分页查询
     * @return 使用手册分页
     */
    PageResult<ServiceManualDO> getServiceManualPage(ServiceManualPageReqVO pageReqVO);

    /**
     * 获得使用手册列表
     * @param pageReqVO
     * @return
     */
    List<ServiceManualDO> list(AppServiceManualPageReqVO pageReqVO);
}
