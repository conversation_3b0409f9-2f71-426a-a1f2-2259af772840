package cn.aguyao.module.charging.controller.app.weixin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "微信 APP - 请求获取微信手机号 VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppPhoneNumberReqVO {

    @Schema(description = "初始化向量", example = "7bjLnBRegagIaotighmavQ==")
    private String iv;

    @Schema(description = "手机号获取凭证", example = "88f3f2036d039abef461340e50123b13e25db5cfdd573e9769c15a6041da1acc")
    private String code;

    @Schema(description = "加密过的用户敏感信息")
    private String encryptedData;


    @Schema(description = "用户唯一标识")
    private String openid;
}
