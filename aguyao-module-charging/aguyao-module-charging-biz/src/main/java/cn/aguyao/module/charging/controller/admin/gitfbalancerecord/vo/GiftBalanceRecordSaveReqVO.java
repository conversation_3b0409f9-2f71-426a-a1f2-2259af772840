package cn.aguyao.module.charging.controller.admin.gitfbalancerecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 赠送金额记录新增/修改 Request VO")
@Data
public class GiftBalanceRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15208")
    private Long id;

    @Schema(description = "会员id", example = "21489")
    private Long mbeId;

    private Long mpId;

    private Integer type;

    private BigDecimal amt;

}
