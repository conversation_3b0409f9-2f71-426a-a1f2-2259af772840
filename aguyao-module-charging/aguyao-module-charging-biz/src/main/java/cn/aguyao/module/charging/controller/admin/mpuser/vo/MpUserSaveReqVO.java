package cn.aguyao.module.charging.controller.admin.mpuser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 微信小程序粉丝新增/修改 Request VO")
@Data
public class MpUserSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6765")
    private Long id;

    @Schema(description = "公众号 appId", example = "16844")
    private String appId;

    @Schema(description = "用户唯一标识", example = "16176")
    private String openid;

    @Schema(description = "微信生态唯一标识", example = "9495")
    private String unionId;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "昵称", example = "张三")
    private String nickname;

    @Schema(description = "头像地址", example = "https://www.iocoder.cn")
    private String headImageUrl;

    @Schema(description = "语言")
    private String language;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "关注状态, 1. 开启 - 已关注; 2. 禁用 - 取消关注", example = "2")
    private Integer subscribeStatus;

    @Schema(description = "关注时间")
    private LocalDateTime subscribeTime;

    @Schema(description = "取消关注时间")
    private LocalDateTime unsubscribeTime;


    /**
     * 会话密钥 session_key 是对用户数据进行 加密签名 的密钥
     */
    @Schema(description = "会话密钥 session_key")
    private String sessionKey;

}
