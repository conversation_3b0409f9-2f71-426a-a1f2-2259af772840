package cn.aguyao.module.charging.controller.admin.schemepower.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 收费方案—功率档位 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SchemePowerRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26629")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "方案id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7111")
    @ExcelProperty("方案id")
    private Long schemeId;

    @Schema(description = "序号")
    @ExcelProperty("序号")
    private Integer sort;

    @Schema(description = "功率区间，左侧（包含）")
    @ExcelProperty("功率区间，左侧（包含）")
    private Integer startPower;

    @Schema(description = "功率区间，右侧（不含）")
    @ExcelProperty("功率区间，右侧（不含）")
    private Integer endPower;

    @Schema(description = "收费标准（单位：元）")
    @ExcelProperty("收费标准（单位：元）")
    private BigDecimal amount;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}