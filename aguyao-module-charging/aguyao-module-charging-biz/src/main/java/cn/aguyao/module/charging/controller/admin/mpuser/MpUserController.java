package cn.aguyao.module.charging.controller.admin.mpuser;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.mpuser.vo.MpUserPageReqVO;
import cn.aguyao.module.charging.controller.admin.mpuser.vo.MpUserRespVO;
import cn.aguyao.module.charging.controller.admin.mpuser.vo.MpUserSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.user.MpUserDO;
import cn.aguyao.module.charging.service.mpuser.MpUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 微信小程序粉丝")
@RestController
@RequestMapping("/mp/user")
@Validated
public class MpUserController {

    @Resource
    private MpUserService userService;

    @PostMapping("/create")
    @Operation(summary = "创建微信小程序粉丝")
    @PreAuthorize("@ss.hasPermission('mp:user:create')")
    public CommonResult<Long> createUser(@Valid @RequestBody MpUserSaveReqVO createReqVO) {
        return success(userService.createUser(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新微信小程序粉丝")
    @PreAuthorize("@ss.hasPermission('mp:user:update')")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody MpUserSaveReqVO updateReqVO) {
        userService.updateUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除微信小程序粉丝")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('mp:user:delete')")
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id) {
        userService.deleteUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得微信小程序粉丝")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('mp:user:query')")
    public CommonResult<MpUserRespVO> getUser(@RequestParam("id") Long id) {
        MpUserDO user = userService.getUser(id);
        return success(BeanUtils.toBean(user, MpUserRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得微信小程序粉丝分页")
    @PreAuthorize("@ss.hasPermission('mp:user:query')")
    public CommonResult<PageResult<MpUserRespVO>> getUserPage(@Valid MpUserPageReqVO pageReqVO) {
        PageResult<MpUserDO> pageResult = userService.getUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MpUserRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出微信小程序粉丝 Excel")
    @PreAuthorize("@ss.hasPermission('mp:user:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUserExcel(@Valid MpUserPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MpUserDO> list = userService.getUserPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "微信小程序粉丝.xls", "数据", MpUserRespVO.class,
                        BeanUtils.toBean(list, MpUserRespVO.class));
    }

}
