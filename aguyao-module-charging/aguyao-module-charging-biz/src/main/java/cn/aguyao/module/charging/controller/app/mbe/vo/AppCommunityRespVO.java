package cn.aguyao.module.charging.controller.app.mbe.vo;

import cn.aguyao.module.charging.dal.dataobject.community.CommunityDO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 小区，即充电的用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppCommunityRespVO {

    /**
     * 字母
     */
    private String letter;

    /**
     * 小区列表
     */
    private List<CommunityDO> list;
}
