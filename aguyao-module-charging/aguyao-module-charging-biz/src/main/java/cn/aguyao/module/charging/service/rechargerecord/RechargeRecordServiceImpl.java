package cn.aguyao.module.charging.service.rechargerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.rechargerecord.vo.RechargeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.rechargerecord.vo.RechargeRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.rechargerecord.RechargeRecordDO;
import cn.aguyao.module.charging.dal.mysql.rechargerecord.RechargeRecordMapper;
import cn.aguyao.module.charging.enums.OrderStatusEnum;
import cn.aguyao.module.charging.enums.PrefixConstants;
import cn.aguyao.module.charging.service.mbe.MbeService;
import cn.aguyao.module.pay.api.order.dto.PayOrderNotifyRespDTO;
import cn.aguyao.module.system.api.serial.SerialApi;
import com.alibaba.fastjson.JSON;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.RECHARGE_RECORD_NOT_EXISTS;

/**
 * 充值记录 Service 实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Validated
public class RechargeRecordServiceImpl implements RechargeRecordService {

    @Resource
    private RechargeRecordMapper rechargeRecordMapper;

    @Resource
    private SerialApi serialApi;

    @Resource
    private MbeService mbeService;


    @Override
    public Long createRechargeRecord(RechargeRecordSaveReqVO createReqVO) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_CZ);
        // 插入
        RechargeRecordDO rechargeRecord = BeanUtils.toBean(createReqVO, RechargeRecordDO.class);
        rechargeRecord.setCode(code);
        rechargeRecordMapper.insert(rechargeRecord);
        // 返回
        return rechargeRecord.getId();
    }

    @Override
    public Long createRechargeRecord(RechargeRecordDO rechargeRecord) {
        rechargeRecordMapper.insert(rechargeRecord);
        return rechargeRecord.getId();
    }

    @Override
    public void updateRechargeRecord(RechargeRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateRechargeRecordExists(updateReqVO.getId());
        // 更新
        RechargeRecordDO updateObj = BeanUtils.toBean(updateReqVO, RechargeRecordDO.class);
        rechargeRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteRechargeRecord(Long id) {
        // 校验存在
        validateRechargeRecordExists(id);
        // 删除
        rechargeRecordMapper.deleteById(id);
    }

    private void validateRechargeRecordExists(Long id) {
        if (rechargeRecordMapper.selectById(id) == null) {
            throw exception(RECHARGE_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public RechargeRecordDO getRechargeRecord(Long id) {
        return rechargeRecordMapper.selectById(id);
    }

    @Override
    public PageResult<RechargeRecordDO> getRechargeRecordPage(RechargeRecordPageReqVO pageReqVO) {
        return rechargeRecordMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notifyHandle(PayOrderNotifyRespDTO respDTO) {
        RechargeRecordDO entity = this.findByCode(respDTO.getOutTradeNo());
        if (Objects.isNull(entity)) {
            log.info("notifyHandle充值记录不存在，不再处理， code = {}", respDTO.getOutTradeNo());
            return ;
        }

        // 如果已经通知过了，则不处理
        if (Objects.equals(entity.getStatus(),OrderStatusEnum.PAID.getValue())) {
            log.info("notifyHandle充值记录已经通知过了，不再处理");
            return ;
        }

        log.info("notifyHandle充值记录处理中， success = {}", respDTO.getSuccess());
        if (respDTO.getSuccess()) {
            // 充值成功
            entity.setRemark("充值成功");
            entity.setStatus(OrderStatusEnum.PAID.getValue());

            // 更新会员余额
            mbeService.updateMbeBalance(entity.getMpId(), entity.getProId());
            log.info("-----------更新会员余额完成");
        } else {
            // 充值失败
            entity.setRemark("充值失败");
            entity.setStatus(OrderStatusEnum.PAY_FAIL.getValue());
        }

        entity.setUpdateTime(LocalDateTime.now());

        log.info("开始更新充值记录处理完成");
        log.info("----(待更新)充值记录最后的状态 entity = {}", JSON.toJSONString(entity));
        rechargeRecordMapper.updateById(entity);
        log.info("---------------更新充值记录处理完成, entity = {}", JSON.toJSONString(entity));
    }

    @Override
    public RechargeRecordDO findByCode(String code) {
        return rechargeRecordMapper.selectByCode(code);
    }

    @Override
    public List<RechargeRecordDO> findByMpId(Long mpId) {
        return rechargeRecordMapper.selectByMpId(mpId);
    }

}
