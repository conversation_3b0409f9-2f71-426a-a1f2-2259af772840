package cn.aguyao.module.charging.dal.mysql.insureconfig;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.insureconfig.vo.InsureConfigPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.insureconfig.InsureConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 投保配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InsureConfigMapper extends BaseMapperX<InsureConfigDO> {

    default PageResult<InsureConfigDO> selectPage(InsureConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InsureConfigDO>()
                .likeIfPresent(InsureConfigDO::getCode, reqVO.getCode())
                .likeIfPresent(InsureConfigDO::getTitle, reqVO.getTitle())
                .likeIfPresent(InsureConfigDO::getContent, reqVO.getContent())
                .eqIfPresent(InsureConfigDO::getRemark, reqVO.getRemark())
                .eqIfPresent(InsureConfigDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(InsureConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InsureConfigDO::getId));
    }

}
