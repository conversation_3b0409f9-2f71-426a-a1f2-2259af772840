package cn.aguyao.module.charging.controller.app.station.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Schema(description = "小程序端 - 电站 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppStationPageReqVO extends PageParam {

    @NotBlank(message = "经度不能为空")
    @Schema(description = "经度")
    private String longitude;

    @NotBlank(message = "纬度不能为空")
    @Schema(description = "纬度")
    private String latitude;

}
