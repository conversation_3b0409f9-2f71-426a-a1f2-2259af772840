package cn.aguyao.module.charging.dal.mysql.monthlypkgrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo.MonthlyPkgRecordPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgrecord.MonthlyPkgRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;

/**
 * 包月记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MonthlyPkgRecordMapper extends BaseMapperX<MonthlyPkgRecordDO> {

    default PageResult<MonthlyPkgRecordDO> selectPage(MonthlyPkgRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MonthlyPkgRecordDO>()
                .likeIfPresent(MonthlyPkgRecordDO::getCode, reqVO.getCode())
                .likeIfPresent(MonthlyPkgRecordDO::getMbeCode, reqVO.getMbeCode())
                .likeIfPresent(MonthlyPkgRecordDO::getMobile, reqVO.getMobile())
                .eqIfPresent(MonthlyPkgRecordDO::getAmount, reqVO.getAmount())
                .betweenIfPresent(MonthlyPkgRecordDO::getPurchaseTime, reqVO.getPurchaseTime())
                .eqIfPresent(MonthlyPkgRecordDO::getBelongCommunityId, reqVO.getBelongCommunityId())
                .eqIfPresent(MonthlyPkgRecordDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MonthlyPkgRecordDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(MonthlyPkgRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MonthlyPkgRecordDO::getId));
    }

    @Select("SELECT * FROM charging_monthly_pkg_record WHERE mp_id = #{mpId} AND expire_time > #{expireTime} order by purchase_time asc limit 1")
    MonthlyPkgRecordDO findOneByMpId(@Param("mpId") Long mpId, @Param("expireTime") LocalDateTime expireTime);

    default MonthlyPkgRecordDO findByCode(String code) {
        return selectOne(new LambdaQueryWrapperX<MonthlyPkgRecordDO>()
                .eq(MonthlyPkgRecordDO::getCode, code));
    }
}
