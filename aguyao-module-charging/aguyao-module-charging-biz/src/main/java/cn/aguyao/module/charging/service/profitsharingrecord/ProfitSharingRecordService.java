package cn.aguyao.module.charging.service.profitsharingrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo.ProfitSharingRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo.ProfitSharingRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.profitsharingrecord.ProfitSharingRecordDO;

import javax.validation.Valid;

/**
 * 分润记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ProfitSharingRecordService {

    /**
     * 创建分润记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProfitSharingRecord(@Valid ProfitSharingRecordSaveReqVO createReqVO);

    /**
     * 更新分润记录
     *
     * @param updateReqVO 更新信息
     */
    void updateProfitSharingRecord(@Valid ProfitSharingRecordSaveReqVO updateReqVO);

    /**
     * 删除分润记录
     *
     * @param id 编号
     */
    void deleteProfitSharingRecord(Long id);

    /**
     * 获得分润记录
     *
     * @param id 编号
     * @return 分润记录
     */
    ProfitSharingRecordDO getProfitSharingRecord(Long id);

    /**
     * 获得分润记录分页
     *
     * @param pageReqVO 分页查询
     * @return 分润记录分页
     */
    PageResult<ProfitSharingRecordDO> getProfitSharingRecordPage(ProfitSharingRecordPageReqVO pageReqVO);

}