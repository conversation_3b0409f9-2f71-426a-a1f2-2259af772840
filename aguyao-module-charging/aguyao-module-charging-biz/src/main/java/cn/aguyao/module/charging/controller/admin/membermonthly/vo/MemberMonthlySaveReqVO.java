package cn.aguyao.module.charging.controller.admin.membermonthly.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户和小区包月的关系新增/修改 Request VO")
@Data
public class MemberMonthlySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24945")
    private Long id;

    @Schema(description = "微信用户id", example = "30072")
    private Long mpId;

    @Schema(description = "会员id", example = "13017")
    private Long mbeId;

    @Schema(description = "小区id", example = "3950")
    private Long communityId;

    @Schema(description = "失效时间")
    private LocalDateTime expiration;

}
