package cn.aguyao.module.charging.controller.admin.community.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 小区新增/修改 Request VO")
@Data
public class CommunitySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1376")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "小区名称", example = "赵六")
    private String name;

    @Schema(description = "楼栋数")
    private Integer buildingsNum;

    @Schema(description = "设备数")
    private Integer deviceNum;

    @Schema(description = "用户数，会员数")
    private Integer mbeNum;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "合作方id")
    private Long partnerId;

    @Schema(description = "地址")
    private String address;

//    @Schema(description = "方案id")
//    private String schemeId;

    @Schema(description = "费率")
    private String rate;

    @Schema(description = "分润点数")
    private BigDecimal profitSharingPoints;
}
