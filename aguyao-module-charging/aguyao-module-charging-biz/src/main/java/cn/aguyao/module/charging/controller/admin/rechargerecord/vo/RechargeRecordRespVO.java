package cn.aguyao.module.charging.controller.admin.rechargerecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 充值记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RechargeRecordRespVO {

//    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "22924")
//    @ExcelProperty("主键")
//    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "用户编号")
    @ExcelProperty("用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    @ExcelProperty("用户手机号")
    private String mobile;

    @Schema(description = "消费金额")
    @ExcelProperty("消费金额")
    private BigDecimal amount;

    @Schema(description = "充值时间")
    @ExcelProperty("充值时间")
    private LocalDateTime rechargeTime;

//    @Schema(description = "支付来源，消费来源")
//    @ExcelProperty("支付来源，消费来源")
//    private Integer paySource;

    @Schema(description = "支付来源，消费来源")
    @ExcelProperty("支付来源，消费来源")
    private String paySourceStr;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

//    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @ExcelProperty("帐号状态（0正常 1停用）")
//    private Integer status;
//
    @Schema(description = "充值状态", example = "充值成功")
    @ExcelProperty("充值状态")
    private String statusStr;

//    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("创建时间")
//    private LocalDateTime createTime;

}
