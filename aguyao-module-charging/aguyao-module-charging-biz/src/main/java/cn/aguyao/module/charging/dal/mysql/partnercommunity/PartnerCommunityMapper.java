package cn.aguyao.module.charging.dal.mysql.partnercommunity;

import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.module.charging.dal.dataobject.partnercommunity.PartnerCommunityDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 商户——小区管理人员（物业） Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PartnerCommunityMapper extends BaseMapperX<PartnerCommunityDO> {

    default List<PartnerCommunityDO> getListByMpId(Long mpId) {
        return selectList(PartnerCommunityDO::getMpId, mpId);
    }

    default int deleteByMpId(Long mpId) {
        return delete(PartnerCommunityDO::getMpId, mpId);
    }
}
