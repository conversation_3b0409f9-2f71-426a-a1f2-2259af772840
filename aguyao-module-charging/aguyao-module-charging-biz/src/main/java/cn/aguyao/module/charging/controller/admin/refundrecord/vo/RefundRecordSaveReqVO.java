package cn.aguyao.module.charging.controller.admin.refundrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 退款记录新增/修改 Request VO")
@Data
public class RefundRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13144")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "申请金额")
    private BigDecimal applyAmount;

    @Schema(description = "退款成功金额")
    private BigDecimal refundSuccessAmount;

    @Schema(description = "申请状态", example = "2")
    private Integer applyStatus;

    @Schema(description = "审批状态", example = "1")
    private Integer approvalStatus;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "原因", example = "太贵了")
    private String reason;

    @Schema(description = "退款类型", example = "1")
    private Integer type;

    @Schema(description = "微信用户id", example = "1")
    private Long mpId;

    @Schema(description = "订单号", example = "1")
    private String orderNo;


    /**
     * 退款状态
     */
    @Schema(description = "退款状态", example = "1")
    private Integer status;
}
