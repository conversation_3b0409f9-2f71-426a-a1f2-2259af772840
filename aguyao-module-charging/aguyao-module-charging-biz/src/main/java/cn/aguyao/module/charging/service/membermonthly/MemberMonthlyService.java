package cn.aguyao.module.charging.service.membermonthly;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.membermonthly.vo.MemberMonthlyPageReqVO;
import cn.aguyao.module.charging.controller.admin.membermonthly.vo.MemberMonthlySaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.membermonthly.MemberMonthlyDO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgrecord.MonthlyPkgRecordDO;

import javax.validation.Valid;

/**
 * 用户和小区包月的关系 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberMonthlyService {

    /**
     * 创建用户和小区包月的关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMemberMonthly(@Valid MemberMonthlySaveReqVO createReqVO);

    /**
     * 更新用户和小区包月的关系
     *
     * @param updateReqVO 更新信息
     */
    void updateMemberMonthly(@Valid MemberMonthlySaveReqVO updateReqVO);

    /**
     * 删除用户和小区包月的关系
     *
     * @param id 编号
     */
    void deleteMemberMonthly(Long id);

    /**
     * 获得用户和小区包月的关系
     *
     * @param id 编号
     * @return 用户和小区包月的关系
     */
    MemberMonthlyDO getMemberMonthly(Long id);

    /**
     * 获得用户和小区包月的关系分页
     *
     * @param pageReqVO 分页查询
     * @return 用户和小区包月的关系分页
     */
    PageResult<MemberMonthlyDO> getMemberMonthlyPage(MemberMonthlyPageReqVO pageReqVO);

    /**
     * MonthlyPkgRecordDO entity
     * @param entity
     */
    void createOrUpdateMemberMonthly(MonthlyPkgRecordDO entity);

    MemberMonthlyDO findByMpIdAndCommunityId(Long mpId, Long communityId);
}
