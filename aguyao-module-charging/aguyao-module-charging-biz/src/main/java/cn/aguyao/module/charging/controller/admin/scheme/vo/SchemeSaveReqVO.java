package cn.aguyao.module.charging.controller.admin.scheme.vo;

import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectSaveReqVO;
import cn.aguyao.module.charging.controller.admin.schemepower.vo.SchemePowerSaveReqVO;
import cn.aguyao.module.charging.controller.admin.schemetime.vo.SchemeTimeSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 收费方案新增/修改 Request VO")
@Data
public class SchemeSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "22942")
    private Long id;

    @Schema(description = "方案名称", example = "芋艿")
    private String name;

    @Schema(description = "方案描述")
    private String description;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @NotNull(message = "计费方式不能为空")
    @Schema(description = "计费方式", example = "1")
    private Integer type;

    ///////////按时间/////////////
    @Schema(description = "按时间—功率档位", example = "按时间—功率档位")
    private List<SchemePowerSaveReqVO> powerItems;

    @Schema(description = "按时间—收费套餐", example = "按时间—收费套餐")
    private List<SchemeCollectSaveReqVO> setmealItems1;

    ///////////按电量/////////////
    @Schema(description = "按电量—时段", example = "按电量—时段")
    private List<SchemeTimeSaveReqVO> timeItems;

    @Schema(description = "按电量—收费套餐", example = "按电量—收费套餐")
    private List<SchemeCollectSaveReqVO> setmealItems2;


    ///////////按电量2.0/////////////
    @Schema(description = "按电量—时段", example = "按电量—时段")
    private List<SchemeTimeSaveReqVO> timeItems3;

    private List<SchemePowerSaveReqVO> powerItems3;

    @Schema(description = "按时间—收费套餐", example = "按时间—收费套餐")
    private List<SchemeCollectSaveReqVO> setmealItems3;
}
