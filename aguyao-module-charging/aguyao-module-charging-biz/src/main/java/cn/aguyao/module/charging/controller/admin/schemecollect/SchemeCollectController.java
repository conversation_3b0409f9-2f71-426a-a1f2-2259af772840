package cn.aguyao.module.charging.controller.admin.schemecollect;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectPageReqVO;
import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectRespVO;
import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.schemecollect.SchemeCollectDO;
import cn.aguyao.module.charging.service.schemecollect.SchemeCollectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 收费方案—收费套餐")
@RestController
@RequestMapping("/charging/scheme-collect")
@Validated
public class SchemeCollectController {

    @Resource
    private SchemeCollectService schemeCollectService;

    @PostMapping("/create")
    @Operation(summary = "创建收费方案—收费套餐")
    @PreAuthorize("@ss.hasPermission('charging:scheme-collect:create')")
    public CommonResult<Long> createSchemeCollect(@Valid @RequestBody SchemeCollectSaveReqVO createReqVO) {
        return success(schemeCollectService.createSchemeCollect(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新收费方案—收费套餐")
    @PreAuthorize("@ss.hasPermission('charging:scheme-collect:update')")
    public CommonResult<Boolean> updateSchemeCollect(@Valid @RequestBody SchemeCollectSaveReqVO updateReqVO) {
        schemeCollectService.updateSchemeCollect(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除收费方案—收费套餐")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:scheme-collect:delete')")
    public CommonResult<Boolean> deleteSchemeCollect(@RequestParam("id") Long id) {
        schemeCollectService.deleteSchemeCollect(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得收费方案—收费套餐")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:scheme-collect:query')")
    public CommonResult<SchemeCollectRespVO> getSchemeCollect(@RequestParam("id") Long id) {
        SchemeCollectDO schemeCollect = schemeCollectService.getSchemeCollect(id);
        return success(BeanUtils.toBean(schemeCollect, SchemeCollectRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得收费方案—收费套餐分页")
    @PreAuthorize("@ss.hasPermission('charging:scheme-collect:query')")
    public CommonResult<PageResult<SchemeCollectRespVO>> getSchemeCollectPage(@Valid SchemeCollectPageReqVO pageReqVO) {
        PageResult<SchemeCollectDO> pageResult = schemeCollectService.getSchemeCollectPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SchemeCollectRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出收费方案—收费套餐 Excel")
    @PreAuthorize("@ss.hasPermission('charging:scheme-collect:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSchemeCollectExcel(@Valid SchemeCollectPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SchemeCollectDO> list = schemeCollectService.getSchemeCollectPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "收费方案—收费套餐.xls", "数据", SchemeCollectRespVO.class,
                        BeanUtils.toBean(list, SchemeCollectRespVO.class));
    }

}