package cn.aguyao.module.charging.controller.app.rechargediscountconfig.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 充值优惠配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppRechargeDiscountConfigRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12503")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "优惠编号")
    @ExcelProperty("优惠编号")
    private String code;

    @Schema(description = "金额")
    @ExcelProperty("金额")
    private BigDecimal amount;

    @Schema(description = "赠送金额")
    @ExcelProperty("赠送金额")
    private BigDecimal giftAmount;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sort;

}
