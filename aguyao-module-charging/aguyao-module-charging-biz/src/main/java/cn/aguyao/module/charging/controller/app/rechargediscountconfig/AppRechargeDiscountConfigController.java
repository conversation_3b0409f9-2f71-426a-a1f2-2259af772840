package cn.aguyao.module.charging.controller.app.rechargediscountconfig;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.app.rechargediscountconfig.vo.AppRechargeDiscountConfigRespVO;
import cn.aguyao.module.charging.dal.dataobject.rechargediscountconfig.RechargeDiscountConfigDO;
import cn.aguyao.module.charging.service.rechargediscountconfig.RechargeDiscountConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 充值优惠配置")
@RestController
@RequestMapping("/charging/recharge-discount-config")
@Validated
public class AppRechargeDiscountConfigController {

    @Resource
    private RechargeDiscountConfigService rechargeDiscountConfigService;


    /**
     * 获取充值优惠配置列表
     * @return
     */
    @GetMapping("/list")
    @Operation(summary = "获得充值优惠配置分页")
    public CommonResult<List<AppRechargeDiscountConfigRespVO>> list() {
        List<RechargeDiscountConfigDO> list = rechargeDiscountConfigService.list();
        return success(BeanUtils.toBean(list, AppRechargeDiscountConfigRespVO.class));
    }


}
