package cn.aguyao.module.charging.dal.mysql.device;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.framework.mybatis.core.query.QueryWrapperX;
import cn.aguyao.module.charging.controller.admin.device.vo.DevicePageReqVO;
import cn.aguyao.module.charging.controller.mgr.device.vo.MgrDeviceCountRespVO;
import cn.aguyao.module.charging.dal.dataobject.device.DeviceDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceMapper extends BaseMapperX<DeviceDO> {

    default PageResult<DeviceDO> selectPage(DevicePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceDO>()
                .likeIfPresent(DeviceDO::getCode, reqVO.getCode())
                .eqIfPresent(DeviceDO::getNetworkStatus, reqVO.getNetworkStatus())
                .eqIfPresent(DeviceDO::getRunningStatus, reqVO.getRunningStatus())
                .eqIfPresent(DeviceDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(DeviceDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(DeviceDO::getDevice, reqVO.getDevice())
                .orderByDesc(DeviceDO::getId));
    }

    IPage<DeviceDO> findPage(IPage iPage, @Param("reqVO") DevicePageReqVO reqVO);

    default DeviceDO findByCode(String code) {
        QueryWrapperX query = new QueryWrapperX<DeviceDO>();
        query.eq("code", code);
        query.or();
        query.eq("device", code);
        query.limitN(1);

        return selectOne(query);
    }

    default DeviceDO findByDevice(String device) {
        return selectOne(new LambdaQueryWrapperX<DeviceDO>()
                .eq(DeviceDO::getDevice, device));
    }

    /**
     * 设备数量统计
     * @param communityId
     * @return
     */
    @Select("SELECT " +
            "    community_id," +
            "    SUM(CASE WHEN running_status = 0 THEN 1 ELSE 0 END) AS idleCount," +
            "    SUM(CASE WHEN running_status = 1 THEN 1 ELSE 0 END) AS occupiedCount," +
            "    SUM(CASE WHEN running_status = 2 THEN 1 ELSE 0 END) AS faultCount," +
            "    SUM(CASE WHEN running_status = 3 THEN 1 ELSE 0 END) AS offlineCount" +
            " FROM " +
            "    charging_device t1 " +
            " left join charging_community t2 on t2.id = t1.community_id" +
            " where 1=1 and t1.community_id = #{communityId}" +
            " GROUP BY community_id" )
    MgrDeviceCountRespVO deviceCountByCommunityId(Long communityId);

    default List<DeviceDO> findByCommunityId(Long communityId) {
        return selectList(new LambdaQueryWrapperX<DeviceDO>()
                .eq(DeviceDO::getCommunityId, communityId));
    }

    /**
     * 小区设备使用量
     * @param communityId
     * @return
     */
    Integer useageAmt(@Param("communityId") Long communityId, @Param("startTime") LocalDateTime startTime);

    default List<DeviceDO> findByBuildingId(Long buildingId) {
        return selectList(new LambdaQueryWrapperX<DeviceDO>()
                .eq(DeviceDO::getBuildingId, buildingId));
    }

}
