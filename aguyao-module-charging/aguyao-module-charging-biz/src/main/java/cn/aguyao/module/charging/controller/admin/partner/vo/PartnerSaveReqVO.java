package cn.aguyao.module.charging.controller.admin.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 合作伙伴——小区管理人员（物业）新增/修改 Request VO")
@Data
public class PartnerSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16337")
    private Long id;

    /**
     * 微信小程序 ID
     */
    private Long mpId;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "名称", example = "芋艿")
    private String name;

    @Schema(description = "分润点数")
    private BigDecimal profitSharingPoints;

    @Schema(description = "历史分润")
    private BigDecimal historicalProfitSharing;

    @Schema(description = "已提现分润")
    private BigDecimal cashoutProfitSharing;

    @Schema(description = "管理的小区数")
    private Integer communityNum;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

    private List<Long> communityIds;

    private Long mbeId;
}
