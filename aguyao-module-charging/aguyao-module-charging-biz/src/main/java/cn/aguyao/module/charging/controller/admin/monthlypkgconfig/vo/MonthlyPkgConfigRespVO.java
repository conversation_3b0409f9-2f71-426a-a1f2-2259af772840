package cn.aguyao.module.charging.controller.admin.monthlypkgconfig.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 包月配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MonthlyPkgConfigRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10348")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "小区id", example = "29573")
    @ExcelProperty("小区id")
    private Long communityId;

    @Schema(description = "编码", example = "27013")
    @ExcelProperty("编码")
    private String code;
//
//    @Schema(description = "三个月价格", example = "14995")
//    @ExcelProperty("三个月价格")
//    private BigDecimal threeMonthsPrice;
//
//    @Schema(description = "六个月价格", example = "8755")
//    @ExcelProperty("六个月价格")
//    private BigDecimal sixMonthsPrice;
//
//    @Schema(description = "一年价格", example = "13497")
//    @ExcelProperty("一年价格")
//    private BigDecimal oneYearPrice;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("排序")
    @Schema(description = "排序", example = "2")
    private Integer sort;

    @ExcelProperty("价格")
    @Schema(description = "价格", example = "2")
    private String price;

    @ExcelProperty("提示")
    @Schema(description = "提示", example = "2")
    private String title;




    @ExcelProperty("小区名称")
    @Schema(description = "小区名称", example = "2")
    private String communityName;

    /**
     * 月数
     */
    @Schema(description = "月数", example = "2")
    private Integer monthNum;
}
