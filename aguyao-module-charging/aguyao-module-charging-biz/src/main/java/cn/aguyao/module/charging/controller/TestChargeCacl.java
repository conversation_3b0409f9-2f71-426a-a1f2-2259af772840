package cn.aguyao.module.charging.controller;

import cn.aguyao.module.charging.dal.dataobject.schemetime.SchemeTimeDO;
import cn.aguyao.module.charging.enums.ChargeModeEnum;
import cn.aguyao.module.charging.enums.PaySourceEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 费用计算测试类
 */
public class TestChargeCacl {


    public void testChargeCacl(Map<String, Object> map) {
        String reason = String.format("【原因】未购买包月或者包月已经过期； 【消费方式】%S； 【计费方式】%S",
                PaySourceEnum.BY_YECZ.getDesc(), ChargeModeEnum.CHARGE_MODE_1.getDesc());
        System.out.println(reason);
        map.put("reason", reason);
    }

    public static void main(String[] args) {

        //
        Map<String, Object> map = new HashMap<>();
        new TestChargeCacl().testChargeCacl(map);
        System.out.println(map.get("reason"));


        //
        Integer maxPower = 280;
        BigDecimal totalDegree = new BigDecimal("0.65");

        BigDecimal tmpPower = new BigDecimal(maxPower).divide(new BigDecimal("1000"), 5, RoundingMode.HALF_UP);
        System.out.println(tmpPower);
        long sec = totalDegree.divide(tmpPower, 5, RoundingMode.HALF_UP)
//                .multiply(new BigDecimal("1000"))       // 将瓦转换成千万
                .multiply(new BigDecimal("3600")).longValue(); // 将小时转换为秒
        System.out.println("sce = " + sec);


        List<SchemeTimeDO> list = new ArrayList<>();
        SchemeTimeDO time = new SchemeTimeDO();
        time.setStartTime("00:00");
        time.setEndTime("23:59");
        time.setEprice(new BigDecimal("0.53"));
        time.setSprice(new BigDecimal("0.20"));
        list.add(time);

        /////////////////////////////
        int minutes = 28;
        Integer power = 176;
        // 开始时间 2025-02-07 20:29:03
        LocalDateTime tempTime = LocalDateTime.of(2025, 2, 7, 20, 29, 3);
        /////////////////////////////
        BigDecimal totalAmt = BigDecimal.ZERO;
        while (minutes > 0) {
//            LocalTime currentHour = tempTime.toLocalTime();
//            BigDecimal pricePerHour = getPricePerHour(list, currentHour);
//            // 每分钟的价格 = 每小时价格 / 60
//            BigDecimal pricePerMinute = pricePerHour.divide(new BigDecimal(60), 5, RoundingMode.HALF_UP);
//            // 往前移一分钟
//            tempTime = tempTime.plusSeconds(60);
//            // 每分钟费用累加
//            totalAmt = totalAmt.add(pricePerMinute);
//            minutes--;
            LocalTime currentHour = tempTime.toLocalTime();
            // 每度电（千瓦时）的价格p
            BigDecimal pricePerDegree = getPricePerHour(list, currentHour);
            // 千瓦 tempPower,功率P
            BigDecimal tempPower = new BigDecimal(power).divide(new BigDecimal(1000), 5, RoundingMode.HALF_UP);
            // 时间t=1/60
            BigDecimal preMinute = new BigDecimal(1).divide(new BigDecimal(60), 5, RoundingMode.HALF_UP);
            // 每分钟的费用 = 每小时价格 / 60
//                    BigDecimal pricePerMinute = pricePerHour.divide(new BigDecimal(60), 5, RoundingMode.HALF_UP);
            BigDecimal pricePerMinute = tempPower.multiply(preMinute).multiply(pricePerDegree);
            // 往前移一分钟
            tempTime = tempTime.plusSeconds(60);
            // 每分钟费用累加
            totalAmt = totalAmt.add(pricePerMinute);
            minutes--;
        }
        BigDecimal rt = totalAmt.setScale(2, RoundingMode.HALF_UP);
        System.out.println(rt);
    }

    private static BigDecimal getPricePerHour(List<SchemeTimeDO> list, LocalTime time) {
        for (SchemeTimeDO item : list) {
            String startTime = item.getStartTime();
            String endTime = item.getEndTime();
            if (startTime.split(":")[0].length() <= 1) {
                startTime = "0" + startTime;
            }
            if (endTime.split(":")[0].length() <= 1) {
                endTime = "0" + endTime;
            }
            // 兼容历史数据
            if (endTime.contains("24:00")) {
                endTime = "23:59";
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
            if ((time.isAfter(LocalTime.parse(startTime + ":00", formatter)) || time.equals(LocalTime.parse(startTime, formatter)))
                    && time.isBefore(LocalTime.parse(endTime + ":59", formatter))) {
                // 电费
                BigDecimal eprice = item.getEprice();
                // 服务费
                BigDecimal sprice = item.getSprice();
                // 总费用 = 电费 + 服务费
                BigDecimal totalPrice = eprice.add(sprice);
                return totalPrice;
            }
        }

        return BigDecimal.ZERO;
    }
}
