package cn.aguyao.module.charging.service.schemecollect;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectPageReqVO;
import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.schemecollect.SchemeCollectDO;
import cn.aguyao.module.charging.enums.ChargeModeEnum;

import javax.validation.Valid;
import java.util.List;

/**
 * 收费方案—收费套餐 Service 接口
 *
 * <AUTHOR>
 */
public interface SchemeCollectService {

    /**
     * 创建收费方案—收费套餐
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSchemeCollect(@Valid SchemeCollectSaveReqVO createReqVO);

    /**
     * 创建收费方案—收费套餐
     *
     * @param list 列表
     * @param chargeModeEnum 方案类型
     * @return 编号
     */
    void createSchemeCollect(List<SchemeCollectSaveReqVO> list, Long schemeId, ChargeModeEnum chargeModeEnum);

    /**
     * 更新收费方案—收费套餐
     *
     * @param updateReqVO 更新信息
     */
    void updateSchemeCollect(@Valid SchemeCollectSaveReqVO updateReqVO);

    /**
     * 更新收费方案—收费套餐
     *
     * @param list 更新信息
     * @param schemeId 方案id
     * @param chargeModeEnum 方案类型
     */
    void updateSchemeCollect(List<SchemeCollectSaveReqVO> list, Long schemeId, ChargeModeEnum chargeModeEnum);

    /**
     * 删除收费方案—收费套餐
     *
     * @param id 编号
     */
    void deleteSchemeCollect(Long id);

    /**
     * 获得收费方案—收费套餐
     *
     * @param id 编号
     * @return 收费方案—收费套餐
     */
    SchemeCollectDO getSchemeCollect(Long id);

    /**
     * 获得收费方案—收费套餐分页
     *
     * @param pageReqVO 分页查询
     * @return 收费方案—收费套餐分页
     */
    PageResult<SchemeCollectDO> getSchemeCollectPage(SchemeCollectPageReqVO pageReqVO);

    /**
     * 根据方案id查询收费套餐
     * @param schemeId
     * @param type
     * @return
     */
    List<SchemeCollectDO> findBySchemeId(Long schemeId, int type);
}
