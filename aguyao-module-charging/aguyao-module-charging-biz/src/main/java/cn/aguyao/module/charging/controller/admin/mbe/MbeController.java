package cn.aguyao.module.charging.controller.admin.mbe;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.mbe.vo.MbePageReqVO;
import cn.aguyao.module.charging.controller.admin.mbe.vo.MbeRespVO;
import cn.aguyao.module.charging.controller.admin.mbe.vo.MbeSaveReqVO;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.service.mbe.MbeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员，即充电的用户")
@RestController
@RequestMapping("/charging/member")
@Validated
public class MbeController {

    @Resource
    private MbeService memberService;

    @PostMapping("/create")
    @Operation(summary = "创建会员，即充电的用户")
    @PreAuthorize("@ss.hasPermission('charging:member:create')")
    public CommonResult<Long> createMember(@Valid @RequestBody MbeSaveReqVO createReqVO) {
        return success(memberService.createMember(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新会员，即充电的用户")
    @PreAuthorize("@ss.hasPermission('charging:member:update')")
    public CommonResult<Boolean> updateMember(@Valid @RequestBody MbeSaveReqVO updateReqVO) {
        memberService.updateMember(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会员，即充电的用户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:member:delete')")
    public CommonResult<Boolean> deleteMember(@RequestParam("id") Long id) {
        memberService.deleteMember(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员，即充电的用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:member:query')")
    public CommonResult<MbeRespVO> getMember(@RequestParam("id") Long id) {
        MbeDO member = memberService.getMember(id);
        return success(BeanUtils.toBean(member, MbeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得会员，即充电的用户分页")
    @PreAuthorize("@ss.hasPermission('charging:member:query')")
    public CommonResult<PageResult<MbeRespVO>> getMemberPage(@Valid MbePageReqVO pageReqVO) {
        PageResult<MbeDO> pageResult = memberService.getMemberPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MbeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员，即充电的用户 Excel")
    @PreAuthorize("@ss.hasPermission('charging:member:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMemberExcel(@Valid MbePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MbeDO> list = memberService.getMemberPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员信息.xls", "数据", MbeRespVO.class,
                        BeanUtils.toBean(list, MbeRespVO.class));
    }

}
