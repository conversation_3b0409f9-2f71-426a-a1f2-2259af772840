package cn.aguyao.module.charging.service.servicemanual;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.servicemanual.vo.ServiceManualPageReqVO;
import cn.aguyao.module.charging.controller.admin.servicemanual.vo.ServiceManualSaveReqVO;
import cn.aguyao.module.charging.controller.app.servicemanual.vo.AppServiceManualPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.servicemanual.ServiceManualDO;
import cn.aguyao.module.charging.dal.mysql.servicemanual.ServiceManualMapper;
import cn.aguyao.module.charging.enums.PrefixConstants;
import cn.aguyao.module.system.api.serial.SerialApi;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.SERVICE_MANUAL_NOT_EXISTS;

/**
 * 使用手册 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ServiceManualServiceImpl implements ServiceManualService {

    @Resource
    private ServiceManualMapper serviceManualMapper;

    @Resource
    private SerialApi serialApi;


    @Override
    public Long createServiceManual(ServiceManualSaveReqVO createReqVO) {
        // 插入
        ServiceManualDO serviceManual = BeanUtils.toBean(createReqVO, ServiceManualDO.class);
        serviceManual.setCode(serialApi.getCode(PrefixConstants.PREFIX_SYSC));
        serviceManualMapper.insert(serviceManual);
        // 返回
        return serviceManual.getId();
    }

    @Override
    public void updateServiceManual(ServiceManualSaveReqVO updateReqVO) {
        // 校验存在
        validateServiceManualExists(updateReqVO.getId());
        // 更新
        ServiceManualDO updateObj = BeanUtils.toBean(updateReqVO, ServiceManualDO.class);
        serviceManualMapper.updateById(updateObj);
    }

    @Override
    public void deleteServiceManual(Long id) {
        // 校验存在
        validateServiceManualExists(id);
        // 删除
        serviceManualMapper.deleteById(id);
    }

    private void validateServiceManualExists(Long id) {
        if (serviceManualMapper.selectById(id) == null) {
            throw exception(SERVICE_MANUAL_NOT_EXISTS);
        }
    }

    @Override
    public ServiceManualDO getServiceManual(Long id) {
        return serviceManualMapper.selectById(id);
    }

    @Override
    public PageResult<ServiceManualDO> getServiceManualPage(ServiceManualPageReqVO pageReqVO) {
        return serviceManualMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ServiceManualDO> list(AppServiceManualPageReqVO pageReqVO) {
        return serviceManualMapper.list(pageReqVO);
    }

}
