package cn.aguyao.module.charging.controller.mgr.community;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.community.vo.CommunityPageReqVO;
import cn.aguyao.module.charging.controller.mgr.community.vo.MgrCommunityDetailRespVO;
import cn.aguyao.module.charging.controller.mgr.community.vo.MgrCommunityRespVO;
import cn.aguyao.module.charging.service.building.BuildingService;
import cn.aguyao.module.charging.service.community.CommunityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理端 - 小区")
@RestController
@RequestMapping("/charging/community")
@Validated
public class MgrCommunityController {

    @Resource
    private CommunityService communityService;

    @Resource
    private BuildingService buildingService;


    @GetMapping("/page")
    @Operation(summary = "获得小区分页")
    @Parameter(name = "name", description = "小区名称")
    public CommonResult<PageResult<MgrCommunityRespVO>> getCommunityPage(
            @RequestParam("pageNo") Integer pageNo,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam(value = "communityName", required = false) String communityName) {

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        CommunityPageReqVO pageReqVO = new CommunityPageReqVO();

        pageReqVO.setName(communityName);
        PageResult<MgrCommunityRespVO> pageResult = communityService.getPage4Mgr(pageReqVO, mpId);
        return success(BeanUtils.toBean(pageResult, MgrCommunityRespVO.class));
    }


    @GetMapping("/detail")
    @Operation(summary = "获得小区明细")
    @Parameter(name = "name", description = "获得小区明细")
    public CommonResult<MgrCommunityDetailRespVO> getCommunityDetail(@RequestParam(value = "communityId") Long communityId) {
//        Long partnerId = SecurityFrameworkUtils.getLoginUserIdCheck();
        MgrCommunityDetailRespVO respVO = communityService.getCommunityDetail(communityId);
        return success(respVO);
    }


}
