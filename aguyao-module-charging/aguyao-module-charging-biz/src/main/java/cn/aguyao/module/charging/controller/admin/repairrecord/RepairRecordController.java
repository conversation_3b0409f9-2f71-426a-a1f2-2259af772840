package cn.aguyao.module.charging.controller.admin.repairrecord;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.repairrecord.vo.RepairRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.repairrecord.vo.RepairRecordRespVO;
import cn.aguyao.module.charging.controller.admin.repairrecord.vo.RepairRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.repairrecord.RepairRecordDO;
import cn.aguyao.module.charging.service.repairrecord.RepairRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 报修记录")
@RestController
@RequestMapping("/charging/repair-record")
@Validated
public class RepairRecordController {

    @Resource
    private RepairRecordService repairRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建报修记录")
    @PreAuthorize("@ss.hasPermission('charging:repair-record:create')")
    public CommonResult<Long> createRepairRecord(@Valid @RequestBody RepairRecordSaveReqVO createReqVO) {
        return success(repairRecordService.createRepairRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新报修记录")
    @PreAuthorize("@ss.hasPermission('charging:repair-record:update')")
    public CommonResult<Boolean> updateRepairRecord(@Valid @RequestBody RepairRecordSaveReqVO updateReqVO) {
        repairRecordService.updateRepairRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除报修记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:repair-record:delete')")
    public CommonResult<Boolean> deleteRepairRecord(@RequestParam("id") Long id) {
        repairRecordService.deleteRepairRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得报修记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:repair-record:query')")
    public CommonResult<RepairRecordRespVO> getRepairRecord(@RequestParam("id") Long id) {
        RepairRecordDO repairRecord = repairRecordService.getRepairRecord(id);
        return success(BeanUtils.toBean(repairRecord, RepairRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得报修记录分页")
    @PreAuthorize("@ss.hasPermission('charging:repair-record:query')")
    public CommonResult<PageResult<RepairRecordRespVO>> getRepairRecordPage(@Valid RepairRecordPageReqVO pageReqVO) {
        PageResult<RepairRecordDO> pageResult = repairRecordService.getRepairRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RepairRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出报修记录 Excel")
    @PreAuthorize("@ss.hasPermission('charging:repair-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRepairRecordExcel(@Valid RepairRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RepairRecordDO> list = repairRecordService.getRepairRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "报修记录.xls", "数据", RepairRecordRespVO.class,
                        BeanUtils.toBean(list, RepairRecordRespVO.class));
    }

}