package cn.aguyao.module.charging.controller.admin.insurerecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 投保记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InsureRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15208")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "投保人，会员id", example = "21489")
    @ExcelProperty("投保人，会员id")
    private Long mbeId;

    @Schema(description = "投保配置的id", example = "10939")
    @ExcelProperty("投保配置的id")
    private Long insureConfigId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}