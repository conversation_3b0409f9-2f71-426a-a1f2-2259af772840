package cn.aguyao.module.charging.controller.mgr.mbe.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员，即充电的用户新增/修改 Request VO")
@Data
public class MgrMbeSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15702")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "所属小区id", example = "24399")
    private Long belongCommunityId;

    @Schema(description = "当前所在小区id, 默认当前小区", example = "24399")
    private Long currentCommunityId;

    @Schema(description = "充值余额")
    private BigDecimal rechargeBalance;

    @Schema(description = "赠送余额")
    private BigDecimal giftBalance;

    @Schema(description = "包月卡小区id", example = "19682")
    private Long monthlyPassCommunityId;

    @Schema(description = "包月卡有效期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime monthlyPassDuration;

    private String monthlyPassDurationStr;

    public void setMonthlyPassDurationStr(String monthlyPassDurationStr) {
//        if (StrUtil.isNotBlank(monthlyPassDurationStr)) {
//            this.monthlyPassDuration = LocalDateTime.parse(monthlyPassDurationStr);
//        }
    }

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

}
