package cn.aguyao.module.charging.dal.mysql.repairrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.repairrecord.vo.RepairRecordPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.repairrecord.RepairRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 报修记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RepairRecordMapper extends BaseMapperX<RepairRecordDO> {

    default PageResult<RepairRecordDO> selectPage(RepairRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RepairRecordDO>()
                .likeIfPresent(RepairRecordDO::getCode, reqVO.getCode())
                .likeIfPresent(RepairRecordDO::getMobile, reqVO.getMobile())
                .eqIfPresent(RepairRecordDO::getCommunityId, reqVO.getCommunityId())
                .eqIfPresent(RepairRecordDO::getBuildingId, reqVO.getBuildingId())
                .likeIfPresent(RepairRecordDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(RepairRecordDO::getRepairStatus, reqVO.getRepairStatus())
                .eqIfPresent(RepairRecordDO::getRemark, reqVO.getRemark())
                .eqIfPresent(RepairRecordDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(RepairRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RepairRecordDO::getId));
    }

}
