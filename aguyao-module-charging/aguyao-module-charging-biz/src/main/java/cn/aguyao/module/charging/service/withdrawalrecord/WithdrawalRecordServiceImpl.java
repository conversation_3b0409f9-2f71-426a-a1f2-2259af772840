package cn.aguyao.module.charging.service.withdrawalrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo.WithdrawalRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo.WithdrawalRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.withdrawalrecord.WithdrawalRecordDO;
import cn.aguyao.module.charging.dal.mysql.withdrawalrecord.WithdrawalRecordMapper;
import cn.aguyao.module.charging.enums.PrefixConstants;
import cn.aguyao.module.system.api.serial.SerialApi;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.WITHDRAWAL_RECORD_NOT_EXISTS;

/**
 * 提现记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WithdrawalRecordServiceImpl implements WithdrawalRecordService {

    @Resource
    private WithdrawalRecordMapper withdrawalRecordMapper;

    @Resource
    private SerialApi serialApi;

    @Override
    public Long createWithdrawalRecord(WithdrawalRecordSaveReqVO createReqVO) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_BY);
        // 插入
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        WithdrawalRecordDO withdrawalRecord = BeanUtils.toBean(createReqVO, WithdrawalRecordDO.class);
        withdrawalRecord.setMpId(mpId);
        withdrawalRecord.setCode(code);
        withdrawalRecordMapper.insert(withdrawalRecord);
        // 返回
        return withdrawalRecord.getId();
    }

    @Override
    public void updateWithdrawalRecord(WithdrawalRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateWithdrawalRecordExists(updateReqVO.getId());
        // 更新
        WithdrawalRecordDO updateObj = BeanUtils.toBean(updateReqVO, WithdrawalRecordDO.class);
        withdrawalRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteWithdrawalRecord(Long id) {
        // 校验存在
        validateWithdrawalRecordExists(id);
        // 删除
        withdrawalRecordMapper.deleteById(id);
    }

    private void validateWithdrawalRecordExists(Long id) {
        if (withdrawalRecordMapper.selectById(id) == null) {
            throw exception(WITHDRAWAL_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public WithdrawalRecordDO getWithdrawalRecord(Long id) {
        return withdrawalRecordMapper.selectById(id);
    }

    @Override
    public PageResult<WithdrawalRecordDO> getWithdrawalRecordPage(WithdrawalRecordPageReqVO pageReqVO) {
        PageResult<WithdrawalRecordDO> page = withdrawalRecordMapper.selectPage(pageReqVO);
        return page;
    }

}
