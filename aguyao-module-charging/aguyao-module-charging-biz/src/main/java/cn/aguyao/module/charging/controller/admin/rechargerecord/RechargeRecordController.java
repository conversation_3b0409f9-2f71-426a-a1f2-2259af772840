package cn.aguyao.module.charging.controller.admin.rechargerecord;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.rechargerecord.vo.RechargeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.rechargerecord.vo.RechargeRecordRespVO;
import cn.aguyao.module.charging.controller.admin.rechargerecord.vo.RechargeRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.rechargerecord.RechargeRecordDO;
import cn.aguyao.module.charging.service.rechargerecord.RechargeRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 充值记录")
@RestController
@RequestMapping("/charging/recharge-record")
@Validated
public class RechargeRecordController {

    @Resource
    private RechargeRecordService rechargeRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建充值记录")
    @PreAuthorize("@ss.hasPermission('charging:recharge-record:create')")
    public CommonResult<Long> createRechargeRecord(@Valid @RequestBody RechargeRecordSaveReqVO createReqVO) {
        return success(rechargeRecordService.createRechargeRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新充值记录")
    @PreAuthorize("@ss.hasPermission('charging:recharge-record:update')")
    public CommonResult<Boolean> updateRechargeRecord(@Valid @RequestBody RechargeRecordSaveReqVO updateReqVO) {
        rechargeRecordService.updateRechargeRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除充值记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:recharge-record:delete')")
    public CommonResult<Boolean> deleteRechargeRecord(@RequestParam("id") Long id) {
        rechargeRecordService.deleteRechargeRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得充值记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:recharge-record:query')")
    public CommonResult<RechargeRecordRespVO> getRechargeRecord(@RequestParam("id") Long id) {
        RechargeRecordDO rechargeRecord = rechargeRecordService.getRechargeRecord(id);
        return success(BeanUtils.toBean(rechargeRecord, RechargeRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得充值记录分页")
    @PreAuthorize("@ss.hasPermission('charging:recharge-record:query')")
    public CommonResult<PageResult<RechargeRecordRespVO>> getRechargeRecordPage(@Valid RechargeRecordPageReqVO pageReqVO) {
        PageResult<RechargeRecordDO> pageResult = rechargeRecordService.getRechargeRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RechargeRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出充值记录 Excel")
    @PreAuthorize("@ss.hasPermission('charging:recharge-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRechargeRecordExcel(@Valid RechargeRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RechargeRecordDO> list = rechargeRecordService.getRechargeRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "充值记录.xls", "数据", RechargeRecordRespVO.class,
                        BeanUtils.toBean(list, RechargeRecordRespVO.class));
    }

}