package cn.aguyao.module.charging.dal.mysql.servicemanual;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.servicemanual.vo.ServiceManualPageReqVO;
import cn.aguyao.module.charging.controller.app.servicemanual.vo.AppServiceManualPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.servicemanual.ServiceManualDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 使用手册 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ServiceManualMapper extends BaseMapperX<ServiceManualDO> {

    default PageResult<ServiceManualDO> selectPage(ServiceManualPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ServiceManualDO>()
                .likeIfPresent(ServiceManualDO::getCode, reqVO.getCode())
                .likeIfPresent(ServiceManualDO::getTitle, reqVO.getTitle())
                .eqIfPresent(ServiceManualDO::getContent, reqVO.getContent())
                .eqIfPresent(ServiceManualDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ServiceManualDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ServiceManualDO::getId));
    }

    default List<ServiceManualDO> list(AppServiceManualPageReqVO pageReqVO) {
        LambdaQueryWrapperX<ServiceManualDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.select(ServiceManualDO::getId,
                ServiceManualDO::getCode,
                ServiceManualDO::getTitle,
                ServiceManualDO::getRemark,
                ServiceManualDO::getCreateTime);
        wrapperX.eqIfPresent(ServiceManualDO::getHelpShow, pageReqVO.getHelpShow());
        return selectList(wrapperX);
    }
}
