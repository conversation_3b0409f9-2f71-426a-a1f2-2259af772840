package cn.aguyao.module.charging.controller.admin.insureconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 投保配置新增/修改 Request VO")
@Data
public class InsureConfigSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5128")
    private Long id;

    @Schema(description = "优惠编号")
    private String code;

    @Schema(description = "投保标题")
    private String title;

    @Schema(description = "投保内容")
    private String content;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

}
