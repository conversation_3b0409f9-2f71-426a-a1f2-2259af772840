package cn.aguyao.module.charging.controller.admin.incomerecord.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 收入（收益）分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IncomeRecordPageReqVO extends PageParam {

    @Schema(description = "会员id， 也就是消费者id", example = "32465")
    private Long mbeId;

    @Schema(description = "微信会员id", example = "3393")
    private Long mpId;

    @Schema(description = "伙伴id，也就是利润所得者id", example = "3393")
    private Long partnerId;

    @Schema(description = "小区id", example = "3393")
    private Long communityId;

    @Schema(description = "小区名称", example = "3393")
    private String communityName;

    @Schema(description = "单笔收入比率")
    private BigDecimal rate;

    @Schema(description = "单笔收入金额")
    private BigDecimal amount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "会员编码", example = "32465")
    private String mbeCode;

}
