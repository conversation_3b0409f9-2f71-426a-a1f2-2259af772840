package cn.aguyao.module.charging.service.incomerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.incomerecord.vo.IncomeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.incomerecord.vo.IncomeRecordSaveReqVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCollectRespVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCommunityRespVO;
import cn.aguyao.module.charging.dal.dataobject.incomerecord.IncomeRecordDO;
import cn.aguyao.module.charging.dal.mysql.incomerecord.IncomeRecordMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.INCOME_RECORD_NOT_EXISTS;

/**
 * 收入（收益） Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IncomeRecordServiceImpl implements IncomeRecordService {

    @Resource
    private IncomeRecordMapper incomeRecordMapper;

    @Override
    public Long createIncomeRecord(IncomeRecordSaveReqVO createReqVO) {
        // 插入
        IncomeRecordDO incomeRecord = BeanUtils.toBean(createReqVO, IncomeRecordDO.class);
        incomeRecordMapper.insert(incomeRecord);
        // 返回
        return incomeRecord.getId();
    }

    @Override
    public Long createIncomeRecord(IncomeRecordDO incomeRecord) {
        incomeRecordMapper.insert(incomeRecord);
        return incomeRecord.getId();
    }

    @Override
    public void updateIncomeRecord(IncomeRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateIncomeRecordExists(updateReqVO.getId());
        // 更新
        IncomeRecordDO updateObj = BeanUtils.toBean(updateReqVO, IncomeRecordDO.class);
        incomeRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteIncomeRecord(Long id) {
        // 校验存在
        validateIncomeRecordExists(id);
        // 删除
        incomeRecordMapper.deleteById(id);
    }

    private void validateIncomeRecordExists(Long id) {
        if (incomeRecordMapper.selectById(id) == null) {
            throw exception(INCOME_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public IncomeRecordDO getIncomeRecord(Long id) {
        return incomeRecordMapper.selectById(id);
    }

    @Override
    public PageResult<IncomeRecordDO> getIncomeRecordPage(IncomeRecordPageReqVO pageReqVO) {
        return incomeRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<IncomeRecordDO> getIncomeRecordPage4Mgr(IncomeRecordPageReqVO pageReqVO) {
        return incomeRecordMapper.selectPage4Mgr(pageReqVO);
    }

    @Override
    public MgrIncomeCollectRespVO incomeCollect(Long partnerId, String communityName) {
        return incomeRecordMapper.incomeCollect(partnerId, communityName);
    }

    @Override
    public PageResult<MgrIncomeCommunityRespVO> incomeCommunity(IncomeRecordPageReqVO reqVO) {
        IPage iPage = new Page(reqVO.getPageNo(), reqVO.getPageSize());
        IPage<MgrIncomeCommunityRespVO> page = incomeRecordMapper.incomeCommunity(iPage, reqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

//    @Override
//    public PageResult<IncomeRecordDO> incomeDetail(Long partnerId, Long communityId, String startTime, String endTime) {
//
//        IncomeRecordPageReqVO pageReqVO = new IncomeRecordPageReqVO();
//        pageReqVO.setPartnerId(partnerId);
//        pageReqVO.setCommunityId(communityId);
//        LocalDateTime[] datetime = DateUtils.dateStr2LocalDateTimeArray(startTime, endTime);
//        pageReqVO.setCreateTime(datetime);
//
//        return incomeRecordMapper.selectPage(pageReqVO);
//    }

}
