package cn.aguyao.module.charging.controller.app.weixin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "微信 APP - 请求获取微信 VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppUserInfoReqVO {

    @Schema(description = "用户信息对象，不包含 openid 等敏感信息", example = "")
    private AppUserInfoVO userInfo;

    @Schema(description = "不包括敏感信息的原始数据字符串，用于计算签名", example = "")
    private String rawData;

    @Schema(description = "使用 sha1( rawData + sessionkey ) 得到字符串，用于校验用户信息，详见 用户数据的签名验证和加解密")
    private String signature;

    @Schema(description = "包括敏感数据在内的完整用户信息的加密数据，详见 用户数据的签名验证和加解密", example = "")
    private String encryptedData;

    @Schema(description = "加密算法的初始向量，详见 用户数据的签名验证和加解密")
    private String iv;

    @Schema(description = "敏感数据对应的云 ID，开通云开发的小程序才会返回，可通过云调用直接获取开放数据，详细见云调用直接获取开放数据", example = "")
    private String cloudID;

}
