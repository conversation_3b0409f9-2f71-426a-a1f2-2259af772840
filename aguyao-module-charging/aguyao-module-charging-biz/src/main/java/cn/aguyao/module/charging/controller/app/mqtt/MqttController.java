package cn.aguyao.module.charging.controller.app.mqtt;


import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.app.mqtt.vo.FuncConfigReqVO;
import cn.aguyao.module.charging.dal.dataobject.device.DeviceDO;
import cn.aguyao.module.charging.enums.DeviceCmdEnum;
import cn.aguyao.module.charging.service.MqttService;
import cn.aguyao.module.charging.service.device.DeviceService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Log4j2
@RestController
@RequestMapping("/system/mqtt")
public class MqttController {

//    @Autowired
    @Resource
    private MqttService mqttService;

    @Resource
    private DeviceService deviceService;


    @GetMapping("/test1")
    public String get() {

        for (int i=0; i<100; i++) {
            log.info("----------------------------------- i = {}", i);
        }
        return "test1";
    }


    @PostMapping("/send")
    public String sendMessage(@RequestParam String topic) {
//        String topic = "mqtt_server/861377075321505/user/get"; //要发送的主题

        try {
            Map<String, Object> map = new HashMap<>();
            map.put("DEVICE", "1234567890");
            map.put("CMD", 3);
            map.put("PORT", 1);
            map.put("TIME", 1000);
            map.put("POWER", 100);
            map.put("AUTOOPEN", 0);
            map.put("SEQ", System.currentTimeMillis());

//            String message = JSON.toJSONString(map);
            String message = "这是要发送的消息！";
//          String message  = MqttSendMsgUtils.sendMqttMsg(1);
            mqttService.publish(topic, message);
        } catch (Exception e) {
            log.error("发送格式组合失败，错误原因为{}",e.getMessage());
        }
        return "主题发送到: " + topic;
    }

    @PostMapping("/subscribe")
    public String subscribeTopic(@RequestParam String topic) {
        try {
            mqttService.subscribe(topic);
            return "Subscribed to topic: " + topic;
        } catch (MqttException e) {
            e.printStackTrace();
            return "Error subscribing to topic";
        }
    }

    @PostMapping("/unsubscribe")
    public String unsubscribeTopic(@RequestParam String topic) {
        try {
            mqttService.unsubscribe(topic);
            return "unsubscribe to topic: " + topic;
        } catch (MqttException e) {
            e.printStackTrace();
            return "Error unsubscribe to topic";
        }
    }


    @PostMapping("/openPort")
    public String openPort(@RequestBody String reqJson) {
        try {

            JSONObject json = JSONObject.parseObject(reqJson);
            String device = json.getString("device");
            Integer port = json.getInteger("port");

            mqttService.openPort(device, port, "11");
            return "open port : " + port;
        } catch (Exception e) {
            e.printStackTrace();
            return "Error unsubscribe to topic";
        }
    }


    @PostMapping("/closePort")
    public String closePort(@RequestBody String reqJson) {
        try {

            Long userId = SecurityFrameworkUtils.getLoginUserIdCheck();
            JSONObject json = JSONObject.parseObject(reqJson);
            String device = json.getString("device");
            Integer port = json.getInteger("port");

            mqttService.closePort(device, port, String.valueOf(userId));
            return "close port : " + port;
        } catch (Exception e) {
            e.printStackTrace();
            return "Error unsubscribe to topic";
        }
    }

    @PostMapping("/portStatus")
    public String portStatus(@RequestBody String reqJson) {
        try {

            JSONObject json = JSONObject.parseObject(reqJson);
            String device = json.getString("device");

            mqttService.portStatus(device);
            return "query all port status";
        } catch (Exception e) {
            e.printStackTrace();
            return "Error unsubscribe to topic";
        }
    }

    @PostMapping("/restart")
    public String restart(@RequestBody String reqJson) {
        try {

            JSONObject json = JSONObject.parseObject(reqJson);
            String device = json.getString("mapper/device");

            mqttService.restart(device);
            return "query all port status";
        } catch (Exception e) {
            e.printStackTrace();
            return "Error unsubscribe to topic";
        }
    }

    /**
     * 批量重启
     * @param reqJson
     * @return
     */
    @PostMapping("/batch-restart")
    public String batchRestart(@RequestBody String reqJson) {
        try {

//            JSONObject json = JSONObject.parseObject(reqJson);
//
//            String device = json.getString("mapper/device");
//            String[] deivces = device.split(",");
//
//            if (deivces == null || deivces.length == 0) {
//                return "设备为空";
//            }

            String[] deivces = {"867339071710895"};

            String url = "https://ysj.aguyao.cn/app-api/system/mqtt/restart";
            for (int i=0; i<deivces.length; i++) {
                JSONObject json = new JSONObject();
                json.put("mapper/device", deivces[i].trim());
                HttpUtil.post(url, json);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return "Error unsubscribe to topic";
        }

        return "success";
    }

    /**
     * 重启设备（充电桩），重启后便于修改mqtt地址
     * @param args
     */
    public static void main(String[] args) {
        String[] deivces = {
                "867339071712107",

        };

//        String url = "http://localhost:48080/app-api/system/mqtt/restart";
        String url = "https://ysj.aguyao.cn/app-api/system/mqtt/restart";
//        String url = "https://www.yuanshijiao.com/app-api/system/mqtt/restart";
        for (int i=0; i<deivces.length; i++) {
            JSONObject json = new JSONObject();
            json.put("mapper/device", deivces[i].trim());
            String result = HttpUtil.post(url, json.toString());
            System.out.println(result);
        }
    }

    /**
     * 功能参数配置，单个设备
     * @param funcConfig
     * @return
     */
    @PostMapping("/func-config/single")
    public String funcCofigSingle(@RequestBody FuncConfigReqVO funcConfig) {
        try {

            String replyTopic = String.format(MqttService.REPLY_TOPIC, funcConfig.getDevice());

            Map<String, Object> map = new HashMap<>();
            map.put("DEVICE", funcConfig.getDevice());
            map.put("CMD", DeviceCmdEnum.FUNC_CONFIG.getValue());
            map.put("POWER", Objects.isNull(funcConfig.getPower()) ? 15 : funcConfig.getPower());
            map.put("MONEY", Objects.isNull(funcConfig.getMoney()) ? 10 : funcConfig.getMoney());
            map.put("RFIDTIME", Objects.isNull(funcConfig.getRfidTime()) ? 60 : funcConfig.getRfidTime());
            map.put("COINTIME", Objects.isNull(funcConfig.getCoinTime()) ? 60 : funcConfig.getCoinTime());
            map.put("TEMP", Objects.isNull(funcConfig.getTemp()) ? 80 : funcConfig.getTemp());
            map.put("OFFTIME1", Objects.isNull(funcConfig.getOffTime1()) ? 30 : funcConfig.getOffTime1());
            map.put("OFFTIME2", Objects.isNull(funcConfig.getOffTime2()) ? 30 : funcConfig.getOffTime2());
            map.put("SEQ", System.currentTimeMillis());

            String message = JSON.toJSONString(map);

            log.info("功能参数配置, 发送的消息为: {}", message);
            System.out.println(message);
            mqttService.publish(replyTopic, message);

            return "success";
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }


    /**
     * 功能参数配置， 全部
     * @param funcConfig
     * @return
     */
    @PostMapping("/func-config/all")
    public String funcCofigAll(@RequestBody FuncConfigReqVO funcConfig) {
        try {

            List<DeviceDO> list = deviceService.findAllDevice();

            if (CollUtil.isEmpty(list)) {
                return "设备数为空";
            }

            list.forEach(deviceDO -> {

                String replyTopic = String.format(MqttService.REPLY_TOPIC, deviceDO.getDevice());

                Map<String, Object> map = new HashMap<>();
                map.put("DEVICE", deviceDO.getDevice());
                map.put("CMD", DeviceCmdEnum.FUNC_CONFIG.getValue());
                map.put("POWER", Objects.isNull(funcConfig.getPower()) ? 15 : funcConfig.getPower());
                map.put("MONEY", Objects.isNull(funcConfig.getMoney()) ? 10 : funcConfig.getMoney());
                map.put("RFIDTIME", Objects.isNull(funcConfig.getRfidTime()) ? 60 : funcConfig.getRfidTime());
                map.put("COINTIME", Objects.isNull(funcConfig.getCoinTime()) ? 60 : funcConfig.getCoinTime());
                map.put("TEMP", Objects.isNull(funcConfig.getTemp()) ? 80 : funcConfig.getTemp());
                map.put("OFFTIME1", Objects.isNull(funcConfig.getOffTime1()) ? 30 : funcConfig.getOffTime1());
                map.put("OFFTIME2", Objects.isNull(funcConfig.getOffTime2()) ? 30 : funcConfig.getOffTime2());
                map.put("SEQ", System.currentTimeMillis());

                String message = JSON.toJSONString(map);

                log.info("功能参数配置, 发送的消息为: {}", message);
                System.out.println(message);
                try {
                    mqttService.publish(replyTopic, message);
                } catch (MqttException e) {
                    throw new RuntimeException(e);
                }
            });

            return "success";
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }
}

