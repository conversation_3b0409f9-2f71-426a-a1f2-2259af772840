package cn.aguyao.module.charging.controller.admin.profitsharingrecord;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo.ProfitSharingRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo.ProfitSharingRecordRespVO;
import cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo.ProfitSharingRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.profitsharingrecord.ProfitSharingRecordDO;
import cn.aguyao.module.charging.service.profitsharingrecord.ProfitSharingRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 分润记录")
@RestController
@RequestMapping("/charging/profit-sharing-record")
@Validated
public class ProfitSharingRecordController {

    @Resource
    private ProfitSharingRecordService profitSharingRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建分润记录")
    @PreAuthorize("@ss.hasPermission('charging:profit-sharing-record:create')")
    public CommonResult<Long> createProfitSharingRecord(@Valid @RequestBody ProfitSharingRecordSaveReqVO createReqVO) {
        return success(profitSharingRecordService.createProfitSharingRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新分润记录")
    @PreAuthorize("@ss.hasPermission('charging:profit-sharing-record:update')")
    public CommonResult<Boolean> updateProfitSharingRecord(@Valid @RequestBody ProfitSharingRecordSaveReqVO updateReqVO) {
        profitSharingRecordService.updateProfitSharingRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除分润记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:profit-sharing-record:delete')")
    public CommonResult<Boolean> deleteProfitSharingRecord(@RequestParam("id") Long id) {
        profitSharingRecordService.deleteProfitSharingRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得分润记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:profit-sharing-record:query')")
    public CommonResult<ProfitSharingRecordRespVO> getProfitSharingRecord(@RequestParam("id") Long id) {
        ProfitSharingRecordDO profitSharingRecord = profitSharingRecordService.getProfitSharingRecord(id);
        return success(BeanUtils.toBean(profitSharingRecord, ProfitSharingRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得分润记录分页")
    @PreAuthorize("@ss.hasPermission('charging:profit-sharing-record:query')")
    public CommonResult<PageResult<ProfitSharingRecordRespVO>> getProfitSharingRecordPage(@Valid ProfitSharingRecordPageReqVO pageReqVO) {
        PageResult<ProfitSharingRecordDO> pageResult = profitSharingRecordService.getProfitSharingRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProfitSharingRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出分润记录 Excel")
    @PreAuthorize("@ss.hasPermission('charging:profit-sharing-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProfitSharingRecordExcel(@Valid ProfitSharingRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProfitSharingRecordDO> list = profitSharingRecordService.getProfitSharingRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "分润记录.xls", "数据", ProfitSharingRecordRespVO.class,
                        BeanUtils.toBean(list, ProfitSharingRecordRespVO.class));
    }

}