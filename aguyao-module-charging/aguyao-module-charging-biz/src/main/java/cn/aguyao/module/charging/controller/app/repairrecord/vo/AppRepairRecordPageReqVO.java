package cn.aguyao.module.charging.controller.app.repairrecord.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 报修记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppRepairRecordPageReqVO extends PageParam {

    @Schema(description = "保留记录编号")
    private String code;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "报修小区id", example = "30287")
    private Long communityId;

    @Schema(description = "楼栋id", example = "4449")
    private Long buildingId;

    @Schema(description = "设备编号")
    private String deviceCode;

    @Schema(description = "维修状态", example = "2")
    private Integer repairStatus;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", example = "2")
    private Integer status;

    @Schema(description = "创建时间，报修时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


    @Schema(description = "报修小区名称", example = "源昌豪庭")
    private String communityName;

}
