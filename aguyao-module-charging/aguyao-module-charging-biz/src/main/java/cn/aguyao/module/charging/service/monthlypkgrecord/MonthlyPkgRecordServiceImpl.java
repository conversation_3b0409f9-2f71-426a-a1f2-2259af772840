package cn.aguyao.module.charging.service.monthlypkgrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo.MonthlyPkgRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo.MonthlyPkgRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgrecord.MonthlyPkgRecordDO;
import cn.aguyao.module.charging.dal.mysql.monthlypkgrecord.MonthlyPkgRecordMapper;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.enums.OrderStatusEnum;
import cn.aguyao.module.charging.enums.PrefixConstants;
import cn.aguyao.module.charging.service.community.CommunityService;
import cn.aguyao.module.charging.service.mbe.MbeService;
import cn.aguyao.module.charging.service.membermonthly.MemberMonthlyService;
import cn.aguyao.module.pay.api.order.dto.PayOrderNotifyRespDTO;
import cn.aguyao.module.system.api.serial.SerialApi;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.MONTHLY_PKG_RECORD_NOT_EXISTS;

/**
 * 包月记录 Service 实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Validated
public class MonthlyPkgRecordServiceImpl implements MonthlyPkgRecordService {

    @Resource
    private MonthlyPkgRecordMapper monthlyPkgRecordMapper;

    @Resource
    private SerialApi serialApi;

    @Lazy
    @Resource
    private MbeService mbeService;

    @Resource
    private CommunityService communityService;

    @Resource
    private MemberMonthlyService memberMonthlyService;


    @Override
    public Long createMonthlyPkgRecord(MonthlyPkgRecordSaveReqVO createReqVO) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_BY);
        // 插入
        MonthlyPkgRecordDO monthlyPkgRecord = BeanUtils.toBean(createReqVO, MonthlyPkgRecordDO.class);
        monthlyPkgRecord.setCode(code);
        monthlyPkgRecordMapper.insert(monthlyPkgRecord);
        // 返回
        return monthlyPkgRecord.getId();
    }

    @Override
    public Long createMonthlyPkgRecord(MonthlyPkgRecordDO monthlyPkgRecord) {
        if (Objects.nonNull(monthlyPkgRecord.getMpId())) {
            MbeDO member = mbeService.selectMemberByMpUserId(monthlyPkgRecord.getMpId());
            if (Objects.nonNull(member)) {
                monthlyPkgRecord.setMbeCode(member.getCode());
                monthlyPkgRecord.setMobile(member.getMobile());
            }
        }
        monthlyPkgRecordMapper.insert(monthlyPkgRecord);
        return monthlyPkgRecord.getId();
    }

    @Override
    public void updateMonthlyPkgRecord(MonthlyPkgRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateMonthlyPkgRecordExists(updateReqVO.getId());
        // 更新
        MonthlyPkgRecordDO updateObj = BeanUtils.toBean(updateReqVO, MonthlyPkgRecordDO.class);
        monthlyPkgRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteMonthlyPkgRecord(Long id) {
        // 校验存在
        validateMonthlyPkgRecordExists(id);
        // 删除
        monthlyPkgRecordMapper.deleteById(id);
    }

    private void validateMonthlyPkgRecordExists(Long id) {
        if (monthlyPkgRecordMapper.selectById(id) == null) {
            throw exception(MONTHLY_PKG_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public MonthlyPkgRecordDO getMonthlyPkgRecord(Long id) {
        MonthlyPkgRecordDO entity = monthlyPkgRecordMapper.selectById(id);
        if (Objects.nonNull(entity) && Objects.nonNull(entity.getBelongCommunityId())) {
            Optional.of(communityService.getCommunity(entity.getBelongCommunityId())).ifPresent(community -> {
                entity.setBelongCommunityName(community.getName());
            });
        }

        return entity;
    }

    @Override
    public PageResult<MonthlyPkgRecordDO> getMonthlyPkgRecordPage(MonthlyPkgRecordPageReqVO pageReqVO) {
        PageResult<MonthlyPkgRecordDO> result = monthlyPkgRecordMapper.selectPage(pageReqVO);
        result.getList().forEach(item -> {
            if (Objects.nonNull(item.getBelongCommunityId())) {
                Optional.of(
                        communityService.getCommunity(item.getBelongCommunityId())).ifPresent(community -> {
                    item.setBelongCommunityName(community.getName());
                });
            }
        });
        return result;
    }


    @Override
    public MonthlyPkgRecordDO getMonthlyPkgRecordByMbeId(Long memberId) {
        // todo
        return null;
    }

    @Override
    public MonthlyPkgRecordDO findOneByMpId(Long mpId) {
        return monthlyPkgRecordMapper.findOneByMpId(mpId, LocalDateTime.now());
    }

    @Override
    @Transactional
    public void notifyHandle(PayOrderNotifyRespDTO respDTO) {
        MonthlyPkgRecordDO entity = this.findByCode(respDTO.getOutTradeNo());
        if (Objects.isNull(entity)) {
            return ;
        }

        // 如果已经通知过了，则不处理
        if (Objects.equals(entity.getStatus(),OrderStatusEnum.PAID.getValue())) {
            log.info("notifyHandle包月记录已经通知过了，不再处理");
            return ;
        }

        if (respDTO.getSuccess()) {
            // 充值成功
            entity.setRemark("充值成功");
            entity.setStatus(OrderStatusEnum.PAID.getValue());

            mbeService.updateMbeMonthlyPkg(entity.getMpId(), entity.getProId());

            // 充值成功，更新包月记录
            memberMonthlyService.createOrUpdateMemberMonthly(entity);
        } else {
            // 充值失败
            entity.setRemark("充值失败");
            entity.setStatus(OrderStatusEnum.PAY_FAIL.getValue());
        }

        monthlyPkgRecordMapper.updateById(entity);
    }

    @Override
    public MonthlyPkgRecordDO findByCode(String code) {
        return monthlyPkgRecordMapper.findByCode(code);
    }
}
