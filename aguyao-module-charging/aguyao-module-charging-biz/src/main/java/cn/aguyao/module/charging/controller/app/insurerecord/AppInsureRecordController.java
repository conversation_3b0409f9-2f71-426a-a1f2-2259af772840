package cn.aguyao.module.charging.controller.app.insurerecord;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.insurerecord.vo.InsureRecordPageReqVO;
import cn.aguyao.module.charging.controller.app.insurerecord.vo.AppInsureRecordRespVO;
import cn.aguyao.module.charging.dal.dataobject.insurerecord.InsureRecordDO;
import cn.aguyao.module.charging.service.insurerecord.InsureRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 投保记录")
@RestController
@RequestMapping("/charging/insure-record")
@Validated
public class AppInsureRecordController {

    @Resource
    private InsureRecordService insureRecordService;

//    @PostMapping("/create")
//    @Operation(summary = "创建投保记录")
//    @PreAuthorize("@ss.hasPermission('charging:insure-record:create')")
//    public CommonResult<Long> createInsureRecord(@Valid @RequestBody AppInsureRecordSaveReqVO createReqVO) {
//        return success(insureRecordService.createInsureRecord(createReqVO));
//    }

    @GetMapping("/page")
    @Operation(summary = "获得投保记录分页")
//    @PreAuthorize("@ss.hasPermission('charging:insure-record:query')")
    public CommonResult<PageResult<AppInsureRecordRespVO>> getInsureRecordPage(
            @RequestParam("startTime") String startTime,
            @RequestParam("endTime") String endTime) {

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        InsureRecordPageReqVO pageReqVO = new InsureRecordPageReqVO();
        pageReqVO.setMpId(mpId);

        LocalDateTime[] createTime = DateUtils.dateStr2LocalDateTimeArray(startTime, endTime);
        pageReqVO.setCreateTime(createTime);
        PageResult<InsureRecordDO> pageResult = insureRecordService.getInsureRecordPage(pageReqVO);

        return success(BeanUtils.toBean(pageResult, AppInsureRecordRespVO.class));
    }
}
