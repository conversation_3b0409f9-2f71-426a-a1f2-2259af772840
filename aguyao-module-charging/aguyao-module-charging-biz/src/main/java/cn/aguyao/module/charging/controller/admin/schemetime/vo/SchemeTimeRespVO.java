package cn.aguyao.module.charging.controller.admin.schemetime.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 收费方案—时段电费 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SchemeTimeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10606")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "方案id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26113")
    @ExcelProperty("方案id")
    private Long schemeId;

    @Schema(description = "序号")
    @ExcelProperty("序号")
    private Integer sort;

    @Schema(description = "开始时段，左侧（包含）")
    @ExcelProperty("开始时段，左侧（包含）")
    private String startTime;

    @Schema(description = "结束时段，右侧（不含）")
    @ExcelProperty("结束时段，右侧（不含）")
    private String endTime;

    @Schema(description = "电费 energy price（单位：元）", example = "25235")
    @ExcelProperty("电费 energy price（单位：元）")
    private BigDecimal eprice;

    @Schema(description = "服务费 service price（单位：元）", example = "10524")
    @ExcelProperty("服务费 service price（单位：元）")
    private BigDecimal sprice;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}