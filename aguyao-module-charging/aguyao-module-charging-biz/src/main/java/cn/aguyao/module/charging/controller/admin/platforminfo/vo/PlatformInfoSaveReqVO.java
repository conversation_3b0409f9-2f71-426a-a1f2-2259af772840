package cn.aguyao.module.charging.controller.admin.platforminfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 平台信息新增/修改 Request VO")
@Data
public class PlatformInfoSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "776")
    private Long id;

    @Schema(description = "公司名称", example = "芋艿")
    private String name;

    @Schema(description = "客服热线")
    private String customerServicePhone;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

}
