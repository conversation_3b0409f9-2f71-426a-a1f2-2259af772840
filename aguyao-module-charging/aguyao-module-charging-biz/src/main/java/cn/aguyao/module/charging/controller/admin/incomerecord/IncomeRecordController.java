package cn.aguyao.module.charging.controller.admin.incomerecord;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.incomerecord.vo.IncomeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.incomerecord.vo.IncomeRecordRespVO;
import cn.aguyao.module.charging.controller.admin.incomerecord.vo.IncomeRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.incomerecord.IncomeRecordDO;
import cn.aguyao.module.charging.service.incomerecord.IncomeRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 收入（收益）")
@RestController
@RequestMapping("/charging/income-record")
@Validated
public class IncomeRecordController {

    @Resource
    private IncomeRecordService incomeRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建收入（收益）")
    @PreAuthorize("@ss.hasPermission('charging:income-record:create')")
    public CommonResult<Long> createIncomeRecord(@Valid @RequestBody IncomeRecordSaveReqVO createReqVO) {
        return success(incomeRecordService.createIncomeRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新收入（收益）")
    @PreAuthorize("@ss.hasPermission('charging:income-record:update')")
    public CommonResult<Boolean> updateIncomeRecord(@Valid @RequestBody IncomeRecordSaveReqVO updateReqVO) {
        incomeRecordService.updateIncomeRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除收入（收益）")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:income-record:delete')")
    public CommonResult<Boolean> deleteIncomeRecord(@RequestParam("id") Long id) {
        incomeRecordService.deleteIncomeRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得收入（收益）")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:income-record:query')")
    public CommonResult<IncomeRecordRespVO> getIncomeRecord(@RequestParam("id") Long id) {
        IncomeRecordDO incomeRecord = incomeRecordService.getIncomeRecord(id);
        return success(BeanUtils.toBean(incomeRecord, IncomeRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得收入（收益）分页")
    @PreAuthorize("@ss.hasPermission('charging:income-record:query')")
    public CommonResult<PageResult<IncomeRecordRespVO>> getIncomeRecordPage(@Valid IncomeRecordPageReqVO pageReqVO) {
        PageResult<IncomeRecordDO> pageResult = incomeRecordService.getIncomeRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, IncomeRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出收入（收益） Excel")
    @PreAuthorize("@ss.hasPermission('charging:income-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportIncomeRecordExcel(@Valid IncomeRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<IncomeRecordDO> list = incomeRecordService.getIncomeRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "收入（收益）.xls", "数据", IncomeRecordRespVO.class,
                        BeanUtils.toBean(list, IncomeRecordRespVO.class));
    }

}