package cn.aguyao.module.charging.controller.app.chargerecord.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 充电记录，消费记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppChargeRecordPageReqVO extends PageParam {

//    @NotNull(message = "消费类型不能为空")
    @Schema(description = "消费类型，1：单词消费； 2：包月消费；")
    private Integer type;

    @Schema(description = "充电时间")
//    @NotEmpty.List({@NotEmpty(message = "开始、结束时间不能为空")})
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
