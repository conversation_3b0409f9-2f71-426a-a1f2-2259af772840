package cn.aguyao.module.charging.annotation;

import cn.aguyao.module.system.api.oauth2.OAuth2TokenApi;
import cn.aguyao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import com.alibaba.fastjson.JSON;
import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Log4j2
@Aspect
@Component
public class TokenVerificationAspect {

    @Resource
    private OAuth2TokenApi oAuth2TokenApi;

    @Pointcut("@annotation(cn.aguyao.module.charging.annotation.TokenRequired)")
    public void tokenRequiredMethods() {}

    @Before("tokenRequiredMethods()")
    public void verifyToken() throws IOException {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            HttpServletResponse response = attributes.getResponse();

            // 从请求头中获取 Token
            String token = request.getHeader("Authorization");

            // 验证 Token
            if (token == null ||!isValidToken(token)) {
                if (response != null) {

                    Map<String, Object> map = new HashMap<>();
                    map.put("code", HttpServletResponse.SC_UNAUTHORIZED);
                    map.put("msg", "Unauthorized");

                    String json = JSON.toJSONString(map);
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);

                    response.getWriter().write(json);
                    response.getWriter().flush();
                    response.getWriter().close();

                    return ;
                }
//                throw new RuntimeException("Unauthorized");
            }
        }
    }

    // 验证 Token 的方法，这里只是简单示例，实际中需要根据具体业务实现
    private boolean isValidToken(String token) {
        // 这里可以添加 Token 验证逻辑，比如检查 Token 是否过期、是否合法等
        System.out.println("-------isValidToken-------");
        try {
            OAuth2AccessTokenCheckRespDTO dto = oAuth2TokenApi.checkAccessToken(token);
            if (Objects.isNull(dto) || Objects.isNull(dto.getUserId())) {
                return false;
            }
        } catch (Exception e) {
//            throw new RuntimeException(e);
            log.error("验证token失败", e);
            return false;
        }
//        return "valid_token".equals(token);
        return true;
    }
}
