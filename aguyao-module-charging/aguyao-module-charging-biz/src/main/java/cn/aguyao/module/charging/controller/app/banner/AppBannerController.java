package cn.aguyao.module.charging.controller.app.banner;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.app.banner.vo.AppBannnerRespVO;
import cn.aguyao.module.charging.dal.dataobject.banner.BannerDO;
import cn.aguyao.module.charging.service.banner.BannerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序端 - banner")
@RestController
@RequestMapping("/charging/banner")
@Validated
public class AppBannerController {

    @Resource
    private BannerService bannerService;


    @GetMapping("/list")
    @Operation(summary = "获得banner列表信息")
    public CommonResult<List<AppBannnerRespVO>> list() {
        List<BannerDO> list = bannerService.getBannerList();
        return success(BeanUtils.toBean(list, AppBannnerRespVO.class));
    }


}
