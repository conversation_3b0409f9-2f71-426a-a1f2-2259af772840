package cn.aguyao.module.charging.service.incomerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.incomerecord.vo.IncomeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.incomerecord.vo.IncomeRecordSaveReqVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCollectRespVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCommunityRespVO;
import cn.aguyao.module.charging.dal.dataobject.incomerecord.IncomeRecordDO;
import org.apache.ibatis.annotations.Param;

import javax.validation.Valid;

/**
 * 收入（收益） Service 接口
 *
 * <AUTHOR>
 */
public interface IncomeRecordService {

    /**
     * 创建收入（收益）
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createIncomeRecord(@Valid IncomeRecordSaveReqVO createReqVO);


    /**
     * 创建收入（收益）
     *
     * @param incomeRecord 创建信息
     * @return 编号
     */
    Long createIncomeRecord(IncomeRecordDO incomeRecord);

    /**
     * 更新收入（收益）
     *
     * @param updateReqVO 更新信息
     */
    void updateIncomeRecord(@Valid IncomeRecordSaveReqVO updateReqVO);

    /**
     * 删除收入（收益）
     *
     * @param id 编号
     */
    void deleteIncomeRecord(Long id);

    /**
     * 获得收入（收益）
     *
     * @param id 编号
     * @return 收入（收益）
     */
    IncomeRecordDO getIncomeRecord(Long id);

    /**
     * 获得收入（收益）分页
     *
     * @param pageReqVO 分页查询
     * @return 收入（收益）分页
     */
    PageResult<IncomeRecordDO> getIncomeRecordPage(@Param("pageReqVO") IncomeRecordPageReqVO pageReqVO);

    PageResult<IncomeRecordDO> getIncomeRecordPage4Mgr(IncomeRecordPageReqVO pageReqVO);

    /**
     * 收入统计
     * @param partnerId
     * @return
     */
    MgrIncomeCollectRespVO incomeCollect(Long partnerId, String communityName);

    /**
     * 各个小区的收入
     * @param pageReqVO
     * @return
     */
    PageResult<MgrIncomeCommunityRespVO> incomeCommunity(IncomeRecordPageReqVO pageReqVO);

    /**
     * 收入详情
     * @param partnerId
     * @param communityId
     * @param startTime
     * @param endTime
     * @return
     */
//    MgrIncomeDetailRespVO incomeDetail(Long partnerId, Long communityId, String startTime, String endTime);
}
