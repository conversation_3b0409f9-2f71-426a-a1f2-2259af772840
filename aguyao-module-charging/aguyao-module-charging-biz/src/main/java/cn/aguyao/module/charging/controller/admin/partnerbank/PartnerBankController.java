package cn.aguyao.module.charging.controller.admin.partnerbank;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.partnerbank.vo.PartnerBankPageReqVO;
import cn.aguyao.module.charging.controller.admin.partnerbank.vo.PartnerBankRespVO;
import cn.aguyao.module.charging.controller.admin.partnerbank.vo.PartnerBankSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.partnerbank.PartnerBankDO;
import cn.aguyao.module.charging.service.partnerbank.PartnerBankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 伙伴银行")
@RestController
@RequestMapping("/charging/partner-bank")
@Validated
public class PartnerBankController {

    @Resource
    private PartnerBankService partnerBankService;

    @PostMapping("/create")
    @Operation(summary = "创建伙伴银行")
    @PreAuthorize("@ss.hasPermission('charging:partner-bank:create')")
    public CommonResult<Long> createPartnerBank(@Valid @RequestBody PartnerBankSaveReqVO createReqVO) {
        return success(partnerBankService.createPartnerBank(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新伙伴银行")
    @PreAuthorize("@ss.hasPermission('charging:partner-bank:update')")
    public CommonResult<Boolean> updatePartnerBank(@Valid @RequestBody PartnerBankSaveReqVO updateReqVO) {
        partnerBankService.updatePartnerBank(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除伙伴银行")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:partner-bank:delete')")
    public CommonResult<Boolean> deletePartnerBank(@RequestParam("id") Long id) {
        partnerBankService.deletePartnerBank(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得伙伴银行")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:partner-bank:query')")
    public CommonResult<PartnerBankRespVO> getPartnerBank(@RequestParam("id") Long id) {
        PartnerBankDO partnerBank = partnerBankService.getPartnerBank(id);
        return success(BeanUtils.toBean(partnerBank, PartnerBankRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得伙伴银行分页")
    @PreAuthorize("@ss.hasPermission('charging:partner-bank:query')")
    public CommonResult<PageResult<PartnerBankRespVO>> getPartnerBankPage(@Valid PartnerBankPageReqVO pageReqVO) {
        PageResult<PartnerBankDO> pageResult = partnerBankService.getPartnerBankPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PartnerBankRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出伙伴银行 Excel")
    @PreAuthorize("@ss.hasPermission('charging:partner-bank:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPartnerBankExcel(@Valid PartnerBankPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PartnerBankDO> list = partnerBankService.getPartnerBankPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "伙伴银行.xls", "数据", PartnerBankRespVO.class,
                        BeanUtils.toBean(list, PartnerBankRespVO.class));
    }

}