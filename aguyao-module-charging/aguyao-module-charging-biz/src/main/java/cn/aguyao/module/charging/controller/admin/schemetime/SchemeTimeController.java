package cn.aguyao.module.charging.controller.admin.schemetime;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.schemetime.vo.SchemeTimePageReqVO;
import cn.aguyao.module.charging.controller.admin.schemetime.vo.SchemeTimeRespVO;
import cn.aguyao.module.charging.controller.admin.schemetime.vo.SchemeTimeSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.schemetime.SchemeTimeDO;
import cn.aguyao.module.charging.service.schemetime.SchemeTimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 收费方案—时段电费")
@RestController
@RequestMapping("/charging/scheme-time")
@Validated
public class SchemeTimeController {

    @Resource
    private SchemeTimeService schemeTimeService;

    @PostMapping("/create")
    @Operation(summary = "创建收费方案—时段电费")
    @PreAuthorize("@ss.hasPermission('charging:scheme-time:create')")
    public CommonResult<Long> createSchemeTime(@Valid @RequestBody SchemeTimeSaveReqVO createReqVO) {
        return success(schemeTimeService.createSchemeTime(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新收费方案—时段电费")
    @PreAuthorize("@ss.hasPermission('charging:scheme-time:update')")
    public CommonResult<Boolean> updateSchemeTime(@Valid @RequestBody SchemeTimeSaveReqVO updateReqVO) {
        schemeTimeService.updateSchemeTime(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除收费方案—时段电费")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:scheme-time:delete')")
    public CommonResult<Boolean> deleteSchemeTime(@RequestParam("id") Long id) {
        schemeTimeService.deleteSchemeTime(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得收费方案—时段电费")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:scheme-time:query')")
    public CommonResult<SchemeTimeRespVO> getSchemeTime(@RequestParam("id") Long id) {
        SchemeTimeDO schemeTime = schemeTimeService.getSchemeTime(id);
        return success(BeanUtils.toBean(schemeTime, SchemeTimeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得收费方案—时段电费分页")
    @PreAuthorize("@ss.hasPermission('charging:scheme-time:query')")
    public CommonResult<PageResult<SchemeTimeRespVO>> getSchemeTimePage(@Valid SchemeTimePageReqVO pageReqVO) {
        PageResult<SchemeTimeDO> pageResult = schemeTimeService.getSchemeTimePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SchemeTimeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出收费方案—时段电费 Excel")
    @PreAuthorize("@ss.hasPermission('charging:scheme-time:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSchemeTimeExcel(@Valid SchemeTimePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SchemeTimeDO> list = schemeTimeService.getSchemeTimePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "收费方案—时段电费.xls", "数据", SchemeTimeRespVO.class,
                        BeanUtils.toBean(list, SchemeTimeRespVO.class));
    }

}