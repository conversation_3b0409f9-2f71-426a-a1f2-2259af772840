package cn.aguyao.module.charging.controller.admin.mpuser.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 微信小程序粉丝分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MpUserPageReqVO extends PageParam {

    @Schema(description = "公众号 appId", example = "16844")
    private String appId;

    @Schema(description = "用户唯一标识", example = "16176")
    private String openid;

    @Schema(description = "微信生态唯一标识", example = "9495")
    private String unionId;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "昵称", example = "张三")
    private String nickname;

    @Schema(description = "头像地址", example = "https://www.iocoder.cn")
    private String headImageUrl;

    @Schema(description = "语言")
    private String language;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "关注状态, 1. 开启 - 已关注; 2. 禁用 - 取消关注", example = "2")
    private Integer subscribeStatus;

    @Schema(description = "关注时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] subscribeTime;

    @Schema(description = "取消关注时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] unsubscribeTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
