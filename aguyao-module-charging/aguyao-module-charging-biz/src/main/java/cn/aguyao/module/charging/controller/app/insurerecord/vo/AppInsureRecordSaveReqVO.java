package cn.aguyao.module.charging.controller.app.insurerecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 投保记录新增/修改 Request VO")
@Data
public class AppInsureRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15208")
    private Long id;

    @Schema(description = "投保人，会员id", example = "21489")
    private Long mbeId;

    @Schema(description = "投保配置的id", example = "10939")
    private Long insureConfigId;

}
