package cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 分润记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProfitSharingRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "18879")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "主管编号")
    @ExcelProperty("主管编号")
    private String partnerCode;

    @Schema(description = "主管名称", example = "赵六")
    @ExcelProperty("主管名称")
    private String partnerName;

    @Schema(description = "主管手机")
    @ExcelProperty("主管手机")
    private String partnerMobile;

    @Schema(description = "费率")
    @ExcelProperty("费率")
    private BigDecimal rate;

    @Schema(description = "分润")
    @ExcelProperty("分润")
    private BigDecimal profitSharing;

    @Schema(description = "所属小区id", example = "15690")
    @ExcelProperty("所属小区id")
    private Long belongCommunityId;

    @Schema(description = "用户手机")
    @ExcelProperty("用户手机")
    private String mbeMobile;

    @Schema(description = "消费时间")
    @ExcelProperty("消费时间")
    private LocalDateTime consumeTime;

    @Schema(description = "消费金额")
    @ExcelProperty("消费金额")
    private BigDecimal consumeAmount;

    @Schema(description = "消费类型", example = "2")
    @ExcelProperty("消费类型")
    private Integer consumeType;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}