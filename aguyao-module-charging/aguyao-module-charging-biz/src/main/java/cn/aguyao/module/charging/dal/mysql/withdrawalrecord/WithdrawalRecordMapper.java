package cn.aguyao.module.charging.dal.mysql.withdrawalrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.collection.ArrayUtils;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo.WithdrawalRecordPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgconfig.MonthlyPkgConfigDO;
import cn.aguyao.module.charging.dal.dataobject.partner.PartnerDO;
import cn.aguyao.module.charging.dal.dataobject.withdrawalrecord.WithdrawalRecordDO;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 提现记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WithdrawalRecordMapper extends BaseMapperX<WithdrawalRecordDO> {

    default PageResult<WithdrawalRecordDO> selectPage(WithdrawalRecordPageReqVO reqVO) {

        MPJLambdaWrapper<WithdrawalRecordDO> wrapper = JoinWrappers.lambda(WithdrawalRecordDO.class) ;
        wrapper.selectAs(PartnerDO::getName, WithdrawalRecordDO::getPartnerName);
        wrapper.selectAs(PartnerDO::getMobile, WithdrawalRecordDO::getPartnerMobile);
        wrapper.selectAll(WithdrawalRecordDO.class);

        wrapper.leftJoin(PartnerDO.class, PartnerDO::getId, WithdrawalRecordDO::getPartnerId);

        wrapper.eqIfExists(WithdrawalRecordDO::getCode, reqVO.getCode());
        wrapper.eqIfExists(WithdrawalRecordDO::getPartnerId, reqVO.getPartnerId());
        wrapper.likeIfExists(WithdrawalRecordDO::getBankName, reqVO.getBankName());
        wrapper.eqIfExists(WithdrawalRecordDO::getBankCardNum, reqVO.getBankCardNum());
        wrapper.eqIfExists(WithdrawalRecordDO::getStatus, reqVO.getStatus());
        wrapper.eqIfExists(PartnerDO::getCode, reqVO.getCode());
        wrapper.likeIfExists(PartnerDO::getName, reqVO.getPartnerName());
        wrapper.likeIfExists(PartnerDO::getMobile, reqVO.getPartnerMobile());
        // 商户端有mpId，后台则不需要mpId字段
        wrapper.eqIfExists(PartnerDO::getMpId, reqVO.getMpId());

        if (reqVO.getApplyTime() != null && reqVO.getApplyTime().length > 0) {
            Object val1 = ArrayUtils.get(reqVO.getApplyTime(), 0);
            Object val2 = ArrayUtils.get(reqVO.getApplyTime(), 1);
            if (val1 != null) {
                wrapper.ge(MonthlyPkgConfigDO::getCreateTime, val1);
            }
            if (val2 != null) {
                wrapper.le(MonthlyPkgConfigDO::getCreateTime, val2);
            }
        }
        wrapper.orderByDesc(MonthlyPkgConfigDO::getId);

        return selectPage(reqVO, wrapper);
    }

}
