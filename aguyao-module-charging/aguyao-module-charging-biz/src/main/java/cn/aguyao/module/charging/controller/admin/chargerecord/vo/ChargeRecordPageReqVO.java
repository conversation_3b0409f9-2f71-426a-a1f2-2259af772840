package cn.aguyao.module.charging.controller.admin.chargerecord.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 充电记录，消费记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChargeRecordPageReqVO extends PageParam {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "用户id")
    private Long mbeId;

    /**
     * 微信用户id
     */
    @Schema(description = "微信用户id")
    private Long mpId;

    @Schema(description = "用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    private String mobile;

//    @Schema(description = "充电时间")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime[] chargeTime;

    @Schema(description = "充电时间_开始充电的时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] chargeTimeStart;

    @Schema(description = "充电时间_结束充电的时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] chargeTimeEnd;

    @Schema(description = "充电小区id", example = "3424")
    private Long chargeCommunityId;

    @Schema(description = "楼栋id", example = "14702")
    private Long buildingsId;

//    @Schema(description = "设备id", example = "26557")
//    private Long deviceId;
    @Schema(description = "设备编码", example = "26557")
    private String device;

    @Schema(description = "消费金额")
    private BigDecimal amount;

    @Schema(description = "1：单次充电（按时间）；2：临时充电（按金额）；")
    private Integer type;

    @Schema(description = "支付来源，消费来源")
    private Integer[] paySource;

    @Schema(description = "进行状态", example = "1")
    private Integer[] proceedStatus;

    @Schema(description = "瓦数，消耗的瓦数")
    private BigDecimal wattage;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "充电小区", example = "")
    private String chargeCommunityName;

}
