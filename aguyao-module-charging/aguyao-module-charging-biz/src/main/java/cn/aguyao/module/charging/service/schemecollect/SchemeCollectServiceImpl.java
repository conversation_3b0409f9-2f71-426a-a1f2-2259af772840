package cn.aguyao.module.charging.service.schemecollect;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectPageReqVO;
import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.schemecollect.SchemeCollectDO;
import cn.aguyao.module.charging.dal.mysql.schemecollect.SchemeCollectMapper;
import cn.aguyao.module.charging.enums.ChargeModeEnum;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.SCHEME_COLLECT_NOT_EXISTS;

/**
 * 收费方案—收费套餐 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SchemeCollectServiceImpl implements SchemeCollectService {

    @Resource
    private SchemeCollectMapper schemeCollectMapper;

    @Override
    public Long createSchemeCollect(SchemeCollectSaveReqVO createReqVO) {
        // 插入
        SchemeCollectDO schemeCollect = BeanUtils.toBean(createReqVO, SchemeCollectDO.class);
        schemeCollectMapper.insert(schemeCollect);
        // 返回
        return schemeCollect.getId();
    }

    @Override
    public void createSchemeCollect(List<SchemeCollectSaveReqVO> list, Long schemeId, ChargeModeEnum chargeModeEnum) {
        for (SchemeCollectSaveReqVO reqVO : list) {
            reqVO.setId(null);
            reqVO.setSchemeId(schemeId);
            reqVO.setType(chargeModeEnum.getValue());
            createSchemeCollect(reqVO);
        }
    }

    @Override
    public void updateSchemeCollect(SchemeCollectSaveReqVO updateReqVO) {
        // 校验存在
        validateSchemeCollectExists(updateReqVO.getId());
        // 更新
        SchemeCollectDO updateObj = BeanUtils.toBean(updateReqVO, SchemeCollectDO.class);
        schemeCollectMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSchemeCollect(List<SchemeCollectSaveReqVO> list, Long schemeId, ChargeModeEnum chargeModeEnum) {
        // 旧的全部删除
        schemeCollectMapper.deleteBySchemeId(schemeId, chargeModeEnum.getValue());

        // 新的全部新增
        if (CollUtil.isNotEmpty(list)) {
            createSchemeCollect(list, schemeId, chargeModeEnum);
        }
    }

    @Override
    public void deleteSchemeCollect(Long id) {
        // 校验存在
        validateSchemeCollectExists(id);
        // 删除
        schemeCollectMapper.deleteById(id);
    }

    private void validateSchemeCollectExists(Long id) {
        if (schemeCollectMapper.selectById(id) == null) {
            throw exception(SCHEME_COLLECT_NOT_EXISTS);
        }
    }

    @Override
    public SchemeCollectDO getSchemeCollect(Long id) {
        return schemeCollectMapper.selectById(id);
    }

    @Override
    public PageResult<SchemeCollectDO> getSchemeCollectPage(SchemeCollectPageReqVO pageReqVO) {
        return schemeCollectMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SchemeCollectDO> findBySchemeId(Long schemeId, int type) {
        return schemeCollectMapper.selectList(
                SchemeCollectDO::getSchemeId, schemeId,
                SchemeCollectDO::getType, type);
    }

}
