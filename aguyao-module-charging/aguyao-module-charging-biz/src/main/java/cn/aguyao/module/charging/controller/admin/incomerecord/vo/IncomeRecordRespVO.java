package cn.aguyao.module.charging.controller.admin.incomerecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 收入（收益） Response VO")
@Data
@ExcelIgnoreUnannotated
public class IncomeRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "22634")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "会员id， 也就是消费者id", example = "32465")
    @ExcelProperty("会员id， 也就是消费者id")
    private Long mbeId;

    @Schema(description = "小区id", example = "3393")
    @ExcelProperty("小区id")
    private Long communityId;

    @Schema(description = "伙伴id，也就是利润所得者id", example = "3393")
    @ExcelProperty("伙伴id，也就是利润所得者id")
    private Long partnerId;

    @Schema(description = "单笔收入比率")
    @ExcelProperty("单笔收入比率")
    private BigDecimal rate;

    @Schema(description = "单笔收入金额")
    @ExcelProperty("单笔收入金额")
    private BigDecimal amount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
