package cn.aguyao.module.charging.controller.admin.building.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 楼栋新增/修改 Request VO")
@Data
public class BuildingSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24573")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "楼栋名称", example = "王五")
    private String name;

    @Schema(description = "小区id", example = "")
    private Long communityId;

    @Schema(description = "设备数")
    private Integer deviceNum;

    @Schema(description = "充电中的设备数")
    private Integer chargingDeviceNum;

    @Schema(description = "待机设备数")
    private Integer standbyDeviceNum;

    @Schema(description = "断网设备数")
    private Integer networkDisconnDeviceNum;

    @Schema(description = "费率")
    private String rate;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "方案id")
    private String schemeId;

    ///////////////////////////////////
    @Schema(description = "小区名称", example = "")
    private String communityName;
}
