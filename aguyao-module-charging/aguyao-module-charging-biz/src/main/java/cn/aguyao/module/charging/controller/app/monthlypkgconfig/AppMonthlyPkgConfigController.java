package cn.aguyao.module.charging.controller.app.monthlypkgconfig;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.app.monthlypkgconfig.vo.AppMonthlyPkgConfigRespVO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgconfig.MonthlyPkgConfigDO;
import cn.aguyao.module.charging.service.monthlypkgconfig.MonthlyPkgConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户端小程序 - 包月配置")
@RestController
@RequestMapping("/charging/monthly-pkg-config")
@Validated
public class AppMonthlyPkgConfigController {

    @Resource
    private MonthlyPkgConfigService monthlyPkgConfigService;

    /**
     * 获取包月配置列表
     * @return
     */
    @GetMapping("/list")
    @Operation(summary = "获得包月配置列表")
//    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-config:query')")
    public CommonResult<List<AppMonthlyPkgConfigRespVO>> list() {
        List<MonthlyPkgConfigDO> list = monthlyPkgConfigService.list();
        return success(BeanUtils.toBean(list, AppMonthlyPkgConfigRespVO.class));
    }

}
