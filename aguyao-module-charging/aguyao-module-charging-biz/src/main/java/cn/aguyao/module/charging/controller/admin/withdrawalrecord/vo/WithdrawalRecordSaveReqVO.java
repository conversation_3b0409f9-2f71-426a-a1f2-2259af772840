package cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 提现记录新增/修改 Request VO")
@Data
public class WithdrawalRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "32603")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "主管id")
    private Long partnerId;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "申请金额")
    private BigDecimal applyAmount;

    @Schema(description = "提现银行名称", example = "芋艿")
    private String bankName;

    @Schema(description = "银行卡号")
    private String bankCardNum;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "申请状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
//    @NotNull(message = "申请状态不能为空")
    private Integer status;

}
