package cn.aguyao.module.charging.controller.app.repairrecord;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.module.charging.controller.app.device.vo.AppDeviceRepairReqVO;
import cn.aguyao.module.charging.service.repairrecord.RepairRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户端小程序 - 报修记录")
@RestController
@RequestMapping("/charging/repair-record")
@Validated
public class AppRepairRecordController {

    @Resource
    private RepairRecordService repairRecordService;

    /**
     * 故障报修
     * @param deviceUpdateReqVO
     * @return
     */
    @PutMapping("/fault-repair")
    @Operation(summary = "故障报修")
//    @PreAuthorize("@ss.hasPermission('charging:device:update')")
    public CommonResult<Boolean> faultRepair(@Valid @RequestBody AppDeviceRepairReqVO deviceUpdateReqVO) {
        repairRecordService.faultRepair(deviceUpdateReqVO);
        return success(true);
    }

}
