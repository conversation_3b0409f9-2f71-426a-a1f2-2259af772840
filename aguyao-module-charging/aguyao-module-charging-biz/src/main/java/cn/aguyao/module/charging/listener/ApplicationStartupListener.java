package cn.aguyao.module.charging.listener;

import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.service.BaseService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class ApplicationStartupListener implements ApplicationListener<ApplicationReadyEvent> {

    @Value("${spring.profiles.active}")
    private String env;

    private String active;


    @Value("${spring.profiles.active}")
    public void setActive(String active) {
        System.out.println("---------当前环境：" + active);
        if ("local".equals(active)) {
            SecurityFrameworkUtils.IS_LOCAL_ENV = true;
        }
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        System.out.println("Spring Boot 应用已启动并准备好处理请求！");
        // 可以在这里执行一些初始化后的操作，比如加载缓存数据、启动定时任务等
        BaseService.SERVER_UP = true;

        System.out.println("--------------当前环境：" + env);
    }
}
