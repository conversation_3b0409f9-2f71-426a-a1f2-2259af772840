package cn.aguyao.module.charging.controller.app.servicemanual.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 使用手册 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppServiceManualRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6332")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "标题")
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "内容，明细")
    @ExcelProperty("内容，明细")
    private String content;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
