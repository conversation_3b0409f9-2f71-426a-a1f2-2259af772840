package cn.aguyao.module.charging.dal.mysql.refundrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.refundrecord.RefundRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 退款记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RefundRecordMapper extends BaseMapperX<RefundRecordDO> {

    default PageResult<RefundRecordDO> selectPage(RefundRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RefundRecordDO>()
                .likeIfPresent(RefundRecordDO::getCode, reqVO.getCode())
                .likeIfPresent(RefundRecordDO::getMbeCode, reqVO.getMbeCode())
                .likeIfPresent(RefundRecordDO::getMobile, reqVO.getMobile())
                .eqIfPresent(RefundRecordDO::getApplyAmount, reqVO.getApplyAmount())
                .eqIfPresent(RefundRecordDO::getRefundSuccessAmount, reqVO.getRefundSuccessAmount())
                .eqIfPresent(RefundRecordDO::getApplyStatus, reqVO.getApplyStatus())
                .eqIfPresent(RefundRecordDO::getApprovalStatus, reqVO.getApprovalStatus())
                .eqIfPresent(RefundRecordDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(RefundRecordDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(RefundRecordDO::getType, reqVO.getType())
                .eqIfPresent(RefundRecordDO::getMpId, reqVO.getMpId())
                .orderByDesc(RefundRecordDO::getId));
    }

    default RefundRecordDO selectByOrderNo(String orderNo) {
        return selectOne(new LambdaQueryWrapperX<RefundRecordDO>()
                .eq(RefundRecordDO::getOrderNo, orderNo));
    }
}
