package cn.aguyao.module.charging.controller.mgr.device.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 设备 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceInfoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10825")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "设备编号")
    @ExcelProperty("设备编号")
    private String code;

    @Schema(description = "设备号")
    @ExcelProperty("设备号")
    private String device;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    /**
     * 信号强度0-31
     */
    @Schema(description = "信号强度0-31", example = "你说的对")
    @ExcelProperty("信号强度0-31")
    private int rssi;

    /**
     * 4G模块IMEI 15位IMEI号
     */
    @Schema(description = "4G模块IMEI 15位IMEI号", example = "你说的对")
    @ExcelProperty("4G模块IMEI 15位IMEI号")
    private String imei;

    /**
     * SIM卡ICCID 15位ICCID
     */
    @Schema(description = "SIM卡ICCID 15位ICCID", example = "你说的对")
    @ExcelProperty("SIM卡ICCID 15位ICCID")
    private String iccid;

    /**
     * 设备软件版本号LF_101_057_4G_Luat_V0020_ASR1802_720D
     */
    @Schema(description = "设备软件版本号", example = "你说的对")
    @ExcelProperty("设备软件版本号")
    private String version;

    /**
     * 服务器类型TCP,MQTT,IOT
     */
    @Schema(description = "服务器类型TCP,MQTT,IOT", example = "你说的对")
    @ExcelProperty("服务器类型TCP,MQTT,IOT")
    private String servertype;

    /**
     * 心跳间隔时间(秒) 60
     */
    @Schema(description = "心跳间隔时间(秒) 60", example = "你说的对")
    @ExcelProperty("心跳间隔时间(秒) 60")
    private int heart;

    /**
     * 最近访问时间
     */
    private String latestTime;

}
