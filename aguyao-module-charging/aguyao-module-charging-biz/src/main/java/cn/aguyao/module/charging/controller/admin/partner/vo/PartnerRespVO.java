package cn.aguyao.module.charging.controller.admin.partner.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 合作伙伴——小区管理人员（物业） Response VO")
@Data
@ExcelIgnoreUnannotated
public class PartnerRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16337")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String mobile;

    @Schema(description = "名称", example = "芋艿")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "分润点数")
    @ExcelProperty("分润点数")
    private BigDecimal profitSharingPoints;

    @Schema(description = "历史分润")
    @ExcelProperty("历史分润")
    private BigDecimal historicalProfitSharing;

    @Schema(description = "已提现分润")
    @ExcelProperty("已提现分润")
    private BigDecimal cashoutProfitSharing;

    @Schema(description = "管理的小区数")
    @ExcelProperty("管理的小区数")
    private Integer communityNum;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    private List<Long> communityIds;

    private Long mbeId;

    private String mbeCode;

    /**
     * 微信小程序 ID
     */
    private Long mpId;

}
