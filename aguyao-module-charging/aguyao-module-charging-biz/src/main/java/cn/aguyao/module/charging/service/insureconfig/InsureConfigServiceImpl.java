package cn.aguyao.module.charging.service.insureconfig;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.insureconfig.vo.InsureConfigPageReqVO;
import cn.aguyao.module.charging.controller.admin.insureconfig.vo.InsureConfigSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.insureconfig.InsureConfigDO;
import cn.aguyao.module.charging.dal.mysql.insureconfig.InsureConfigMapper;
import cn.aguyao.module.charging.enums.PrefixConstants;
import cn.aguyao.module.system.api.serial.SerialApi;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.INSURE_CONFIG_NOT_EXISTS;

/**
 * 投保配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InsureConfigServiceImpl implements InsureConfigService {

    @Resource
    private InsureConfigMapper insureConfigMapper;

    @Resource
    private SerialApi serialApi;

    @Override
    public Long createInsureConfig(InsureConfigSaveReqVO createReqVO) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_TBPZ);
        // 插入
        InsureConfigDO insureConfig = BeanUtils.toBean(createReqVO, InsureConfigDO.class);
        insureConfig.setCode(code);
        insureConfigMapper.insert(insureConfig);
        // 返回
        return insureConfig.getId();
    }

    @Override
    public void updateInsureConfig(InsureConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateInsureConfigExists(updateReqVO.getId());
        // 更新
        InsureConfigDO updateObj = BeanUtils.toBean(updateReqVO, InsureConfigDO.class);
        insureConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteInsureConfig(Long id) {
        // 校验存在
        validateInsureConfigExists(id);
        // 删除
        insureConfigMapper.deleteById(id);
    }

    private void validateInsureConfigExists(Long id) {
        if (insureConfigMapper.selectById(id) == null) {
            throw exception(INSURE_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public InsureConfigDO getInsureConfig(Long id) {
        return insureConfigMapper.selectById(id);
    }

    @Override
    public PageResult<InsureConfigDO> getInsureConfigPage(InsureConfigPageReqVO pageReqVO) {
        return insureConfigMapper.selectPage(pageReqVO);
    }

}
