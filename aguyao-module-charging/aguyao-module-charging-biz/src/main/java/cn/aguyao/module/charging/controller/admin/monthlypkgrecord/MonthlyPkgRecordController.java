package cn.aguyao.module.charging.controller.admin.monthlypkgrecord;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo.MonthlyPkgRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo.MonthlyPkgRecordRespVO;
import cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo.MonthlyPkgRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgrecord.MonthlyPkgRecordDO;
import cn.aguyao.module.charging.service.monthlypkgrecord.MonthlyPkgRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 包月记录")
@RestController
@RequestMapping("/charging/monthly-pkg-record")
@Validated
public class MonthlyPkgRecordController {

    @Resource
    private MonthlyPkgRecordService monthlyPkgRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建包月记录")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-record:create')")
    public CommonResult<Long> createMonthlyPkgRecord(@Valid @RequestBody MonthlyPkgRecordSaveReqVO createReqVO) {
        return success(monthlyPkgRecordService.createMonthlyPkgRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新包月记录")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-record:update')")
    public CommonResult<Boolean> updateMonthlyPkgRecord(@Valid @RequestBody MonthlyPkgRecordSaveReqVO updateReqVO) {
        monthlyPkgRecordService.updateMonthlyPkgRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除包月记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-record:delete')")
    public CommonResult<Boolean> deleteMonthlyPkgRecord(@RequestParam("id") Long id) {
        monthlyPkgRecordService.deleteMonthlyPkgRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得包月记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-record:query')")
    public CommonResult<MonthlyPkgRecordRespVO> getMonthlyPkgRecord(@RequestParam("id") Long id) {
        MonthlyPkgRecordDO monthlyPkgRecord = monthlyPkgRecordService.getMonthlyPkgRecord(id);
        return success(BeanUtils.toBean(monthlyPkgRecord, MonthlyPkgRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得包月记录分页")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-record:query')")
    public CommonResult<PageResult<MonthlyPkgRecordRespVO>> getMonthlyPkgRecordPage(@Valid MonthlyPkgRecordPageReqVO pageReqVO) {
        PageResult<MonthlyPkgRecordDO> pageResult = monthlyPkgRecordService.getMonthlyPkgRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MonthlyPkgRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出包月记录 Excel")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMonthlyPkgRecordExcel(@Valid MonthlyPkgRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MonthlyPkgRecordDO> list = monthlyPkgRecordService.getMonthlyPkgRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "包月记录.xls", "数据", MonthlyPkgRecordRespVO.class,
                        BeanUtils.toBean(list, MonthlyPkgRecordRespVO.class));
    }

}