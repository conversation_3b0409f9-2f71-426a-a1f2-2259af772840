package cn.aguyao.module.charging.service.auth;

import cn.aguyao.framework.common.enums.CommonStatusEnum;
import cn.aguyao.framework.common.enums.TerminalEnum;
import cn.aguyao.framework.common.enums.UserTypeEnum;
import cn.aguyao.framework.common.exception.ServerException;
import cn.aguyao.framework.common.util.monitor.TracerUtils;
import cn.aguyao.framework.common.util.servlet.ServletUtils;
import cn.aguyao.module.charging.controller.app.auth.vo.*;
import cn.aguyao.module.charging.controller.app.weixin.dto.SessionKeyDTO;
import cn.aguyao.module.charging.controller.app.weixin.vo.AppPhoneNumberReqVO;
import cn.aguyao.module.charging.convert.auth.AuthConvert;
import cn.aguyao.module.charging.dal.dataobject.partner.PartnerDO;
import cn.aguyao.module.charging.dal.dataobject.user.MpUserDO;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.service.mbe.MbeService;
import cn.aguyao.module.charging.service.mpuser.MpUserService;
import cn.aguyao.module.charging.service.partner.PartnerService;
import cn.aguyao.module.charging.service.weixin.WeiXinService;
import cn.aguyao.module.system.api.logger.LoginLogApi;
import cn.aguyao.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.aguyao.module.system.api.oauth2.OAuth2TokenApi;
import cn.aguyao.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.aguyao.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.aguyao.module.system.api.sms.SmsCodeApi;
import cn.aguyao.module.system.api.social.SocialClientApi;
import cn.aguyao.module.system.api.social.SocialUserApi;
import cn.aguyao.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.aguyao.module.system.api.social.dto.SocialWxPhoneNumberInfoRespDTO;
import cn.aguyao.module.system.enums.logger.LoginLogTypeEnum;
import cn.aguyao.module.system.enums.logger.LoginResultEnum;
import cn.aguyao.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.aguyao.module.system.enums.sms.SmsSceneEnum;
import cn.aguyao.module.system.enums.social.SocialTypeEnum;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.AUTH_LOGIN_USER_DISABLED;

/**
 * 会员的认证 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class MbeAuthServiceImpl implements MbeAuthService {

    @Resource
    private MbeService userService;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private LoginLogApi loginLogApi;
    @Resource
    private SocialUserApi socialUserApi;
    @Resource
    private SocialClientApi socialClientApi;
    @Resource
    private OAuth2TokenApi oauth2TokenApi;

    @Resource
    private WeiXinService weiXinService;

    @Resource
    private MpUserService mpUserService;

    @Resource
    private PartnerService partnerService;


    @Override
    public AppAuthLoginRespVO login(AppAuthLoginReqVO reqVO) {
        // 使用手机 + 密码，进行登录。
        MbeDO user = login0(reqVO.getMobile(), reqVO.getPassword());

        // 如果 socialType 非空，说明需要绑定社交用户
        String openid = null;
        if (reqVO.getSocialType() != null) {
            openid = socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
                    reqVO.getSocialType(), reqVO.getSocialCode(), reqVO.getSocialState()));
        }


        //
        Boolean isPartner = false;
        PartnerDO partner = partnerService.getPartnerByMpId(user.getMpUserId());
        if (Objects.nonNull(partner)) {
            isPartner = true;
        }
        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), reqVO.getMobile(), LoginLogTypeEnum.LOGIN_MOBILE, isPartner);
    }

//    @Override
//    @Transactional
//    public AppAuthLoginRespVO smsLogin(AppAuthSmsLoginReqVO reqVO) {
//        // 校验验证码
//        String userIp = getClientIP();
//        smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convert(reqVO, SmsSceneEnum.MEMBER_LOGIN.getScene(), userIp));
//
//        // 获得获得注册用户
//        MemberUserDO user = userService.createUserIfAbsent(reqVO.getMobile(), userIp, getTerminal());
//        Assert.notNull(user, "获取用户失败，结果为空");
//
//        // 如果 socialType 非空，说明需要绑定社交用户
//        String openid = null;
//        if (reqVO.getSocialType() != null) {
//            openid = socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
//                    reqVO.getSocialType(), reqVO.getSocialCode(), reqVO.getSocialState()));
//        }
//
//        // 创建 Token 令牌，记录登录日志
//        return createTokenAfterLoginSuccess(user.getId(), reqVO.getMobile(), LoginLogTypeEnum.LOGIN_SMS, openid);
//    }
//

//    /**
//     * @param reqVO
//     * @return
//     */
//    @Override
//    @Transactional
//    public AppAuthLoginRespVO socialLogin(AppAuthSocialLoginReqVO reqVO) {
//        // 使用 code 授权码，进行登录。然后，获得到绑定的用户编号
//        SocialUserRespDTO socialUser = socialUserApi.getSocialUserByCode(UserTypeEnum.MEMBER.getValue(), reqVO.getType(),
//                reqVO.getCode(), reqVO.getState());
//        if (socialUser == null) {
//            throw exception(AUTH_SOCIAL_USER_NOT_FOUND);
//        }
//
//        // 情况一：已绑定，直接读取用户信息
//        MemberUserDO user;
//        if (socialUser.getUserId() != null) {
//            user = userService.getUser(socialUser.getUserId());
//        // 情况二：未绑定，注册用户 + 绑定用户
//        } else {
//            user = userService.createUser(socialUser.getNickname(), socialUser.getAvatar(), getClientIP(), getTerminal());
//            socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
//                    reqVO.getType(), reqVO.getCode(), reqVO.getState()));
//        }
//        if (user == null) {
//            throw exception(USER_NOT_EXISTS);
//        }
//
//        // 创建 Token 令牌，记录登录日志
//        return createTokenAfterLoginSuccess(user.getId(), user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL, socialUser.getOpenid());
//    }

    @Override
    public AppAuthLoginRespVO weixinMiniAppLogin(AppAuthWeixinMiniAppLoginReqVO reqVO) {
        // 获得对应的手机号信息
        SocialWxPhoneNumberInfoRespDTO phoneNumberInfo = socialClientApi.getWxMaPhoneNumberInfo(
                UserTypeEnum.MEMBER.getValue(), reqVO.getPhoneCode());
        Assert.notNull(phoneNumberInfo, "获得手机信息失败，结果为空");

        // 获得注册用户
        MbeDO user = userService.createUserIfAbsent(phoneNumberInfo.getPurePhoneNumber(),
                getClientIP(), TerminalEnum.WECHAT_MINI_PROGRAM.getTerminal());
        Assert.notNull(user, "获取用户失败，结果为空");

        // 绑定社交用户
        String openid = socialUserApi.bindSocialUser(new SocialUserBindReqDTO(user.getId(), getUserType().getValue(),
                SocialTypeEnum.WECHAT_MINI_APP.getType(), reqVO.getLoginCode(), reqVO.getState()));

        Boolean isPartner = false;
        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), user.getMobile(), LoginLogTypeEnum.LOGIN_SOCIAL, isPartner);
    }

//    private AppAuthLoginRespVO createTokenAfterLoginSuccess(MemberUserDO user, String mobile,
//                                                            LoginLogTypeEnum logType, String openid) {
//        // 插入登陆日志
//        createLoginLog(user.getId(), mobile, logType, LoginResultEnum.SUCCESS);
//        // 创建 Token 令牌
//        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi.createAccessToken(new OAuth2AccessTokenCreateReqDTO()
//                .setUserId(user.getId()).setUserType(getUserType().getValue())
//                .setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT));
//        // 构建返回结果
//        return AuthConvert.INSTANCE.convert(accessTokenRespDTO, openid);
//    }

    private AppAuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String mobile,
                                                            LoginLogTypeEnum logType, Boolean isPartner) {
        // 插入登陆日志
        createLoginLog(userId, mobile, logType, LoginResultEnum.SUCCESS);
        // 创建 Token 令牌
        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi.createAccessToken(new OAuth2AccessTokenCreateReqDTO()
                .setUserId(userId).setUserType(getUserType().getValue()).setIsPartner(isPartner)
                .setClientId(OAuth2ClientConstants.CLIENT_ID_MINI_PROGRAM));
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenRespDTO);
    }

    @Override
    public String getSocialAuthorizeUrl(Integer type, String redirectUri) {
        return socialClientApi.getAuthorizeUrl(type, UserTypeEnum.MEMBER.getValue(), redirectUri);
    }

    private MbeDO login0(String mobile, String password) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_MOBILE;
        // 校验账号是否存在
        MbeDO user = userService.getUserByMobile(mobile);
        if (user == null) {
            createLoginLog(null, mobile, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), mobile, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), mobile, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        return user;
    }

    private void createLoginLog(Long userId, String mobile, LoginLogTypeEnum logType, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(mobile);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogApi.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
            userService.updateUserLogin(userId, getClientIP());
        }
    }

    @Override
    public void logout(String token) {
        // 删除访问令牌
        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi.removeAccessToken(token);
        if (accessTokenRespDTO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenRespDTO.getUserId());
    }

    @Override
    public void sendSmsCode(Long userId, AppAuthSmsSendReqVO reqVO) {
        // 情况 1：如果是修改手机场景，需要校验新手机号是否已经注册，说明不能使用该手机了 todo
//        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene())) {
//            MemberUserDO user = userService.getUserByMobile(reqVO.getMobile());
//            if (user != null && !Objects.equals(user.getId(), userId)) {
//                throw exception(AUTH_MOBILE_USED);
//            }
//        }
//        // 情况 2：如果是重置密码场景，需要校验手机号是存在的
//        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_RESET_PASSWORD.getScene())) {
//            MemberUserDO user = userService.getUserByMobile(reqVO.getMobile());
//            if (user == null) {
//                throw exception(USER_MOBILE_NOT_EXISTS);
//            }
//        }
        // 情况 3：如果是修改密码场景，需要查询手机号，无需前端传递
        if (Objects.equals(reqVO.getScene(), SmsSceneEnum.MEMBER_UPDATE_PASSWORD.getScene())) {
            MbeDO user = userService.getMember(userId);
            // TODO 芋艿：后续 member user 手机非强绑定，这块需要做下调整；
            reqVO.setMobile(user.getMobile());
        }

        // 执行发送
        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()));
    }

    @Override
    public void validateSmsCode(Long userId, AppAuthSmsValidateReqVO reqVO) {
        smsCodeApi.validateSmsCode(AuthConvert.INSTANCE.convert(reqVO));
    }

    @Override
    public AppAuthLoginRespVO refreshToken(String refreshToken) {
        OAuth2AccessTokenRespDTO accessTokenDO = oauth2TokenApi.refreshAccessToken(refreshToken,
                OAuth2ClientConstants.CLIENT_ID_MINI_PROGRAM);
        return AuthConvert.INSTANCE.convert(accessTokenDO, null);
    }

    @Override
    public String codeLogin(String code) throws Exception{
        // 获得对应的手机号信息
        SessionKeyDTO sessionKeyDTO = weiXinService.jscode2session(code);
        if (sessionKeyDTO == null || BooleanUtil.isFalse(sessionKeyDTO.getSuccess())) {
            throw new ServerException(sessionKeyDTO.getErrcode(), sessionKeyDTO.getErrmsg());
        }

        Long mpUserId = weiXinService.createUserIfAbsent(sessionKeyDTO.getOpenid(), sessionKeyDTO.getUnionid(), sessionKeyDTO.getSessionKey());

        // 创建 Token 令牌，记录登录日志
//        return createTokenAfterLoginSuccess(mpUserId, null, LoginLogTypeEnum.LOGIN_CODE_USER, sessionKeyDTO.getOpenid());
        return sessionKeyDTO.getOpenid();
    }

    @Override
    public AppAuthLoginRespVO authByPhoneNumber(AppPhoneNumberReqVO reqVO) {
        log.info("------uthByPhoneNumber 请求参数reqVO：{}", JSON.toJSONString(reqVO));
        String mobile = weiXinService.getPhoneNumber(reqVO);
        MpUserDO user = mpUserService.getUserByOpenid(reqVO.getOpenid());
        userService.createMbeIfAbsent(user.getId(), mobile, UserTypeEnum.MEMBER);

        Boolean isPartner = false;
        PartnerDO partner = partnerService.getPartnerByMpId(user.getId());
        if (Objects.nonNull(partner) && Objects.equals(0, partner.getStatus())) {
            // 状态正常
            isPartner = true;
        }
        return createTokenAfterLoginSuccess(user.getId(), mobile, LoginLogTypeEnum.LOGIN_CODE_USER, isPartner);
    }

    private void createLogoutLog(Long userId) {
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(LoginLogTypeEnum.LOGOUT_SELF.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(getMobile(userId));
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        loginLogApi.createLoginLog(reqDTO);
    }

    private String getMobile(Long userId) {
        if (userId == null) {
            return null;
        }
        MbeDO user = userService.getMember(userId);
        return user != null ? user.getMobile() : null;
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.MEMBER;
    }

}
