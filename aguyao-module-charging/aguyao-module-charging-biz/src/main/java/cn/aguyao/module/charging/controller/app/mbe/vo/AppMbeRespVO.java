package cn.aguyao.module.charging.controller.app.mbe.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 会员，即充电的用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppMbeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15702")
//    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String mobile;

    @Schema(description = "所属小区id", example = "24399")
//    @ExcelProperty("所属小区id")
    private Long belongCommunityId;

    @Schema(description = "当前所在小区id, 默认当前小区", example = "24399")
    @ExcelProperty("当前所在小区id")
    private Long currentCommunityId;

    @Schema(description = "充值余额")
    @ExcelProperty("充值余额")
    private BigDecimal rechargeBalance;

    @Schema(description = "赠送余额")
    @ExcelProperty("赠送余额")
    private BigDecimal giftBalance;

    @Schema(description = "包月卡小区id", example = "19682")
//    @ExcelProperty("包月卡小区id")
    private Long monthlyPassCommunityId;

    @Schema(description = "包月卡有效期")
    @ExcelProperty("包月卡有效期")
    private LocalDateTime monthlyPassDuration;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;




    @ExcelProperty("所属小区名称")
    @Schema(description = "所属小区名称", example = "源昌豪庭")
    private String belongCommunityName;

    @ExcelProperty("包月卡小区名称")
    @Schema(description = "包月卡小区名称", example = "源昌豪庭")
    private String monthlyPassCommunityName;
}
