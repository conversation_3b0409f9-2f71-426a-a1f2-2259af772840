package cn.aguyao.module.charging.controller.admin.withdrawalrecord;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo.WithdrawalRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo.WithdrawalRecordRespVO;
import cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo.WithdrawalRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.withdrawalrecord.WithdrawalRecordDO;
import cn.aguyao.module.charging.service.withdrawalrecord.WithdrawalRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 提现记录")
@RestController
@RequestMapping("/charging/withdrawal-record")
@Validated
public class WithdrawalRecordController {

    @Resource
    private WithdrawalRecordService withdrawalRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建提现记录")
    @PreAuthorize("@ss.hasPermission('charging:withdrawal-record:create')")
    public CommonResult<Long> createWithdrawalRecord(@Valid @RequestBody WithdrawalRecordSaveReqVO createReqVO) {
        return success(withdrawalRecordService.createWithdrawalRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新提现记录")
    @PreAuthorize("@ss.hasPermission('charging:withdrawal-record:update')")
    public CommonResult<Boolean> updateWithdrawalRecord(@Valid @RequestBody WithdrawalRecordSaveReqVO updateReqVO) {
        withdrawalRecordService.updateWithdrawalRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除提现记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:withdrawal-record:delete')")
    public CommonResult<Boolean> deleteWithdrawalRecord(@RequestParam("id") Long id) {
        withdrawalRecordService.deleteWithdrawalRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得提现记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:withdrawal-record:query')")
    public CommonResult<WithdrawalRecordRespVO> getWithdrawalRecord(@RequestParam("id") Long id) {
        WithdrawalRecordDO withdrawalRecord = withdrawalRecordService.getWithdrawalRecord(id);
        return success(BeanUtils.toBean(withdrawalRecord, WithdrawalRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得提现记录分页")
    @PreAuthorize("@ss.hasPermission('charging:withdrawal-record:query')")
    public CommonResult<PageResult<WithdrawalRecordRespVO>> getWithdrawalRecordPage(@Valid WithdrawalRecordPageReqVO pageReqVO) {
        PageResult<WithdrawalRecordDO> pageResult = withdrawalRecordService.getWithdrawalRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WithdrawalRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出提现记录 Excel")
    @PreAuthorize("@ss.hasPermission('charging:withdrawal-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWithdrawalRecordExcel(@Valid WithdrawalRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WithdrawalRecordDO> list = withdrawalRecordService.getWithdrawalRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "提现记录.xls", "数据", WithdrawalRecordRespVO.class,
                        BeanUtils.toBean(list, WithdrawalRecordRespVO.class));
    }

}