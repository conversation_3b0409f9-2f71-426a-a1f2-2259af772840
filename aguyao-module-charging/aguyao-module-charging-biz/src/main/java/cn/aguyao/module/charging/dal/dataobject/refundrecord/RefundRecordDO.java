package cn.aguyao.module.charging.dal.dataobject.refundrecord;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 退款记录 DO
 *
 * <AUTHOR>
 */
@TableName("charging_refund_record")
@KeySequence("charging_refund_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 编号
     */
    private String code;
    /**
     * 用户编号
     */
    private String mbeCode;
    /**
     * 用户手机号
     */
    private String mobile;
    /**
     * 申请金额
     */
    private BigDecimal applyAmount;
    /**
     * 退款成功金额
     */
    private BigDecimal refundSuccessAmount;
    /**
     * 申请状态
     */
    private Integer applyStatus;
    /**
     * 审批状态
     */
    private Integer approvalStatus;
    /**
     * 备注
     */
    private String remark;

    /**
     * 原因
     */
    private String reason;

    /**
     * 退款类型，0：余额退款；1：包月退款；2：临充退款；
     */
    private Integer type;


    private Long mpId;

    /**
     * 退款状态
     */
    private Integer status;

    /**
     * 订单编号
     */
    private String orderNo;
}
