package cn.aguyao.module.charging.controller.app.banner.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "小程序端 - 楼栋 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppBannnerRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24573")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "标签", example = "冬季寒梅")
    @ExcelProperty("标签")
    private String tag;

    @Schema(description = "图片地址", example = "")
    @ExcelProperty("图片地址")
    private String imageUrl;

    @Schema(description = "跳转链接")
    @ExcelProperty("跳转链接")
    private String jumpLink;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;
}
