package cn.aguyao.module.charging.dal.dataobject.building;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 楼栋 DO
 *
 * <AUTHOR>
 */
@TableName("charging_building")
@KeySequence("charging_building_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuildingDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 编号
     */
    private String code;
    /**
     * 楼栋名称
     */
    private String name;
    /**
     * 所属小区ID
     */
    private Long communityId;
    /**
     * 设备数
     */
    private Integer deviceNum;
    /**
     * 充电中的设备数
     */
    private Integer chargingDeviceNum;
    /**
     * 待机设备数
     */
    private Integer standbyDeviceNum;
    /**
     * 断网设备数
     */
    private Integer networkDisconnDeviceNum;
    /**
     * 费率
     */
    private String rate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;


    /**
     * 收费方案id
     */
    private Long schemeId;

    ///////////////////////////////////
    @TableField(exist = false)
    private String communityName;

    /**
     * 方案名称
     */
    @TableField(exist = false)
    private String schemeName;
}
