package cn.aguyao.module.charging.controller.admin.partner.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 合作伙伴——小区管理人员（物业）分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PartnerPageReqVO extends PageParam {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "名称", example = "芋艿")
    private String name;

    @Schema(description = "分润点数")
    private BigDecimal profitSharingPoints;

    @Schema(description = "历史分润")
    private BigDecimal historicalProfitSharing;

    @Schema(description = "已提现分润")
    private BigDecimal cashoutProfitSharing;

    @Schema(description = "管理的小区数")
    private Integer communityNum;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
