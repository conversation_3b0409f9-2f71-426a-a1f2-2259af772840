package cn.aguyao.module.charging.controller.app.mqtt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;


@Schema(description = "管理后台 - 会员，即充电的用户新增/修改 Request VO")
@Data
public class FuncConfigReqVO {

    @Schema(description = "设备代码")
    @NotBlank(message = "设备代码不能为空")
    private String device;

    @Schema(description = "充满自停功率,15瓦(范围1-100瓦)功率小于设定值时自停")
    private Integer power;

    @Schema(description = "单次刷卡扣除金额,10(范围0-999元)单次刷卡扣除金额，单位：元")
    private Integer money;

    @Schema(description = "单次刷卡启动时长，60分钟(范围0-999分钟)单次刷卡时间长")
    private Integer rfidTime;

    @Schema(description = "单次投币时长,60分钟(范围0-999分钟)单次投币时间长")
    private Integer coinTime;

    @Schema(description = "高温报警温度,80度(范围1-100度)温度高于设定值时报警")
    private Integer temp;

    @Schema(description = "充满自停关闭时间,30秒(范围1-65535秒)负载小于自停功率后，输出关闭等待时间。")
    private Integer offTime1;

    @Schema(description = "插头拨出关闭时间，30秒(范围1-250秒)插头拨出后关闭时间")
    private Integer offTime2;

}


