package cn.aguyao.module.charging.controller.admin.incomerecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 收入（收益）新增/修改 Request VO")
@Data
public class IncomeRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "22634")
    private Long id;

    @Schema(description = "会员id， 也就是消费者id", example = "32465")
    private Long mbeId;

    @Schema(description = "小区id", example = "3393")
    private Long communityId;

    @Schema(description = "伙伴id，也就是利润所得者id", example = "3393")
    private Long partnerId;

    @Schema(description = "单笔收入比率")
    private BigDecimal rate;

    @Schema(description = "单笔收入金额")
    private BigDecimal amount;

}
