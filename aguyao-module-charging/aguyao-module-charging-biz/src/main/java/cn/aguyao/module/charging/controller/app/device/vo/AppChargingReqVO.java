package cn.aguyao.module.charging.controller.app.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;


@Schema(description = " 停/充电请求")
@Data
public class AppChargingReqVO {

    @NotNull(message = "设备代码不能为空")
    @Schema(description = "设备代码， 主机编号")
    private String device;

    @Schema(description = "功能代码， 3:打开指定端口输出;  4:关闭指定端口220V输出")
    private Integer cmd;

    @NotNull(message = "端口代码不能为空")
    @Schema(description = "端口代码， 1为1号端口，2为2号端口 (255为全部端口)")
    private Integer port;

    @Schema(description = "输出时间， 1000 秒(最大68719476735秒)")
    private Integer time;

    @Schema(description = "输出功率,  0-1000W，0为不限制功率,充满自停。")
    private Integer power;

    @Schema(description = "自启动功能,   0为关闭自启动，1为打开自启动")
    private Integer autoopen;

//    /**
//     * mp user id
//     */
//    private Long userId;
}
