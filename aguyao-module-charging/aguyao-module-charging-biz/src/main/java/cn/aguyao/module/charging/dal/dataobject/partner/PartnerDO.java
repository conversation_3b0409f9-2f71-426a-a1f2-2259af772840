package cn.aguyao.module.charging.dal.dataobject.partner;

import cn.aguyao.framework.common.enums.TerminalEnum;
import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商户——小区管理人员（物业） DO
 *
 * <AUTHOR>
 */
@TableName("charging_partner")
@KeySequence("charging_partner_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartnerDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 微信小程序 ID
     */
    private Long mpId;
    /**
     * 编号
     */
    private String code;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 密码
     */
    private String password;
    /**
     * 名称
     */
    private String name;
    /**
     * 分润点数
     */
    private BigDecimal profitSharingPoints;
    /**
     * 历史分润
     */
    private BigDecimal historicalProfitSharing;
    /**
     * 已提现分润
     */
    private BigDecimal cashoutProfitSharing;
    /**
     * 管理的小区数
     */
    private Integer communityNum;
    /**
     * 备注
     */
    private String remark;
    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;



    /**
     * 注册 IP
     */
    private String registerIp;
    /**
     * 注册终端
     * 枚举 {@link TerminalEnum}
     */
    private Integer registerTerminal;
    /**
     * 最后登录IP
     */
    private String loginIp;
    /**
     * 最后登录时间
     */
    private LocalDateTime loginDate;

    /**
     * 可提现金额
     */
    private BigDecimal canWithdraw;

    /**
     * 已提现金额
     */
    private BigDecimal withdrawn;

    /**
     * 冻结金额
     */
    private BigDecimal blockedBalances;


    @TableField(exist = false)
    private List<Long> communityIds;


    @TableField(exist = false)
    private Long mbeId;

    @TableField(exist = false)
    private String mbeCode;

}
