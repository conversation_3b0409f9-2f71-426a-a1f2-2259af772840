package cn.aguyao.module.charging.service.partnercommunity;

import cn.aguyao.module.charging.dal.dataobject.partnercommunity.PartnerCommunityDO;

import java.util.List;

/**
 * 商户——小区管理人员（物业） Service 接口
 *
 * <AUTHOR>
 */
public interface PartnerCommunityService {

    List<PartnerCommunityDO> getListByMpId(Long mpId);

    /**
     * 删除商户——小区管理人员（物业）
     *
     * @param mpId
     */
    void deleteByMpId(Long mpId);

    /**
     * 新增或修改商户跟小区的关联关系
     * @param mpId
     * @param partnerId
     * @param communityIds
     * @return
     */
    int createOrUpdatePartnerCommunity(Long mpId, Long partnerId, List<Long> communityIds);

}
