package cn.aguyao.module.charging.service.insurerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.insurerecord.vo.InsureRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.insurerecord.vo.InsureRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.insurerecord.InsureRecordDO;

import javax.validation.Valid;

/**
 * 投保记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InsureRecordService {

    /**
     * 创建投保记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInsureRecord(@Valid InsureRecordSaveReqVO createReqVO);

    /**
     * 创建投保记录
     *
     * @param insureRecord 创建信息
     * @return 编号
     */
    Long createInsureRecord(InsureRecordDO insureRecord);

    /**
     * 更新投保记录
     *
     * @param updateReqVO 更新信息
     */
    void updateInsureRecord(@Valid InsureRecordSaveReqVO updateReqVO);

    /**
     * 删除投保记录
     *
     * @param id 编号
     */
    void deleteInsureRecord(Long id);

    /**
     * 获得投保记录
     *
     * @param id 编号
     * @return 投保记录
     */
    InsureRecordDO getInsureRecord(Long id);

    /**
     * 获得投保记录分页
     *
     * @param pageReqVO 分页查询
     * @return 投保记录分页
     */
    PageResult<InsureRecordDO> getInsureRecordPage(InsureRecordPageReqVO pageReqVO);

}
