package cn.aguyao.module.charging.controller.app.device.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备使用情况
 */
@Schema(description = "管理后台 - 设备 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppDeviceUseStateRespVO {

    @Schema(description = "设备编号")
    @ExcelProperty("设备编号")
    private String device;

    @Schema(description = "端口信息JSON")
    @ExcelProperty("端口信息JSON")
    private JSONArray portInfo;

    /**
     * 端口状态
     * 0: 未使用
     * 1: 部分使用
     * 2: 全部使用
     */
//    @Schema(description = "设备是否使用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @ExcelProperty("设备是否使用")
//    private Integer portState;

    @Schema(description = "当portState为1或者2时，是否当前用户使用", example = "1")
    @ExcelProperty("是否当前用户使用")
    private Boolean currentUser;

    @Schema(description = "当前用户充电的端口", example = "1")
    @ExcelProperty("当前用户充电的端口")
    private Integer portNum;

    /**
     * 充电状态
     * 1：充电中
     * 2：已完成
     * 3：异常
     */
    @Schema(description = "充电状态", example = "1/2/3")
    @ExcelProperty("充电状态")
    private Integer chargingState;

    @Schema(description = "充值订单id，跳转使用的", example = "1")
    @ExcelProperty("充值订单id")
    private Long orderId;
}
