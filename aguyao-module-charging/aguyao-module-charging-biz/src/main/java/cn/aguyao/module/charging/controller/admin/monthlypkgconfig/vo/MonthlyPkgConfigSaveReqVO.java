package cn.aguyao.module.charging.controller.admin.monthlypkgconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 包月配置新增/修改 Request VO")
@Data
public class MonthlyPkgConfigSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10348")
    private Long id;

    @Schema(description = "小区id", example = "29573")
    private Long communityId;

    @Schema(description = "编码", example = "27013")
    private String code;

//    @Schema(description = "一个月价格", example = "27013")
//    private BigDecimal oneMonthPrice;
//
//    @Schema(description = "三个月价格", example = "14995")
//    private BigDecimal threeMonthsPrice;
//
//    @Schema(description = "六个月价格", example = "8755")
//    private BigDecimal sixMonthsPrice;
//
//    @Schema(description = "一年价格", example = "13497")
//    private BigDecimal oneYearPrice;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

    @Schema(description = "排序", example = "2")
    private Integer sort;

    @Schema(description = "价格", example = "2")
    private String price;

    @Schema(description = "提示", example = "2")
    private String title;


    @Schema(description = "小区名称", example = "2")
    private String communityName;

    /**
     * 月数
     */
    @Schema(description = "月数", example = "2")
    private Integer monthNum;
}
