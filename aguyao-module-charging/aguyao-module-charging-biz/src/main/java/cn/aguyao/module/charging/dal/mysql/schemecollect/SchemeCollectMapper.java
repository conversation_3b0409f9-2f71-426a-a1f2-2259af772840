package cn.aguyao.module.charging.dal.mysql.schemecollect;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.schemecollect.SchemeCollectDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 收费方案—收费套餐 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SchemeCollectMapper extends BaseMapperX<SchemeCollectDO> {

    default PageResult<SchemeCollectDO> selectPage(SchemeCollectPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SchemeCollectDO>()
                .eqIfPresent(SchemeCollectDO::getSchemeId, reqVO.getSchemeId())
                .eqIfPresent(SchemeCollectDO::getAmount, reqVO.getAmount())
                .eqIfPresent(SchemeCollectDO::getDescription, reqVO.getDescription())
                .eqIfPresent(SchemeCollectDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(SchemeCollectDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SchemeCollectDO::getId));
    }

    default void deleteBySchemeId(Long schemeId, Integer type) {
        update(new LambdaUpdateWrapper<SchemeCollectDO>()
                .eq(SchemeCollectDO::getSchemeId, schemeId)
                .eq(SchemeCollectDO::getType, type)
                .set(SchemeCollectDO::getDeleted, true));

    }
}
