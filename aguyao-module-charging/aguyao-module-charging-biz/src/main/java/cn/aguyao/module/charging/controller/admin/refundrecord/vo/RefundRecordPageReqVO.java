package cn.aguyao.module.charging.controller.admin.refundrecord.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 退款记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RefundRecordPageReqVO extends PageParam {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "申请金额")
    private BigDecimal applyAmount;

    @Schema(description = "退款成功金额")
    private BigDecimal refundSuccessAmount;

    @Schema(description = "申请状态", example = "2")
    private Integer applyStatus;

    @Schema(description = "审批状态", example = "1")
    private Integer approvalStatus;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间，申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "原因", example = "太贵了")
    private String reason;

    @Schema(description = "退款类型", example = "1")
    private Integer type;

    private Long mpId;
}
