package cn.aguyao.module.charging.controller.admin.mpuser.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 微信小程序粉丝 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MpUserRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "6765")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "公众号 appId", example = "16844")
    @ExcelProperty("公众号 appId")
    private String appId;

    @Schema(description = "用户唯一标识", example = "16176")
    @ExcelProperty("用户唯一标识")
    private String openid;

    @Schema(description = "微信生态唯一标识", example = "9495")
    @ExcelProperty("微信生态唯一标识")
    private String unionId;

    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String mobile;

    @Schema(description = "昵称", example = "张三")
    @ExcelProperty("昵称")
    private String nickname;

    @Schema(description = "头像地址", example = "https://www.iocoder.cn")
    @ExcelProperty("头像地址")
    private String headImageUrl;

    @Schema(description = "语言")
    @ExcelProperty("语言")
    private String language;

    @Schema(description = "国家")
    @ExcelProperty("国家")
    private String country;

    @Schema(description = "省份")
    @ExcelProperty("省份")
    private String province;

    @Schema(description = "城市")
    @ExcelProperty("城市")
    private String city;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "关注状态, 1. 开启 - 已关注; 2. 禁用 - 取消关注", example = "2")
    @ExcelProperty("关注状态, 1. 开启 - 已关注; 2. 禁用 - 取消关注")
    private Integer subscribeStatus;

    @Schema(description = "关注时间")
    @ExcelProperty("关注时间")
    private LocalDateTime subscribeTime;

    @Schema(description = "取消关注时间")
    @ExcelProperty("取消关注时间")
    private LocalDateTime unsubscribeTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
