package cn.aguyao.module.charging.dal.mysql.building;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.building.vo.BuildingPageReqVO;
import cn.aguyao.module.charging.controller.mgr.community.vo.MgrBuildingInfoVO;
import cn.aguyao.module.charging.dal.dataobject.building.BuildingDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 楼栋 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BuildingMapper extends BaseMapperX<BuildingDO> {

    default PageResult<BuildingDO> selectPage(BuildingPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BuildingDO>()
                .likeIfPresent(BuildingDO::getCode, reqVO.getCode())
                .likeIfPresent(BuildingDO::getName, reqVO.getName())
                .eqIfPresent(BuildingDO::getDeviceNum, reqVO.getDeviceNum())
                .eqIfPresent(BuildingDO::getChargingDeviceNum, reqVO.getChargingDeviceNum())
                .eqIfPresent(BuildingDO::getStandbyDeviceNum, reqVO.getStandbyDeviceNum())
                .eqIfPresent(BuildingDO::getNetworkDisconnDeviceNum, reqVO.getNetworkDisconnDeviceNum())
                .eqIfPresent(BuildingDO::getRate, reqVO.getRate())
                .eqIfPresent(BuildingDO::getRemark, reqVO.getRemark())
                .eqIfPresent(BuildingDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(BuildingDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BuildingDO::getId));
    }

    default List<BuildingDO> selectByCommunityId(Long communityId) {
        return selectList(new LambdaQueryWrapperX<BuildingDO>()
                .eq(BuildingDO::getCommunityId, communityId)
                .orderByDesc(BuildingDO::getId));
    }

    @Select("SELECT " +
            "    t1.id," +
            "    t1.name bname," +
            "    t1.remark remark," +
            "    count(t2.id) totalCount " +
            " FROM " +
            "    charging_building  t1 " +
            " LEFT JOIN charging_device t2 ON t1.id = t2.building_id " +
            " where 1=1 and t2.deleted = 0 and t1.community_id = #{communityId}" +
            " GROUP BY t1.id order by t1.name ")
    List<MgrBuildingInfoVO> selectCountList(Long communityId);

    @Select("select t1.* from charging_building t1 " +
            " left join charging_device t2 on t2.building_id = t1.id " +
            " where t2.device = #{device} ")
    BuildingDO getRateInfoByDevice(String device);
}
