package cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 包月记录新增/修改 Request VO")
@Data
public class MonthlyPkgRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "21264")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "消费金额")
    private BigDecimal amount;

    @Schema(description = "购买时间")
    private LocalDateTime purchaseTime;

    @Schema(description = "包月小区id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2318")
    @NotNull(message = "包月小区id不能为空")
    private Long belongCommunityId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

}
