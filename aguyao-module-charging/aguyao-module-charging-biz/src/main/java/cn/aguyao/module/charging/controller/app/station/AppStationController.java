package cn.aguyao.module.charging.controller.app.station;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.app.station.vo.AppStationPageReqVO;
import cn.aguyao.module.charging.controller.app.station.vo.AppStationRespVO;
import cn.aguyao.module.charging.service.community.CommunityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序端 - 电站")
@RestController
@RequestMapping("/charging/station")
@Validated
public class AppStationController {


    @Resource
    private CommunityService communityService;


    /**
     * 获取附件电站信息
     * @param reqVO
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "获取各个电站信息")
    public CommonResult<PageResult<AppStationRespVO>> getBuildingPage(@Valid AppStationPageReqVO reqVO) {
        PageResult<AppStationRespVO> page = communityService.getStationPage(reqVO);
        return success(page);
    }


}
