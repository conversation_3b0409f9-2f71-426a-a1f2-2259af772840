package cn.aguyao.module.charging.controller.app.refundrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 退款记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppRefundRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13144")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "用户编号")
    @ExcelProperty("用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    @ExcelProperty("用户手机号")
    private String mobile;

    @Schema(description = "申请金额")
    @ExcelProperty("申请金额")
    private BigDecimal applyAmount;

    @Schema(description = "退款成功金额")
    @ExcelProperty("退款成功金额")
    private BigDecimal refundSuccessAmount;

    @Schema(description = "申请状态", example = "2")
    @ExcelProperty("申请状态")
    private Integer applyStatus;

    @Schema(description = "审批状态", example = "1")
    @ExcelProperty("审批状态")
    private Integer approvalStatus;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间，申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间，申请时间")
    private LocalDateTime createTime;

    @ExcelProperty("原因")
    @Schema(description = "原因", example = "太贵了")
    private String reason;

    @ExcelProperty("退款类型")
    @Schema(description = "退款类型", example = "1")
    private Integer type;


    /**
     * 退款状态
     */
    @ExcelProperty("退款状态")
    @Schema(description = "退款状态", example = "1")
    private Integer status;
}
