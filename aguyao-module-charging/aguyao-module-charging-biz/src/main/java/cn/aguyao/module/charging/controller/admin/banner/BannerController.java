package cn.aguyao.module.charging.controller.admin.banner;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.banner.vo.BannerPageReqVO;
import cn.aguyao.module.charging.controller.admin.banner.vo.BannerRespVO;
import cn.aguyao.module.charging.controller.admin.banner.vo.BannerSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.banner.BannerDO;
import cn.aguyao.module.charging.service.banner.BannerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 移动端banner")
@RestController
@RequestMapping("/charging/banner")
@Validated
public class BannerController {

    @Resource
    private BannerService bannerService;

    @PostMapping("/create")
    @Operation(summary = "创建移动端banner")
    @PreAuthorize("@ss.hasPermission('charging:banner:create')")
    public CommonResult<Long> createBanner(@Valid @RequestBody BannerSaveReqVO createReqVO) {
        return success(bannerService.createBanner(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新移动端banner")
    @PreAuthorize("@ss.hasPermission('charging:banner:update')")
    public CommonResult<Boolean> updateBanner(@Valid @RequestBody BannerSaveReqVO updateReqVO) {
        bannerService.updateBanner(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除移动端banner")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:banner:delete')")
    public CommonResult<Boolean> deleteBanner(@RequestParam("id") Long id) {
        bannerService.deleteBanner(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得移动端banner")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:banner:query')")
    public CommonResult<BannerRespVO> getBanner(@RequestParam("id") Long id) {
        BannerDO banner = bannerService.getBanner(id);
        return success(BeanUtils.toBean(banner, BannerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得移动端banner分页")
    @PreAuthorize("@ss.hasPermission('charging:banner:query')")
    public CommonResult<PageResult<BannerRespVO>> getBannerPage(@Valid BannerPageReqVO pageReqVO) {
        PageResult<BannerDO> pageResult = bannerService.getBannerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BannerRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出移动端banner Excel")
    @PreAuthorize("@ss.hasPermission('charging:banner:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBannerExcel(@Valid BannerPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BannerDO> list = bannerService.getBannerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "移动端banner.xls", "数据", BannerRespVO.class,
                        BeanUtils.toBean(list, BannerRespVO.class));
    }

}