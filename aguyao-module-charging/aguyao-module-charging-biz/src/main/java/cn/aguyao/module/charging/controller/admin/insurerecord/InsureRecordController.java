package cn.aguyao.module.charging.controller.admin.insurerecord;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.insurerecord.vo.InsureRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.insurerecord.vo.InsureRecordRespVO;
import cn.aguyao.module.charging.controller.admin.insurerecord.vo.InsureRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.insurerecord.InsureRecordDO;
import cn.aguyao.module.charging.service.insurerecord.InsureRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 投保记录")
@RestController
@RequestMapping("/charging/insure-record")
@Validated
public class InsureRecordController {

    @Resource
    private InsureRecordService insureRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建投保记录")
    @PreAuthorize("@ss.hasPermission('charging:insure-record:create')")
    public CommonResult<Long> createInsureRecord(@Valid @RequestBody InsureRecordSaveReqVO createReqVO) {
        return success(insureRecordService.createInsureRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新投保记录")
    @PreAuthorize("@ss.hasPermission('charging:insure-record:update')")
    public CommonResult<Boolean> updateInsureRecord(@Valid @RequestBody InsureRecordSaveReqVO updateReqVO) {
        insureRecordService.updateInsureRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除投保记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:insure-record:delete')")
    public CommonResult<Boolean> deleteInsureRecord(@RequestParam("id") Long id) {
        insureRecordService.deleteInsureRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得投保记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:insure-record:query')")
    public CommonResult<InsureRecordRespVO> getInsureRecord(@RequestParam("id") Long id) {
        InsureRecordDO insureRecord = insureRecordService.getInsureRecord(id);
        return success(BeanUtils.toBean(insureRecord, InsureRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得投保记录分页")
    @PreAuthorize("@ss.hasPermission('charging:insure-record:query')")
    public CommonResult<PageResult<InsureRecordRespVO>> getInsureRecordPage(@Valid InsureRecordPageReqVO pageReqVO) {
        PageResult<InsureRecordDO> pageResult = insureRecordService.getInsureRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InsureRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出投保记录 Excel")
    @PreAuthorize("@ss.hasPermission('charging:insure-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInsureRecordExcel(@Valid InsureRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InsureRecordDO> list = insureRecordService.getInsureRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "投保记录.xls", "数据", InsureRecordRespVO.class,
                        BeanUtils.toBean(list, InsureRecordRespVO.class));
    }

}