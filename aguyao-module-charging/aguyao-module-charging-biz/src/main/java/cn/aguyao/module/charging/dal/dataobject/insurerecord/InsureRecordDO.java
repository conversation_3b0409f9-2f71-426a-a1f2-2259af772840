package cn.aguyao.module.charging.dal.dataobject.insurerecord;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 投保记录 DO
 *
 * <AUTHOR>
 */
@TableName("charging_insure_record")
@KeySequence("charging_insure_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsureRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 投保人，会员id
     */
    private Long mbeId;
    /**
     * 投保人，微信id
     */
    private Long mpId;
    /**
     * 投保配置的id
     */
    private Long insureConfigId;

    /**
     * 充电记录id
     */
    private Long chargeRecordId;

    /**
     * 充电记录编码
     */
    private String chargeRecordCode;

    /**
     * 投保费用
     */
    private BigDecimal amount;

    @TableField(exist = false)
    private String mbeCode;

    @TableField(exist = false)
    private String mobile;
}
