package cn.aguyao.module.charging.controller.app.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@Schema(description = "临时充电")
@Data
public class AppTempChargeReqVO {

    @NotNull(message = "设备代码不能为空")
    @Schema(description = "设备代码， 主机编号")
    private String device;

    @NotNull(message = "端口代码不能为空")
    @Schema(description = "端口代码， 1为1号端口，2为2号端口 (255为全部端口)")
    private Integer port;

    @NotNull(message = "充电金额")
    @Schema(description = "充电金额，1")
    private BigDecimal amount;

    @Schema(description = "守护充电标识，0：不守护；1：守护；")
    private Integer guardFlag;

}
