package cn.aguyao.module.charging.controller.app.chargerecord;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordDetailRespVO;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordPageReqVO;
import cn.aguyao.module.charging.controller.app.chargerecord.vo.AppChargeRecordRespVO;
import cn.aguyao.module.charging.service.chargerecord.ChargeRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 充电记录，消费记录")
@RestController
@RequestMapping("/charging/charge-record")
@Validated
public class AppChargeRecordController {

    @Resource
    private ChargeRecordService chargeRecordService;

    /**
     * 充电记录分页
     * @param type 消费类型，1：单次消费； 2：包月消费；
     * @param startTime
     * @param endTime
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "充电记录分页")
//    @PreAuthorize("@ss.hasPermission('charging:charge-record:query')")
    public CommonResult<PageResult<AppChargeRecordRespVO>> getChargeRecordPage(
            @RequestParam("type") Integer type,  // (接口文档)消费类型，0：全部数据； 1：单次消费； 2：包月消费；
            @RequestParam(name = "startTime", required = false) String startTime,
            @RequestParam(name = "endTime", required = false) String endTime,
            @RequestParam("pageNo") Integer pageNo,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam("proceedStatus") Integer proceedSt
    ) {
        ChargeRecordPageReqVO pageReqVO = new ChargeRecordPageReqVO();
        if (Objects.nonNull(type) && Objects.equals(type, 2)) { // 包月消费
            Integer[] it = new Integer[]{1};
            pageReqVO.setPaySource(it);
        } else if (Objects.nonNull(type) && Objects.equals(type, 1)) {  // 单次消费
            // 2：赠送余额； 3：充值余额；4：微信支付
            Integer[] it = new Integer[]{2,3,4};
            pageReqVO.setPaySource(it);
        }
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);

        LocalDateTime[] timeArray = DateUtils.dateStr2LocalDateTimeArray(startTime, endTime);
        pageReqVO.setCreateTime(timeArray);

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        pageReqVO.setMpId(mpId);
        Integer[] proceedStatus = new Integer[]{proceedSt};
        pageReqVO.setProceedStatus(proceedStatus);
        PageResult<AppChargeRecordRespVO> pageResult = chargeRecordService.getChargeRecordPage4App(pageReqVO);

        return success(pageResult);
    }


    /**
     * 充电记录详情
     * @param id 充电记录id
     * @return
     */
    @GetMapping("/detail")
    @Operation(summary = "充电记录详情")
    public CommonResult<ChargeRecordDetailRespVO> detail(@RequestParam("id") Long id) {
        ChargeRecordDetailRespVO respVO = chargeRecordService.detailById(id);
        return success(respVO);
    }

}
