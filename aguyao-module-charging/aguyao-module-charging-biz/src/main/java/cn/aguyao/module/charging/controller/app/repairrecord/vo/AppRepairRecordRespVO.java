package cn.aguyao.module.charging.controller.app.repairrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 报修记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppRepairRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "21553")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "保留记录编号")
    @ExcelProperty("保留记录编号")
    private String code;

    @Schema(description = "用户手机号")
    @ExcelProperty("用户手机号")
    private String mobile;

    @Schema(description = "报修小区id", example = "30287")
    @ExcelProperty("报修小区id")
    private Long communityId;

    @Schema(description = "楼栋id", example = "4449")
    @ExcelProperty("楼栋id")
    private Long buildingId;

    @Schema(description = "设备编号")
    @ExcelProperty("设备编号")
    private String deviceCode;

    @Schema(description = "维修状态", example = "2")
    @ExcelProperty("维修状态")
    private Integer repairStatus;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间，报修时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间，报修时间")
    private LocalDateTime createTime;


    /**
     * 报修小区姓名
     */
    @Schema(description = "报修小区姓名", example = "源昌豪庭")
    @ExcelProperty("报修小区姓名")
    private String communityName;
}
