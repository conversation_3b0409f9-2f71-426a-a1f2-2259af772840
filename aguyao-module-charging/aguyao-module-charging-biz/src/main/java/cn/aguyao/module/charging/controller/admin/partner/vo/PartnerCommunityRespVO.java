package cn.aguyao.module.charging.controller.admin.partner.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 商户——小区管理人员（物业） Response VO")
@Data
@ExcelIgnoreUnannotated
public class PartnerCommunityRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16337")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "商户微信对应的id")
    @ExcelProperty("商户微信对应的id")
    private Long mpId;

    @Schema(description = "商户id", example = "22868")
    @ExcelProperty("商户id")
    private Long partnerId;

    @Schema(description = "小区id", example = "22868")
    @ExcelProperty("小区id")
    private Long communityId;
}
