package cn.aguyao.module.charging.controller.app.weixin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "微信 APP - 请求获取微信用户信息明细 VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppUserInfoVO {

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "用户头像图片的 URL")
    private String avatarUrl;

    @Schema(description = "用户性别。 0：未知； 1：男性； 2：女性")
    private Integer gender;

    @Schema(description = "用户所在国家")
    private String country;

    @Schema(description = "用户所在省份")
    private String province;

    @Schema(description = "用户所在城市")
    private String city;

    @Schema(description = "显示 country，province，city 所用的语言。强制返回 “zh_CN”")
    private String language;
}
