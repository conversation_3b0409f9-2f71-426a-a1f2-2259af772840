package cn.aguyao.module.charging.controller.app.platforminfo.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 平台信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PlatformInfoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "776")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "公司名称", example = "芋艿")
    @ExcelProperty("公司名称")
    private String name;

    @Schema(description = "客服热线")
    @ExcelProperty("客服热线")
    private String customerServicePhone;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
