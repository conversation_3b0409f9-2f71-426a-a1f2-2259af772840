package cn.aguyao.module.charging.controller.mgr.mbe.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 会员，即充电的用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MgrMbeDetailRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15702")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "充值余额")
    @ExcelProperty("充值余额")
    private BigDecimal rechargeBalance;

    @Schema(description = "赠送余额")
    @ExcelProperty("赠送余额")
    private BigDecimal giftBalance;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("所属小区名称")
    @Schema(description = "所属小区名称", example = "源昌豪庭")
    private String belongCommunityName;



    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "关注时间, yyyy-MM-dd")
    private String subscribeTime;
}
