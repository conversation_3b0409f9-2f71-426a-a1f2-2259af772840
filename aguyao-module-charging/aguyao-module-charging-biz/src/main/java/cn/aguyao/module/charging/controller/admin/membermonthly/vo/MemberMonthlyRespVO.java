package cn.aguyao.module.charging.controller.admin.membermonthly.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户和小区包月的关系 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberMonthlyRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24945")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "微信用户id", example = "30072")
    @ExcelProperty("微信用户id")
    private Long mpId;

    @Schema(description = "会员id", example = "13017")
    @ExcelProperty("会员id")
    private Long mbeId;

    @Schema(description = "小区id", example = "3950")
    @ExcelProperty("小区id")
    private Long communityId;

    @Schema(description = "失效时间")
    @ExcelProperty("失效时间")
    private LocalDateTime expiration;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
