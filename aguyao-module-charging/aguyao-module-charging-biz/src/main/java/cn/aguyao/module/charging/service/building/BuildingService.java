package cn.aguyao.module.charging.service.building;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.building.vo.BuildingPageReqVO;
import cn.aguyao.module.charging.controller.admin.building.vo.BuildingSaveReqVO;
import cn.aguyao.module.charging.controller.mgr.community.vo.MgrBuildingInfoVO;
import cn.aguyao.module.charging.dal.dataobject.building.BuildingDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 楼栋 Service 接口
 *
 * <AUTHOR>
 */
public interface BuildingService {

    /**
     * 创建楼栋
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBuilding(@Valid BuildingSaveReqVO createReqVO);

    /**
     * 更新楼栋
     *
     * @param updateReqVO 更新信息
     */
    void updateBuilding(@Valid BuildingSaveReqVO updateReqVO);

    /**
     * 删除楼栋
     *
     * @param id 编号
     */
    void deleteBuilding(Long id);

    /**
     * 获得楼栋
     *
     * @param id 编号
     * @return 楼栋
     */
    BuildingDO getBuilding(Long id);

    /**
     * 获得楼栋分页
     *
     * @param pageReqVO 分页查询
     * @return 楼栋分页
     */
    PageResult<BuildingDO> getBuildingPage(BuildingPageReqVO pageReqVO);

    /**
     * 根据小区id获取楼栋列表
     * @param communityId
     * @return
     */
    List<BuildingDO> getBuildingList(Long communityId);

    /**
     * 根据小区id获取楼栋数量统计
     * @param communityId
     * @return
     */
    List<MgrBuildingInfoVO> selectCountList(Long communityId);

    BuildingDO getRateInfoByDevice(String device);
}
