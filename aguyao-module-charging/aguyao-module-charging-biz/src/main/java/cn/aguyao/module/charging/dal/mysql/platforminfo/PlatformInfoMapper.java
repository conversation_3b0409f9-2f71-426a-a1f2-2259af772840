package cn.aguyao.module.charging.dal.mysql.platforminfo;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.platforminfo.vo.PlatformInfoPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.platforminfo.PlatformInfoDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 平台信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlatformInfoMapper extends BaseMapperX<PlatformInfoDO> {

    default PageResult<PlatformInfoDO> selectPage(PlatformInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PlatformInfoDO>()
                .likeIfPresent(PlatformInfoDO::getName, reqVO.getName())
                .likeIfPresent(PlatformInfoDO::getCustomerServicePhone, reqVO.getCustomerServicePhone())
                .eqIfPresent(PlatformInfoDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PlatformInfoDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(PlatformInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PlatformInfoDO::getId));
    }

    default List<PlatformInfoDO> phoneList() {
        return selectList(new LambdaQueryWrapperX<PlatformInfoDO>()
                .select(PlatformInfoDO::getCustomerServicePhone)
                .orderByDesc(PlatformInfoDO::getCreateTime)
        );
    }
}
