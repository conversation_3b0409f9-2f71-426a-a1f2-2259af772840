package cn.aguyao.module.charging.controller.app.order.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 充值相应参数， 余额充值、包月充值
 */
@Schema(description = "充值相应参数， 余额充值、包月充值")
@Data
public class AppRechargeRespVO {

    @Schema(description = "小程序id")
    private String appId;

    @Schema(description = "时间戳")
    private String timeStamp;

    @Schema(description = "随机字符串")
    private String nonceStr;

    @JsonProperty("package")
    @Schema(description = "预支付交易会话ID,prepay_id")
    private String packageValue;

    @Schema(description = "签名方式,RSA")
    private String signType;

    @Schema(description = "签名")
    private String paySign;

}
