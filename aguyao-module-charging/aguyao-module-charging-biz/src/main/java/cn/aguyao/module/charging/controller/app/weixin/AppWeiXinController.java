package cn.aguyao.module.charging.controller.app.weixin;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.module.charging.controller.app.weixin.vo.AppUserInfoReqVO;
import cn.aguyao.module.charging.service.weixin.WeiXinService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "用户端-微信小程序")
@RestController
@RequestMapping("/wx")
@Validated
@Log4j2
public class AppWeiXinController {

    @Resource
    private WeiXinService weiXinService;


//    /**
//     * 微信小程序授权登录， “新方式不再需要提前调用wx.login进行登录。”，  https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/getPhoneNumber.html
//     * @param code
//     * @return
//     */
//    @Deprecated
//    @GetMapping("/miniapp/code")
//    @Operation(summary = "微信小程序登录")
//    @Parameters({
//            @Parameter(name = "code", description = "用户登录的code", required = true)
//    })
//    public CommonResult<AppAuthLoginRespVO> appletCode(@RequestParam("code") String code) {
//        return CommonResult.success(weiXinService.createToken(code));
//    }


    /**
     * 获取微信小程序用户信息
     * @param reqVO
     * @return
     */
    @PostMapping("/miniapp/getuserinfo")
    @Operation(summary = "微信小程序登录")
    @Parameters({
            @Parameter(name = "reqVO", description = "获取用户信息", required = true)
    })
    public CommonResult<String> getUserInfo(@RequestBody AppUserInfoReqVO reqVO) {
        return CommonResult.success(weiXinService.getUserInfo(reqVO));
    }
}
