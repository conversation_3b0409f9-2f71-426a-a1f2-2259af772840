package cn.aguyao.module.charging.dal.mysql.membermonthly;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.membermonthly.vo.MemberMonthlyPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.membermonthly.MemberMonthlyDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户和小区包月的关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberMonthlyMapper extends BaseMapperX<MemberMonthlyDO> {

    default PageResult<MemberMonthlyDO> selectPage(MemberMonthlyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberMonthlyDO>()
                .eqIfPresent(MemberMonthlyDO::getMpId, reqVO.getMpId())
                .eqIfPresent(MemberMonthlyDO::getMbeId, reqVO.getMbeId())
                .eqIfPresent(MemberMonthlyDO::getCommunityId, reqVO.getCommunityId())
                .eqIfPresent(MemberMonthlyDO::getExpiration, reqVO.getExpiration())
                .betweenIfPresent(MemberMonthlyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MemberMonthlyDO::getId));
    }

}