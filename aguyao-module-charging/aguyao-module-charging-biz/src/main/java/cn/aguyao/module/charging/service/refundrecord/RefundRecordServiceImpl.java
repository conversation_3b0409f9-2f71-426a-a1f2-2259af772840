package cn.aguyao.module.charging.service.refundrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.refundrecord.RefundRecordDO;
import cn.aguyao.module.charging.dal.mysql.refundrecord.RefundRecordMapper;
import cn.aguyao.module.charging.enums.PayAppConst;
import cn.aguyao.module.charging.enums.PrefixConstants;
import cn.aguyao.module.charging.enums.RefundEnum;
import cn.aguyao.module.pay.api.app.PayAppApi;
import cn.aguyao.module.pay.api.app.dto.PayAppRespDTO;
import cn.aguyao.module.pay.api.refund.PayRefundApi;
import cn.aguyao.module.pay.api.refund.dto.PayRefundCreateReqDTO;
import cn.aguyao.module.system.api.serial.SerialApi;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.REFUND_RECORD_NOT_EXISTS;

/**
 * 退款记录 Service 实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Validated
public class RefundRecordServiceImpl implements RefundRecordService {

    @Resource
    private RefundRecordMapper refundRecordMapper;

    @Resource
    private SerialApi serialApi;

    @Resource
    private PayAppApi payAppApi;

    @Resource
    private PayRefundApi payRefundApi;

    @Override
    public Long createRefundRecord(RefundRecordSaveReqVO createReqVO) {

        // 插入
        RefundRecordDO refundRecord = BeanUtils.toBean(createReqVO, RefundRecordDO.class);
        if (StrUtil.isBlank(createReqVO.getCode())) {
            String code = serialApi.getCode(PrefixConstants.PREFIX_TK);
            refundRecord.setCode(code);
        }
        refundRecordMapper.insert(refundRecord);
        // 返回
        return refundRecord.getId();
    }

    @Override
    public void updateRefundRecord(RefundRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateRefundRecordExists(updateReqVO.getId());
        // 更新
        RefundRecordDO updateObj = BeanUtils.toBean(updateReqVO, RefundRecordDO.class);
        refundRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteRefundRecord(Long id) {
        // 校验存在
        validateRefundRecordExists(id);
        // 删除
        refundRecordMapper.deleteById(id);
    }

    private void validateRefundRecordExists(Long id) {
        if (refundRecordMapper.selectById(id) == null) {
            throw exception(REFUND_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public RefundRecordDO getRefundRecord(Long id) {
        return refundRecordMapper.selectById(id);
    }

    @Override
    public PageResult<RefundRecordDO> getRefundRecordPage(RefundRecordPageReqVO pageReqVO) {
        return refundRecordMapper.selectPage(pageReqVO);
    }

    /**
     * todo
     * @param id
     */
    @Override
    public void handleRefund(Long id) {

        if (Objects.isNull(id)) {
            throw exception(REFUND_RECORD_NOT_EXISTS);
        }

        RefundRecordDO recordDO = refundRecordMapper.selectById(id);
        if (recordDO == null) {
            throw exception(REFUND_RECORD_NOT_EXISTS);
        }

        // 构造退款记录
        log.info("------------------手动发起退款------------------退款单编码： {}", recordDO.getCode());
        PayAppRespDTO payApp = payAppApi.getByName(PayAppConst.XMYSJ);
        PayRefundCreateReqDTO dto = new PayRefundCreateReqDTO();
        dto.setAppId(payApp.getId());
        dto.setReason("手动发起退款");
        dto.setUserIp(getClientIP());
        dto.setMerchantOrderId(recordDO.getOrderNo());
        dto.setPrice(recordDO.getApplyAmount().multiply(new BigDecimal(100)).intValue());

        // 最好方式，查询退款单编码，找到最大的自编号然后加1，作为退款单号 todo todo
        String tkCode = recordDO.getCode() + "_01";
        dto.setMerchantRefundId(tkCode);

        log.info("开始执行手动退款业务，参数：{}", JSON.toJSONString(dto));
        Long rid = payRefundApi.createRefund(dto);
        log.info("手动退款成功，退款单号：{}, 退款业务Id：{}", tkCode, rid);
    }

    @Override
    public RefundRecordDO getRefundRecordByOrderNo(String orderNo) {
        return refundRecordMapper.selectByOrderNo(orderNo);
    }

    @Override
    public void notifyHandle(String merchantOrderId, Integer refundPrice, RefundEnum.RefundStatusEnum refundStatusEnum) {
        RefundRecordDO entity = getRefundRecordByOrderNo(merchantOrderId);
        if (Objects.nonNull(entity)) {
            entity.setUpdateTime(LocalDateTime.now());
            entity.setStatus(refundStatusEnum.getValue());
            entity.setRefundSuccessAmount(new BigDecimal(refundPrice).divide(new BigDecimal(100)));
            refundRecordMapper.updateById(entity);
        }
    }

}
