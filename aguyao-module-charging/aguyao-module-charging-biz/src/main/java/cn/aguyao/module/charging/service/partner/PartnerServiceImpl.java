package cn.aguyao.module.charging.service.partner;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.incomerecord.vo.IncomeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.partner.vo.PartnerPageReqVO;
import cn.aguyao.module.charging.controller.admin.partner.vo.PartnerSaveReqVO;
import cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo.WithdrawalRecordSaveReqVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCollectRespVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCommunityRespVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrPartnerBalanceRespVO;
import cn.aguyao.module.charging.dal.dataobject.incomerecord.IncomeRecordDO;
import cn.aguyao.module.charging.dal.dataobject.partner.PartnerDO;
import cn.aguyao.module.charging.dal.dataobject.partnerbank.PartnerBankDO;
import cn.aguyao.module.charging.dal.dataobject.partnercommunity.PartnerCommunityDO;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.dal.mysql.partner.PartnerMapper;
import cn.aguyao.module.charging.enums.ApprovalStatusEnum;
import cn.aguyao.module.charging.enums.PrefixConstants;
import cn.aguyao.module.charging.service.incomerecord.IncomeRecordService;
import cn.aguyao.module.charging.service.mbe.MbeService;
import cn.aguyao.module.charging.service.partnerbank.PartnerBankService;
import cn.aguyao.module.charging.service.partnercommunity.PartnerCommunityService;
import cn.aguyao.module.charging.service.withdrawalrecord.WithdrawalRecordService;
import cn.aguyao.module.system.api.serial.SerialApi;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.*;

/**
 * 合作伙伴——小区管理人员（物业） Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PartnerServiceImpl implements PartnerService {

    @Resource
    private PartnerMapper partnerMapper;


    @Resource
    private SerialApi serialApi;

    @Resource
    private IncomeRecordService incomeRecordService;

    @Lazy
    @Resource
    private PartnerBankService partnerBankService;

    @Resource
    private PartnerCommunityService partnerCommunityService;

    @Resource
    private WithdrawalRecordService withdrawalRecordService;

    @Lazy
    @Resource
    private MbeService mbeService;



    @Override
    public Long createPartner(PartnerSaveReqVO createReqVO) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_ZG);


        MbeDO member = mbeService.getMember(createReqVO.getMbeId());
        Long mpId = null;
        if (Objects.nonNull(member)) {
            mpId = member.getMpUserId();
        }
        // 插入
        PartnerDO partner = BeanUtils.toBean(createReqVO, PartnerDO.class);
        partner.setMpId(mpId);
        partner.setCode(code);
        partnerMapper.insert(partner);

        if (CollUtil.isNotEmpty(createReqVO.getCommunityIds())) {
            partnerCommunityService.createOrUpdatePartnerCommunity(partner.getMpId(), partner.getId(), createReqVO.getCommunityIds());
        }

        // 返回
        return partner.getId();
    }

    @Override
    public void updatePartner(PartnerSaveReqVO updateReqVO) {
        // 校验存在
        validatePartnerExists(updateReqVO.getId());
        // 更新
        PartnerDO partner = BeanUtils.toBean(updateReqVO, PartnerDO.class);

//        PartnerDO entity = this.getPartner(updateReqVO.getId());
        MbeDO member = mbeService.getMember(updateReqVO.getMbeId());
        Long mpId = null;
        if (Objects.nonNull(member)) {
            mpId = member.getMpUserId();
        }

        partner.setMpId(mpId);
        partnerCommunityService.createOrUpdatePartnerCommunity(mpId, partner.getId(), updateReqVO.getCommunityIds());

        partnerMapper.updateById(partner);
    }

    @Override
    public void updatePartner(PartnerDO partnerDO) {
        partnerDO.setUpdateTime(LocalDateTime.now());
        partnerMapper.updateById(partnerDO);
    }

    @Override
    public void deletePartner(Long id) {
        // 校验存在
//        validatePartnerExists(id);
        PartnerDO partnerDO = partnerMapper.selectById(id);
        if (partnerDO == null) {
            throw exception(PARTNER_NOT_EXISTS);
        }
        // 删除
        partnerMapper.deleteById(id);

        partnerCommunityService.deleteByMpId(partnerDO.getMpId());
    }

    private void validatePartnerExists(Long id) {
        if (partnerMapper.selectById(id) == null) {
            throw exception(PARTNER_NOT_EXISTS);
        }
    }

    @Override
    public PartnerDO getPartner(Long id) {
        PartnerDO partner = partnerMapper.selectById(id);
        if (Objects.nonNull(partner)) {
            List<PartnerCommunityDO> list = partnerCommunityService.getListByMpId(partner.getMpId());
            if (CollUtil.isNotEmpty(list)) {
                partner.setCommunityIds(list.stream().map(PartnerCommunityDO::getCommunityId).collect(Collectors.toList()));
            }

            MbeDO member = mbeService.selectMemberByMpUserId(partner.getMpId());
            if (Objects.nonNull(member)) {
                partner.setMbeId(member.getId());
                partner.setMbeCode(member.getCode());
            }
        }
        return partner;
    }

    @Override
    public PartnerDO getPartnerByMpId(Long mpId) {
        return partnerMapper.selectOne(PartnerDO::getMpId, mpId);
    }

    @Override
    public Long getPartnerIdByMpId(Long mpId) {
        return Optional.of(partnerMapper.selectOne(PartnerDO::getMpId, mpId)).orElseGet(PartnerDO::new).getId();
    }

    @Override
    public PageResult<PartnerDO> getPartnerPage(PartnerPageReqVO pageReqVO) {
        return partnerMapper.selectPage(pageReqVO);
    }


    @Override
    public PartnerDO getUserByMobile(String mobile) {
        return partnerMapper.getUserByMobile(mobile);
    }

    @Override
    public PartnerDO createUserIfAbsent(String purePhoneNumber, String clientIP, Integer terminal) {
        return null;
    }

    @Override
    public int updateUserLogin(Long userId, String clientIP) {
        return partnerMapper.updateUserLogin(userId,clientIP);
    }

    @Override
    public MgrPartnerBalanceRespVO balance() {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        PartnerDO partner = this.getPartnerByMpId(mpId);

        if (Objects.isNull(partner)) {
            return null;
        }

        MgrPartnerBalanceRespVO resp = new MgrPartnerBalanceRespVO();
        resp.setWithdrawn(partner.getWithdrawn());
        resp.setCanWithdraw(partner.getCanWithdraw());
        PartnerBankDO partnerBankDO = partnerBankService.getPartnerBankByPartnerId(partner.getId());
        if (Objects.nonNull(partnerBankDO)) {
//            resp.setCardNo(partnerBankDO.getCardNo());
            resp.setBankDeposit(partnerBankDO.getBankDeposit());

            if (StrUtil.isNotBlank(partnerBankDO.getCardNo())) {
                String no = partnerBankDO.getCardNo();
                String start = no.substring(0, 4);
                String end = no.substring(no.length() - 3);
                resp.setCardNo(start + "****" + end);
            }
        }

        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean withdraw(BigDecimal amount) {

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        // 1、查询用户余额
        PartnerDO partner = this.getPartnerByMpId(mpId);
        if (Objects.isNull(partner.getCanWithdraw())
                || partner.getCanWithdraw().compareTo(amount) < 0) {
            throw exception(PARTNER_BALANCE_INSUFFICIENT);
        }

        // 2、是否绑定银行卡
        PartnerBankDO partnerBank = partnerBankService.getPartnerBankByPartnerId(partner.getId());
        if (Objects.isNull(partnerBank)) {
            throw exception(PARTNER_BANK_NOT_BINDING);
        }

        // 2、冻结余额
        partner.setCanWithdraw(partner.getCanWithdraw().subtract(amount));
        if (partner.getBlockedBalances() == null) {
            partner.setBlockedBalances(BigDecimal.ZERO);
        }
        partner.setBlockedBalances(partner.getBlockedBalances().add(amount));
        partnerMapper.updateById(partner);

        // 3、创建提现记录
        WithdrawalRecordSaveReqVO req = new WithdrawalRecordSaveReqVO();
        req.setPartnerId(partner.getId());
        req.setApplyAmount(amount);
        req.setApplyTime(LocalDateTime.now());
        req.setBankName(partnerBank.getBankName());
        req.setBankCardNum(partnerBank.getCardNo());
        req.setStatus(ApprovalStatusEnum.WithdrawStatusEnum.APPLYING_0.getValue());

        withdrawalRecordService.createWithdrawalRecord(req);

        return null;
    }

    @Override
    public MgrIncomeCollectRespVO incomeCollect(Long mpId, String communityName) {
        PartnerDO partner = this.getPartnerByMpId(mpId);
        MgrIncomeCollectRespVO respVO = incomeRecordService.incomeCollect(partner.getId(), communityName);
//        if (Objects.nonNull(respVO)) {
//            List<MgrIncomeCommunityRespVO> list = incomeRecordService.incomeCommunity(mpId);
//            respVO.setList(list);
//        }
        return respVO;
    }

    @Override
    public PageResult<IncomeRecordDO> incomeDetail(IncomeRecordPageReqVO pageReqVO) {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        Long partnerId = this.getPartnerIdByMpId(mpId);
        pageReqVO.setPartnerId(partnerId);
        PageResult<IncomeRecordDO> pageResult = incomeRecordService.getIncomeRecordPage4Mgr(pageReqVO);
        return pageResult;
    }

    @Override
    public PageResult<MgrIncomeCommunityRespVO> incomeCollectPage(IncomeRecordPageReqVO pageReqVO) {
        Long partnerId=  this.getPartnerIdByMpId(pageReqVO.getMpId());
        pageReqVO.setPartnerId(partnerId);
        PageResult<MgrIncomeCommunityRespVO> pageResult = incomeRecordService.incomeCommunity(pageReqVO);
        return pageResult;
    }

}
