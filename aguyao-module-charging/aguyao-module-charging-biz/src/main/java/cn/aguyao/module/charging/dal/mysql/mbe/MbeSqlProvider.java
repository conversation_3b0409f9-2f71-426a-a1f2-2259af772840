package cn.aguyao.module.charging.dal.mysql.mbe;

import cn.aguyao.framework.common.util.collection.ArrayUtils;
import cn.aguyao.module.charging.controller.admin.mbe.vo.MbePageReqVO;
import cn.aguyao.module.charging.controller.mgr.mbe.vo.MgrMbePageReqVO;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

import java.time.LocalDateTime;
import java.util.Objects;

public class MbeSqlProvider {

    public String selectPage(Page<?> page, @Param("reqVO") final MbePageReqVO reqVO) {
        return new SQL() {{
            SELECT("t.*, " +
                " (select name from charging_community t1 where t1.id = t.belong_community_id) as belongCommunityName, " +
                " (select name from charging_community t1 where t1.id = t.monthly_pass_community_id) as monthlyPassCommunityName ");
            FROM("charging_member t  ");
            if (StrUtil.isNotBlank(reqVO.getCode())) {
                AND();
                WHERE(" t.code like concat('%', #{reqVO.code}, '%')");
            }

            if (StrUtil.isNotBlank(reqVO.getMobile())) {
                AND();
                WHERE(" t.mobile like concat('%', #{reqVO.mobile}, '%')");
            }

            if (StrUtil.isNotBlank(reqVO.getBelongCommunityName())) {
                LEFT_OUTER_JOIN(" charging_community t3 on t3.id = t.belong_community_id ");
                AND();
                WHERE(" t3.name like concat('%', #{reqVO.belongCommunityName}, '%')");
            }

            if (reqVO.getMonthlyPassDuration() != null) {
                Object val1 = ArrayUtils.get(reqVO.getMonthlyPassDuration(), 0);
                Object val2 = ArrayUtils.get(reqVO.getMonthlyPassDuration(), 1);
                if (val1 != null) {
                    AND();
                    WHERE(" t.monthly_pass_duration >= #{reqVO.monthlyPassDuration[0]}");
                }
                if (val2 != null) {
                    AND();
                    WHERE(" t.monthly_pass_duration <= #{reqVO.monthlyPassDuration[1]}");
                }
            }

            AND();
            WHERE(" t.deleted = 0");

        }}.toString();
    }

    /**
     * 管理端：按小区用户统计信息
     * @return
     */
    public String statisticsInfo(Page<?> page, @Param("reqVO") final MgrMbePageReqVO reqVO) {
        return new SQL() {{
            SELECT("    p.id partnerId," +
                    "    p.name partnerName," +
                    "    c.id communityId," +
                    "    c.name communityName," +
                    "    COUNT(m.id) AS userCount," +
                    "    SUM(CASE WHEN DATE(m.create_time) = CURDATE() THEN 1 ELSE 0 END) AS newUserCount ");
            FROM(" charging_community c ");
            LEFT_OUTER_JOIN(" charging_partner_community pc on c.id = pc.community_id and pc.deleted = 0 ");
            LEFT_OUTER_JOIN(" charging_partner p on pc.partner_id = p.id and p.deleted = 0 ");
            LEFT_OUTER_JOIN(" charging_member m ON c.id= m.belong_community_id and m.deleted = 0 ");
            WHERE(" c.deleted = 0 and p.mp_id = " + reqVO.getMpId());
            if (StrUtil.isNotBlank(reqVO.getBelongCommunityName())) {
                AND();
                WHERE(" c.name like concat('%', #{reqVO.belongCommunityName}, '%')");
            }
            GROUP_BY(" p.id, c.id");
        }}.toString();
    }

    /**
     * 管理端：用户统计信息
     * @return
     */
    public String statisticsInfoAll(@Param("id") final Long id, @Param("communityName") final String communityName) {
        return new SQL() {{
            SELECT(" COUNT(m.id) AS totalUserCount," +
                    "    SUM(CASE WHEN DATE(m.create_time) = CURDATE() THEN 1 ELSE 0 END) AS todayUserCount ");
//            FROM(" charging_partner p ");
//            LEFT_OUTER_JOIN(" charging_partner_community pc on p.id = pc.partner_id ");
//            LEFT_OUTER_JOIN(" charging_community c on pc.community_id= c.id ");
//            LEFT_OUTER_JOIN(" charging_member m ON c.id= m.belong_community_id ");
//            WHERE(" p.id = " + id);


            FROM(" charging_community c ");
            LEFT_OUTER_JOIN(" charging_partner_community pc on c.id = pc.community_id and pc.deleted = 0 ");
            LEFT_OUTER_JOIN(" charging_partner p on pc.partner_id = p.id and p.deleted = 0 ");
            LEFT_OUTER_JOIN(" charging_member m ON c.id= m.belong_community_id and m.deleted = 0 ");
            WHERE(" c.deleted = 0 and p.id = " + id);


            if (StrUtil.isNotBlank(communityName)) {
                AND();
                WHERE(" c.name like concat('%', #{communityName}, '%')");
            }
            GROUP_BY(" p.id");
        }}.toString();
    }

    /**
     * 管理端：用户统计详情信息
     * @return
     */
//    public String statisticsDetail(@Param("partnerId") final Long partnerId,
//                                   @Param("communityId") final Long communityId,
//                                   @Param("startTime") final String startTime,
//                                   @Param("endTime") final String endTime) {
    public String statisticsDetail(Page<?> page, @Param("reqVO") final MgrMbePageReqVO reqVO) {

        return new SQL() {{
            SELECT(" cm.id, cm.code,  cm.mobile, " +
                    "cm.recharge_balance balance, " +
                    "cm.gift_balance giftBalance, " +
                    "cm.create_time createTime, " +
                    "mu.nickname nickname, " +
                    "mu.subscribe_time subscribeTime ");
            FROM(" charging_member cm ");
            LEFT_OUTER_JOIN(" mp_user mu on (cm.id = mu.user_id and mu.user_type = 1) ");
            WHERE(" cm.belong_community_id = " + reqVO.getBelongCommunityId());

            if (Objects.nonNull(reqVO.getCreateTime())) {

                LocalDateTime startTime = ArrayUtils.get(reqVO.getCreateTime(), 0);
                LocalDateTime endTime = ArrayUtils.get(reqVO.getCreateTime(), 1);

                if (Objects.nonNull(startTime)) {
                    AND();
                    WHERE(" cm.create_time >= '" + startTime + "'" );
                }
                if (Objects.nonNull(endTime)) {
                    AND();
                    WHERE(" cm.create_time <= '" + endTime + "'" );
                }
            }

            if (StrUtil.isNotBlank(reqVO.getMobile())) {
                AND();
                WHERE(" cm.mobile like concat('%', #{reqVO.mobile}, '%')");
            }

            ORDER_BY(" cm.belong_community_id, cm.id");
        }}.toString();
    }

}
