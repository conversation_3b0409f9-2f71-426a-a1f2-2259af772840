package cn.aguyao.module.charging.dal.mysql.rechargediscountconfig;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.rechargediscountconfig.vo.RechargeDiscountConfigPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.rechargediscountconfig.RechargeDiscountConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 充值优惠配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RechargeDiscountConfigMapper extends BaseMapperX<RechargeDiscountConfigDO> {

    default PageResult<RechargeDiscountConfigDO> selectPage(RechargeDiscountConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RechargeDiscountConfigDO>()
                .likeIfPresent(RechargeDiscountConfigDO::getCode, reqVO.getCode())
                .eqIfPresent(RechargeDiscountConfigDO::getAmount, reqVO.getAmount())
                .eqIfPresent(RechargeDiscountConfigDO::getGiftAmount, reqVO.getGiftAmount())
                .eqIfPresent(RechargeDiscountConfigDO::getRemark, reqVO.getRemark())
                .eqIfPresent(RechargeDiscountConfigDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(RechargeDiscountConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RechargeDiscountConfigDO::getSort));
    }

    default List<RechargeDiscountConfigDO> list() {
        return selectList(new LambdaQueryWrapperX<RechargeDiscountConfigDO>()
                .orderByAsc(RechargeDiscountConfigDO::getSort));
    }
}
