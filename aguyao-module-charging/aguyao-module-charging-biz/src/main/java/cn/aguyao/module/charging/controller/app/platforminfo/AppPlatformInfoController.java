package cn.aguyao.module.charging.controller.app.platforminfo;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.module.charging.service.platforminfo.PlatformInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "用户端小程序 - 平台信息")
@RestController
@RequestMapping("/charging/platform-info")
@Validated
public class AppPlatformInfoController {

    @Resource
    private PlatformInfoService platformInfoService;

    /**
     * 电话
     * @return
     */
    @GetMapping("/phone/list")
    @Operation(summary = "电话")
//    @PreAuthorize("@ss.hasPermission('charging:platform-info:query')")
    public CommonResult<Map<String, List<String>>> phoneList() {
        return success(platformInfoService.phoneList());
    }

}
