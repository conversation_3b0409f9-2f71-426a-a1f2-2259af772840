package cn.aguyao.module.charging.dal.dataobject.banner;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * Banner DO
 *
 * <AUTHOR>
 */
@TableName("charging_banner")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BannerDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 标签
     */
    private String tag;
    /**
     * 跳转链接
     */
    private String jumpLink;
    /**
     * 图片链接
     */
    private String imageUrl;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;
}
