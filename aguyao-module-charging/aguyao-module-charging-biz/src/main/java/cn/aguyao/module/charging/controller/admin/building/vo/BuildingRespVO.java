package cn.aguyao.module.charging.controller.admin.building.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 楼栋 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BuildingRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24573")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "楼栋名称", example = "王五")
    @ExcelProperty("楼栋名称")
    private String name;

    @Schema(description = "小区名称", example = "")
    @ExcelProperty("小区名称")
    private Long communityId;

    @Schema(description = "设备数")
    @ExcelProperty("设备数")
    private Integer deviceNum;

    @Schema(description = "充电中的设备数")
    @ExcelProperty("充电中的设备数")
    private Integer chargingDeviceNum;

    @Schema(description = "待机设备数")
    @ExcelProperty("待机设备数")
    private Integer standbyDeviceNum;

    @Schema(description = "断网设备数")
    @ExcelProperty("断网设备数")
    private Integer networkDisconnDeviceNum;

    @Schema(description = "费率")
    @ExcelProperty("费率")
    private String rate;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    @Schema(description = "方案id")
    @ExcelProperty("方案id")
    private String schemeId;

    @Schema(description = "方案名称")
    @ExcelProperty("方案名称")
    private String schemeName;

    ///////////////////////////////////
    @Schema(description = "小区名称", example = "")
    private String communityName;
}
