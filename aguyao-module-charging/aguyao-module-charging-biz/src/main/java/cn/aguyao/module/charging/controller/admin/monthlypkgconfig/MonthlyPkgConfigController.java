package cn.aguyao.module.charging.controller.admin.monthlypkgconfig;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.monthlypkgconfig.vo.MonthlyPkgConfigPageReqVO;
import cn.aguyao.module.charging.controller.admin.monthlypkgconfig.vo.MonthlyPkgConfigRespVO;
import cn.aguyao.module.charging.controller.admin.monthlypkgconfig.vo.MonthlyPkgConfigSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgconfig.MonthlyPkgConfigDO;
import cn.aguyao.module.charging.service.monthlypkgconfig.MonthlyPkgConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 包月配置")
@RestController
@RequestMapping("/charging/monthly-pkg-config")
@Validated
public class MonthlyPkgConfigController {

    @Resource
    private MonthlyPkgConfigService monthlyPkgConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建包月配置")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-config:create')")
    public CommonResult<Long> createMonthlyPkgConfig(@Valid @RequestBody MonthlyPkgConfigSaveReqVO createReqVO) {
        return success(monthlyPkgConfigService.createMonthlyPkgConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新包月配置")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-config:update')")
    public CommonResult<Boolean> updateMonthlyPkgConfig(@Valid @RequestBody MonthlyPkgConfigSaveReqVO updateReqVO) {
        monthlyPkgConfigService.updateMonthlyPkgConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除包月配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-config:delete')")
    public CommonResult<Boolean> deleteMonthlyPkgConfig(@RequestParam("id") Long id) {
        monthlyPkgConfigService.deleteMonthlyPkgConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得包月配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-config:query')")
    public CommonResult<MonthlyPkgConfigRespVO> getMonthlyPkgConfig(@RequestParam("id") Long id) {
        MonthlyPkgConfigDO monthlyPkgConfig = monthlyPkgConfigService.getMonthlyPkgConfig(id);
        return success(BeanUtils.toBean(monthlyPkgConfig, MonthlyPkgConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得包月配置分页")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-config:query')")
    public CommonResult<PageResult<MonthlyPkgConfigRespVO>> getMonthlyPkgConfigPage(@Valid MonthlyPkgConfigPageReqVO pageReqVO) {
        PageResult<MonthlyPkgConfigDO> pageResult = monthlyPkgConfigService.getMonthlyPkgConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MonthlyPkgConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出包月配置 Excel")
    @PreAuthorize("@ss.hasPermission('charging:monthly-pkg-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMonthlyPkgConfigExcel(@Valid MonthlyPkgConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MonthlyPkgConfigDO> list = monthlyPkgConfigService.getMonthlyPkgConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "包月配置.xls", "数据", MonthlyPkgConfigRespVO.class,
                        BeanUtils.toBean(list, MonthlyPkgConfigRespVO.class));
    }

}