package cn.aguyao.module.charging.controller.admin.banner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 移动端banner新增/修改 Request VO")
@Data
public class BannerSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13933")
    private Long id;

    @Schema(description = "标签")
    private String tag;

    @Schema(description = "跳转链接")
    private String jumpLink;

    @Schema(description = "图片链接", example = "https://www.iocoder.cn")
    private String imageUrl;

    @Schema(description = "排序")
    private Integer sort;

}