package cn.aguyao.module.charging.dal.mysql.banner;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.banner.vo.BannerPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.banner.BannerDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Banner Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BannerMapper extends BaseMapperX<BannerDO> {


    default List<BannerDO> getBannerList() {
        return selectList(new LambdaQueryWrapperX<BannerDO>()
                .orderByAsc(BannerDO::getSort));
    }

    default PageResult<BannerDO> selectPage(BannerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BannerDO>()
                .eqIfPresent(BannerDO::getTag, reqVO.getTag())
                .eqIfPresent(BannerDO::getJumpLink, reqVO.getJumpLink())
                .eqIfPresent(BannerDO::getImageUrl, reqVO.getImageUrl())
                .eqIfPresent(BannerDO::getSort, reqVO.getSort())
                .betweenIfPresent(BannerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BannerDO::getId));
    }
}
