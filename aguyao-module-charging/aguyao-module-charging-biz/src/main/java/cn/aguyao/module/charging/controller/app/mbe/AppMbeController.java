package cn.aguyao.module.charging.controller.app.mbe;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.security.core.LoginUser;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.annotation.TokenRequired;
import cn.aguyao.module.charging.controller.app.mbe.vo.AppCommunityRespVO;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.service.community.CommunityService;
import cn.aguyao.module.charging.service.mbe.MbeService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.aguyao.framework.common.pojo.CommonResult.success;
import static cn.aguyao.module.charging.enums.AppErrorCodeConstants.USER_NOT_LOGIN;

@Tag(name = "小程序端 - 会员，即充电的用户")
@RestController
@RequestMapping("/charging/mbe")
@Validated
public class AppMbeController {

    @Resource
    private MbeService memberService;

    @Resource
    private CommunityService communityService;


    /**
     * 1.1、用户&账户信息
     * @return
     */
//    @TokenRequired
    @GetMapping("/get")
    @Operation(summary = "获得会员，即充电的用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<Map<String,Object>> getMember() {
        Long id = SecurityFrameworkUtils.getLoginUserIdCheck();
        if (Objects.isNull(id)) {
            return CommonResult.error(USER_NOT_LOGIN);
        }
        MbeDO mbeDO = memberService.selectMemberByMpUserId(id);
        Map<String, Object> mbeInfo = memberService.getMemberInfo(mbeDO.getId());
        return success(mbeInfo);
    }


    /**
     * 1.10、获取账户余额
     * @return
     */
    @GetMapping("/refund/balance")
    @Operation(summary = "获取账户余额")
    public CommonResult<BigDecimal> refundBalance() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (Objects.isNull(loginUser)) {
            return CommonResult.error(USER_NOT_LOGIN);
        }
        MbeDO member = memberService.selectMemberByMpUserId(loginUser.getId());
        BigDecimal balance = BigDecimal.ZERO;
        if (Objects.nonNull(member)) {
            balance = member.getRechargeBalance();
        }
        return success(balance);
    }




    /**
     * 1.11、余额 系统核酸
     * @return
     */
    @GetMapping("/refund/costing")
    @Operation(summary = "系统核酸")
//    @PreAuthorize("@ss.hasPermission('charging:member:query')")
    public CommonResult<BigDecimal> refundCosting() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUserCheck();

        MbeDO member = memberService.getMember(loginUser.getId());
        BigDecimal balance = BigDecimal.ZERO;
        if (Objects.nonNull(member)) {
            balance = member.getRechargeBalance();
        }
        return success(balance);
    }

    /**
     * 1.12、确认退款
     * @return
     */
    @PutMapping("/refund/confirm")
    @Operation(summary = "确认退款")
    public CommonResult<String> refundConfirm(@RequestBody String jsonReason) {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (Objects.isNull(loginUser)) {
            return CommonResult.error(USER_NOT_LOGIN);
        }

        String reason = null;
        if (StrUtil.isNotBlank(jsonReason)) {
            JSONObject json = new JSONObject(jsonReason);
            reason = json.getStr("reason");
        }

        return memberService.refundConfirm(loginUser.getId(), reason);
    }


    /**
     * 1.13 小区列表
     * @return
     */
    @TokenRequired
    @GetMapping("/communityList")
    @Operation(summary = "小区列表")
    @Parameter(name = "communityName", description = "小区名称", required = true, example = "源昌豪庭")
    public CommonResult<List<AppCommunityRespVO>> communityList(
            @RequestParam(value = "communityName", required = false) String communityName) {
        List<AppCommunityRespVO> list =  communityService.getCommunityList4App(communityName);
        return success(list);
    }


    /**
     * 1.14、切换小区
     * @return
     */
    @GetMapping("/changeCommunity")
    @Operation(summary = "切换小区")
    public CommonResult<Void> changeCommunity(@RequestParam("communityId") Long communityId) {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        MbeDO member = memberService.selectMemberByMpUserId(mpId);
        memberService.updateCurrentCommunityId(member.getId(), communityId);
        return success();
    }
}
