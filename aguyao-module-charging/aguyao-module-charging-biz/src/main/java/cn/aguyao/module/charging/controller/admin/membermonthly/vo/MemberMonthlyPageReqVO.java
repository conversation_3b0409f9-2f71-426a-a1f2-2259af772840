package cn.aguyao.module.charging.controller.admin.membermonthly.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户和小区包月的关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberMonthlyPageReqVO extends PageParam {

    @Schema(description = "微信用户id", example = "30072")
    private Long mpId;

    @Schema(description = "会员id", example = "13017")
    private Long mbeId;

    @Schema(description = "小区id", example = "3950")
    private Long communityId;

    @Schema(description = "失效时间")
    private LocalDateTime expiration;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
