package cn.aguyao.module.charging.controller.admin.chargerecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 充电记录，消费记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ChargeRecordDetailRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "25604")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "用户id")
    @ExcelProperty("用户id")
    private Long mbeId;

    @Schema(description = "用户编号")
    @ExcelProperty("用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    @ExcelProperty("用户手机号")
    private String mobile;

    @Schema(description = "充电时间")
    @ExcelProperty("充电时间")
    private LocalDateTime chargeTime;

    @Schema(description = "充电小区", example = "3424")
    @ExcelProperty("充电小区")
    private String chargeCommunityName;

    @Schema(description = "设备编码", example = "26557")
    @ExcelProperty("设备编码")
    private String device;

    @Schema(description = "消费金额")
    @ExcelProperty("消费金额")
    private BigDecimal amount;

    @Schema(description = "支付来源，消费来源")
    @ExcelProperty("支付来源，消费来源")
    private Integer paySource;

    @Schema(description = "进行状态", example = "1")
    @ExcelProperty("进行状态")
    private Integer proceedStatus;

    @Schema(description = "瓦数，消耗的瓦数")
    @ExcelProperty("瓦数，消耗的瓦数")
    private BigDecimal wattage;

    @Schema(description = "充电时长，单位：秒", example = "1")
    @ExcelProperty("充电时长，单位：秒")
    private Integer time;

    @Schema(description = "实际充电时间", example = "1")
    @ExcelProperty("实际充电时间")
    private String actualChargeTime;

    @Schema(description = "剩余金额，余额+赠送")
    @ExcelProperty("剩余金额，余额+赠送")
    private BigDecimal balance;

    @Schema(description = "已经充电了多长时间，单位：秒")
    @ExcelProperty("已经充电了多长时间，单位：秒")
    private Long seconds;
}
