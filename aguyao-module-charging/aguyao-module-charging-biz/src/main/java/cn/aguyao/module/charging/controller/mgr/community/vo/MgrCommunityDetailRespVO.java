package cn.aguyao.module.charging.controller.mgr.community.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理端 - 小区 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MgrCommunityDetailRespVO {



    @Schema(description = "小区名称", example = "赵六")
    @ExcelProperty("小区名称")
    private String name;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "地址")
    @ExcelProperty("地址")
    private String address;

    @Schema(description = "用户数，会员数")
    @ExcelProperty("用户数，会员数")
    private Long mbeNum;

    @Schema(description = "使用人数")
    @ExcelProperty("使用人数")
    private Long useNum;

    @Schema(description = "分成比例")
    @ExcelProperty("分成比例")
    private BigDecimal proportion;


    @Schema(description = "楼栋信息列表")
    @ExcelProperty("楼栋信息列表")
    private List<MgrBuildingInfoVO> buildingList;


}
