package cn.aguyao.module.charging.dal.dataobject.user;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 微信小程序粉丝 DO
 *
 * <AUTHOR>
 */
@TableName("mp_user")
@KeySequence("mp_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MpUserDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 公众号 appId
     */
    private String appId;
    /**
     * 用户唯一标识
     */
    private String openid;
    /**
     * 微信生态唯一标识
     */
    private String unionId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像地址
     */
    private String headImageUrl;
    /**
     * 语言
     */
    private String language;
    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 备注
     */
    private String remark;
    /**
     * 关注状态, 1. 开启 - 已关注; 2. 禁用 - 取消关注
     */
    private Integer subscribeStatus;
    /**
     * 关注时间
     */
    private LocalDateTime subscribeTime;
    /**
     * 取消关注时间
     */
    private LocalDateTime unsubscribeTime;


    /**
     * 会话密钥 session_key 是对用户数据进行 加密签名 的密钥
     */
    private String sessionKey;


    /**
     * 用户id，可能是会员id， 也可能是合作伙伴id
     */
    private Long userId;

    /**
     * 用户类型，1：会员； 2：合作伙伴；
     */
    private Integer userType;


    /**
     * 用户类型，1：会员；
     */
    public static final int USER_TYPE_1 = 1;

    /**
     * 用户类型，2：合作伙伴；
     */
    public static final int USER_TYPE_2 = 2;

}
