package cn.aguyao.module.charging.service.device;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.device.vo.DevicePageReqVO;
import cn.aguyao.module.charging.controller.admin.device.vo.DeviceSaveReqVO;
import cn.aguyao.module.charging.controller.app.device.vo.AppChargingAwaitingRespVO;
import cn.aguyao.module.charging.controller.app.device.vo.AppChargingReqVO;
import cn.aguyao.module.charging.controller.app.device.vo.AppDeviceUseStateRespVO;
import cn.aguyao.module.charging.controller.app.schemecollect.vo.AppSchemeCollectRespVO;
import cn.aguyao.module.charging.controller.mgr.device.vo.MgrDeviceListRespVO;
import cn.aguyao.module.charging.controller.mgr.device.vo.MgrDeviceSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.building.BuildingDO;
import cn.aguyao.module.charging.dal.dataobject.chargerecord.ChargeRecordDO;
import cn.aguyao.module.charging.dal.dataobject.community.CommunityDO;
import cn.aguyao.module.charging.dal.dataobject.device.DeviceDO;
import cn.aguyao.module.charging.dal.dataobject.deviceCode.DeviceCodeDO;
import cn.aguyao.module.charging.dal.dataobject.scheme.SchemeDO;
import cn.aguyao.module.charging.dal.dataobject.schemecollect.SchemeCollectDO;
import cn.aguyao.module.charging.dal.mysql.device.DeviceMapper;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.enums.ChargeModeEnum;
import cn.aguyao.module.charging.enums.DeviceBtStatEnum;
import cn.aguyao.module.charging.enums.DeviceOrderSeqConst;
import cn.aguyao.module.charging.enums.RedisKeyConstants;
import cn.aguyao.module.charging.service.MqttService;
import cn.aguyao.module.charging.service.RedisService;
import cn.aguyao.module.charging.service.building.BuildingService;
import cn.aguyao.module.charging.service.chargerecord.ChargeRecordService;
import cn.aguyao.module.charging.service.community.CommunityService;
import cn.aguyao.module.charging.service.deviceCode.DeviceCodeService;
import cn.aguyao.module.charging.service.mbe.MbeService;
import cn.aguyao.module.charging.service.scheme.SchemeService;
import cn.aguyao.module.charging.service.schemecollect.SchemeCollectService;
import cn.aguyao.module.system.api.serial.SerialApi;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.DEVICE_NOT_EXISTS;

/**
 * 设备 Service 实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Validated
public class DeviceServiceImpl implements DeviceService {

    String[] ignoreProperties = {"id", "remark", "communityId", "buildingId"};

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private SerialApi serialApi;

    @Resource
    private MqttService mqttService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private CommunityService communityService;

    @Resource
    private BuildingService buildingService;

    @Resource
    private MbeService mbeService;

    @Resource
    private SchemeService schemeService;

    @Resource
    private SchemeCollectService schemeCollectService;

    @Lazy
    @Resource
    private ChargeRecordService chargeRecordService;

    @Resource
    private RedisService redisService;

    @Resource
    private DeviceCodeService deviceCodeService;


//    @Resource
//    private MonthlyPkgRecordService monthlyPkgRecordService;


    @Override
    public Long createDevice(DeviceSaveReqVO createReqVO) {

//        String code = serialApi.getCode(PrefixConstants.PREFIX_DE);
        // 插入
        DeviceDO device = BeanUtils.toBean(createReqVO, DeviceDO.class);
//        device.setCode(code);
        deviceMapper.insert(device);

        // 更新设备信息
        redisTemplate.opsForHash().put(RedisKeyConstants.BASIC_DEVICE_INFO, device.getDevice(), JSONObject.toJSONString(device));
        // 返回
        return device.getId();
    }

    @Override
    public Long createDevice(DeviceDO device) {

//        String code = serialApi.getCode(PrefixConstants.PREFIX_DE);
//        device.setCode(code);
        deviceMapper.insert(device);
        // 返回
        return device.getId();
    }

    @Override
    public void updateDevice(DeviceDO device) {
        DeviceDO deviceDO = deviceMapper.findByDevice(device.getDevice());
        if (Objects.nonNull(deviceDO)) {
            deviceDO.setPort1(device.getPort1());
            deviceDO.setPort2(device.getPort2());
            deviceDO.setPower1(device.getPower1());
            deviceDO.setPower2(device.getPower2());
            deviceDO.setBtStat1(device.getBtStat1());
            deviceDO.setBtStat2(device.getBtStat2());
            deviceDO.setEnergy1(device.getEnergy1());
            deviceDO.setEnergy2(device.getEnergy2());
            deviceDO.setOutTime1(device.getOutTime1());
            deviceDO.setOutTime2(device.getOutTime2());
            deviceDO.setUpdateTime(LocalDateTime.now());
            deviceMapper.updateById(deviceDO);

            // 更新设备信息
            redisTemplate.opsForHash().put(RedisKeyConstants.BASIC_DEVICE_INFO, deviceDO.getDevice(), JSONObject.toJSONString(deviceDO));
        }
    }

    @Override
    public void createOrUpdate(DeviceDO device) {
        try {
            DeviceCodeDO deviceCode = deviceCodeService.findByDevice(device.getDevice());
            DeviceDO deviceDO = deviceMapper.findByDevice(device.getDevice());
            if (Objects.nonNull(deviceDO)) {
                BeanUtil.copyProperties(device, deviceDO, ignoreProperties);
                deviceDO.setCode(deviceCode.getCode());
                deviceMapper.updateById(deviceDO);
            } else {
                device.setCode(deviceCode.getCode());
                deviceMapper.insert(device);
                deviceDO = new DeviceDO();
                BeanUtil.copyProperties(device, deviceDO);
            }

            redisTemplate.opsForHash().put(RedisKeyConstants.BASIC_DEVICE_INFO, deviceDO.getDevice(), JSONObject.toJSONString(deviceDO));
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    @Override
    public void updateDevice(DeviceSaveReqVO updateReqVO) {
        // 校验存在
        validateDeviceExists(updateReqVO.getId());
        // 更新
        DeviceDO device = BeanUtils.toBean(updateReqVO, DeviceDO.class);
        deviceMapper.updateById(device);

        DeviceDO entity = this.getDevice(device.getId());

        // 更新设备信息
        redisTemplate.opsForHash().put(RedisKeyConstants.BASIC_DEVICE_INFO, entity.getDevice(), JSONObject.toJSONString(entity));
    }

    @Override
    public void deleteDevice(Long id) {
        // 校验存在
        validateDeviceExists(id);
        // 删除
        deviceMapper.deleteById(id);
    }

    private void validateDeviceExists(Long id) {
        if (deviceMapper.selectById(id) == null) {
            throw exception(DEVICE_NOT_EXISTS);
        }
    }

    @Override
    public DeviceDO getDevice(Long id) {
        DeviceDO deviceDO = deviceMapper.selectById(id);
        if (deviceDO == null) {
            throw exception(DEVICE_NOT_EXISTS);
        }
        if (Objects.nonNull(deviceDO.getCommunityId())) {
            CommunityDO community = communityService.getCommunity(deviceDO.getCommunityId());
            Optional.ofNullable(community).ifPresent(c -> deviceDO.setCommunityName(c.getName()));
        }

        JSONObject jsonObject = redisService.getHeartbeat(deviceDO.getDevice());
        if (Objects.nonNull(jsonObject)) {
            deviceDO.setNetworkStatus(1);
            deviceDO.setLatestTime(jsonObject.getString("latestTime"));
        }

        return deviceDO;
    }

    @Override
    public PageResult<DeviceDO> getDevicePage(DevicePageReqVO pageReqVO) {

//        PageResult<DeviceDO> pageResult = deviceMapper.selectPage(pageReqVO);

        IPage iPage = new Page(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        IPage<DeviceDO> page = deviceMapper.findPage(iPage, pageReqVO);
        PageResult<DeviceDO> pageResult =  new PageResult<>(page.getRecords(), page.getTotal());

        if (Objects.nonNull(pageResult) && !pageResult.getList().isEmpty()) {
            pageResult.getList().forEach(
                    entity -> {
                        // 小区名称
                        if (Objects.nonNull(entity.getCommunityId())) {
                            CommunityDO communityDO = communityService.getCommunity(entity.getCommunityId());
                            Optional.ofNullable(communityDO).ifPresent(
                                    community -> {
                                        entity.setCommunityName(community.getName());
                                    }
                            );
                        }
                        // 楼栋名称
                        if (Objects.nonNull(entity.getBuildingId())) {
                            BuildingDO buildingDO = buildingService.getBuilding(entity.getBuildingId());
                            Optional.ofNullable(buildingDO).ifPresent(
                                    building -> {
                                        entity.setBuildingName(building.getName());
                                    }
                            );

                            // 收费方案
                            if (Objects.nonNull(buildingDO) && Objects.nonNull(buildingDO.getSchemeId())) {
                                SchemeDO schemeDO = schemeService.getScheme(buildingDO.getSchemeId());
                                Optional.ofNullable(schemeDO).ifPresent(
                                        scheme -> {
                                            entity.setSchemeName(scheme.getName());
                                        }
                                );
                            }

                            if (Objects.nonNull(entity.getDevice())) {
                                String key = RedisKeyConstants.DEVICE_HEARTBEAT_DETECTION + entity.getDevice();
                                Object obj = redisTemplate.opsForValue().get(key);
                                if (Objects.nonNull(obj)) {
                                    entity.setNetworkStatus(1);
                                }
                            }

                        }


                    }
            );

        }
        return pageResult;
    }


    @Override
    public DeviceDO findByCode(String code) {
        return deviceMapper.findByCode(code);
    }

    @Override
    public DeviceDO findByDevice(String device) {
        return deviceMapper.findByDevice(device);
    }

    @Override
    public void startRecharge(AppChargingReqVO reqVO) throws Exception {
        //
        Long userId = SecurityFrameworkUtils.getLoginUserIdCheck();
        // 1、 发起开电请求
        log.info("用户：{}， 发起开电请求，设备：{}，端口：{}，功率：{}", userId, reqVO.getDevice(), reqVO.getPort(), reqVO.getPower());
        mqttService.openPort(reqVO.getDevice(), reqVO.getPort(), String.valueOf(userId));
    }

    /**
     * 停止充电
     * @param reqVO
     */
    @Override
    public void stopRecharge(AppChargingReqVO reqVO) throws Exception {
        // 1、发起停电请求
        Long userId = SecurityFrameworkUtils.getLoginUserIdCheck();
        mqttService.closePort(reqVO.getDevice(), reqVO.getPort(), String.valueOf(userId));
    }

    @Override
    public AppDeviceUseStateRespVO useState(String device, Long mpId) {
        String seq = DeviceOrderSeqConst.DEVICE_STATUS + "_" + device;
        try {

            log.info("-------------- 获取设备状态，key = seq：{}", seq);
            JSONObject jsonObject = redisService.getPortStatus(device);
            if (Objects.isNull(jsonObject)) {
                mqttService.portStatus(device, seq);
            }

            int num = 0;
            // 等待
            while (true) {
                if (num > 75) {
                    throw new RuntimeException("网络异常，刷新重试！");
                }

                if (Objects.isNull(jsonObject)) {
                    jsonObject = redisService.getPortStatus(device);
                }

                log.info("-------------- 获取设备状态：{}", jsonObject);
                if (Objects.nonNull(jsonObject)) {
                    /**
                     *  [
                     *  {"Port":0,"Power":0,OutTime":0,"BtStat":0,"Energy":"12340"},
                     *  {"Port":1,"Power":0,OutTime":0,"BtStat":0,"Energy":"12340"}
                     *  ]
                     */
                    JSONArray jsonArray = jsonObject.getJSONArray("ALLDATA");

                    AppDeviceUseStateRespVO respVO = new AppDeviceUseStateRespVO();
                    respVO.setDevice(device);
                    respVO.setPortInfo(jsonArray);

                    // 默认，不是当前用户
                    respVO.setCurrentUser(false);
                    respVO.setChargingState(ChargeRecordDO.PROCEED_STATUS_0);
                    for (int i = 0; i < jsonArray.size(); i++) {

                        JSONObject obj = jsonArray.getJSONObject(i);
                        Integer port = obj.getInteger("Port");
                        Integer btStat = obj.getInteger("BtStat");

                        if (Objects.equals(btStat, DeviceBtStatEnum.BT_STAT_0.getValue())) {
                            // 待机
                        } else if (Objects.equals(btStat, DeviceBtStatEnum.BT_STAT_1.getValue())) {
                            // 充电中
                            ChargeRecordDO chargeRecordDO = chargeRecordService.findByDeviceAndPort(device, port, ChargeRecordDO.PROCEED_STATUS_1);
                            if (Objects.nonNull(chargeRecordDO) && chargeRecordDO.getMpId().equals(mpId)) {
                                respVO.setPortNum(port);
                                respVO.setCurrentUser(true);
                                respVO.setOrderId(chargeRecordDO.getId());
                                respVO.setChargingState(ChargeRecordDO.PROCEED_STATUS_1);
                            }
                        } else if (Objects.equals(btStat, DeviceBtStatEnum.BT_STAT_2.getValue())
                            || Objects.equals(btStat, DeviceBtStatEnum.BT_STAT_3.getValue())) {
                            // 充电完成
                            ChargeRecordDO chargeRecordDO = chargeRecordService.findByDeviceAndPort(device, port, ChargeRecordDO.PROCEED_STATUS_2);
                            if (Objects.nonNull(chargeRecordDO) && chargeRecordDO.getMpId().equals(mpId)) {
                                respVO.setPortNum(port);
                                respVO.setCurrentUser(true);
                                respVO.setOrderId(chargeRecordDO.getId());
                                respVO.setChargingState(ChargeRecordDO.PROCEED_STATUS_2);
                            }
                        } else {
                            // 异常 todo
                            log.info("--------------查询设备状态，其他情况-------------btStat： {}", btStat);
                        }
                    }

//                    redisService.delPortStatus(device);
                    return respVO;
                } else {
                    try {
                        Thread.sleep(200);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
                num ++;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public AppChargingAwaitingRespVO awaitingInfo(String device, Long mpId) {
        AppChargingAwaitingRespVO respVO = new AppChargingAwaitingRespVO();
        // 1、 获取用户信息
        MbeDO mbeDO = mbeService.selectMemberByMpUserId(mpId);
        respVO.setMpId(mpId);
        respVO.setMbeId(mbeDO.getId());
        respVO.setMbeCode(mbeDO.getCode());
        respVO.setGiftBalance(mbeDO.getGiftBalance());
        respVO.setRechargeBalance(mbeDO.getRechargeBalance());

        // 2、 获取充电费率
        BuildingDO buildingDO = buildingService.getRateInfoByDevice(device);
        if (Objects.nonNull(buildingDO)) {
            respVO.setRateInfo(buildingDO.getRate());

            // 跟设备对应的小区充电的收费方式有关，只传一个就好
            SchemeDO schemeDO = schemeService.selectByDevice(device);
            if (Objects.nonNull(schemeDO) && schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_1.getValue())) {
                // 按时间计费
                // 3、 获取单次充电套餐
                List<SchemeCollectDO> byTime = schemeCollectService.findBySchemeId(buildingDO.getSchemeId(), ChargeModeEnum.CHARGE_MODE_1.getValue());
                List<AppSchemeCollectRespVO> byTimeList = BeanUtils.toBean(byTime, AppSchemeCollectRespVO.class);
                respVO.setSingleChargingSetmeal(byTimeList);

                // 4、 获取临时充电套餐
                respVO.setTempChargingSetmeal(byTimeList);

            } else if (Objects.nonNull(schemeDO) && schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_2.getValue())) {
                // 按电量计费
                // 4、 获取临时充电套餐
                List<SchemeCollectDO> byElectric = schemeCollectService.findBySchemeId(buildingDO.getSchemeId(), ChargeModeEnum.CHARGE_MODE_2.getValue());
                List<AppSchemeCollectRespVO> byElectricList = BeanUtils.toBean(byElectric, AppSchemeCollectRespVO.class);
                respVO.setTempChargingSetmeal(byElectricList);

                respVO.setSingleChargingSetmeal(byElectricList);
            }  else if (Objects.nonNull(schemeDO) && schemeDO.getType().equals(ChargeModeEnum.CHARGE_MODE_3.getValue())) {
                // 按电量计费
                // 4、 获取临时充电套餐
                List<SchemeCollectDO> byElectric = schemeCollectService.findBySchemeId(buildingDO.getSchemeId(), ChargeModeEnum.CHARGE_MODE_3.getValue());
                List<AppSchemeCollectRespVO> byElectricList = BeanUtils.toBean(byElectric, AppSchemeCollectRespVO.class);
                respVO.setTempChargingSetmeal(byElectricList);

                respVO.setSingleChargingSetmeal(byElectricList);
            }


        }

        return respVO;
    }




    ////////////////////////////////// 小程序——管理端 【start】 ////////////////////////////////////////
    @Override
    public Boolean updateRemark(MgrDeviceSaveReqVO reqVO) {

        DeviceDO device = this.getDevice(reqVO.getId());
        if (device == null) {
            throw exception(DEVICE_NOT_EXISTS);
        }

        device.setRemark(reqVO.getRemark());
        int result = deviceMapper.updateById(device);

        return result > 0;
    }

    @Override
    public List<DeviceDO> findAllDevice() {
        return deviceMapper.selectList();
    }

    @Override
    public List<MgrDeviceListRespVO> inventory(Long buildingId) {
        List<DeviceDO> list = deviceMapper.selectList(DeviceDO::getBuildingId, buildingId);
        if (CollUtil.isNotEmpty(list)) {
            List<MgrDeviceListRespVO> rtList = new ArrayList<>();
            for (DeviceDO deviceDO : list) {
                MgrDeviceListRespVO respVO = new MgrDeviceListRespVO();
                respVO.setId(deviceDO.getId());
                respVO.setDevice(deviceDO.getDevice());
                respVO.setRemark(deviceDO.getRemark());
                respVO.setCreateTime(deviceDO.getCreateTime());

                JSONObject jsonObject = redisService.getHeartbeat(deviceDO.getDevice());
                if (Objects.nonNull(jsonObject)) {
                    JSONArray protInfo = jsonObject.getJSONArray("ALLDATA");
                    respVO.setNetworkStatus(1);
                    Integer btStat1 = ((JSONObject) protInfo.get(0)).getInteger("BtStat");
                    Integer btStat2 = ((JSONObject) protInfo.get(1)).getInteger("BtStat");

                    respVO.setBtStat1(btStat1);
                    respVO.setBtStat2(btStat2);
                } else {
                    respVO.setNetworkStatus(0);
                }

                rtList.add(respVO);
            }

            return rtList;
        }
        return null;
    }


    @Override
    public CommonResult<String> poweronTest(String device, Integer port) {
        CommonResult<String> result = CommonResult.success();
        try {
            JSONObject jsonObject = redisService.getHeartbeat(device);
            if (Objects.nonNull(jsonObject)) {
                mqttService.openPort(device, port, System.currentTimeMillis() + "");
                result.setMsg("通电测试中");
            } else {
                result.setMsg("设备不在线，请稍后再试");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return result;
    }

    @Override
    public List<DeviceDO> findByBuildingId(Long buildingId) {
        return deviceMapper.selectList(DeviceDO::getBuildingId, buildingId);
    }

}
