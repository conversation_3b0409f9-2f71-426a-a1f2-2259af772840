package cn.aguyao.module.charging.controller.mgr.partner;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.incomerecord.vo.IncomeRecordPageReqVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCollectRespVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrIncomeCommunityRespVO;
import cn.aguyao.module.charging.controller.mgr.partner.vo.MgrPartnerBalanceRespVO;
import cn.aguyao.module.charging.dal.dataobject.incomerecord.IncomeRecordDO;
import cn.aguyao.module.charging.service.partner.PartnerService;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合作伙伴——小区管理人员（物业）")
@RestController
@RequestMapping("/charging/partner")
@Validated
public class MgrPartnerController {

    @Resource
    private PartnerService partnerService;


    /**
     * 账号余额
     * @return
     */
    @GetMapping("/balance")
    @Operation(summary = "获得账户余额")
    public CommonResult<MgrPartnerBalanceRespVO> balance() {
        MgrPartnerBalanceRespVO resp = partnerService.balance();
        return success(resp);
    }


    /**
     * 提现
     */
    @PostMapping("/withdraw")
    @Operation(summary = "提现")
    @Parameter(name = "amount", description = "提现金额")
    public CommonResult<Boolean> withdraw(@RequestBody @NotBlank String amount) {
        JSONObject json = new JSONObject(amount);
        Boolean result = partnerService.withdraw(json.getBigDecimal("amount"));
        return success(result);
    }



    /////////////////////////////////////// 收益统计 start //////////////////////////////////////////
    /**
     * 收益统计,汇总
     * @return
     */
    @GetMapping("/income/collect")
    @Operation(summary = "收益统计")
    public CommonResult<MgrIncomeCollectRespVO> incomeCollect(@RequestParam("communityName") String communityName) {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        MgrIncomeCollectRespVO resp = partnerService.incomeCollect(mpId, communityName);
        return success(resp);
    }

    /**
     * 收益统计，按小区汇总
     * @return
     */
    @GetMapping("/income/collect/page")
    @Operation(summary = "收益统计")
    public CommonResult<PageResult<MgrIncomeCommunityRespVO>> incomeCollectPage(@RequestParam("pageNo") Integer pageNo,
                                                                                @RequestParam("pageSize") Integer pageSize,
                                                                                @RequestParam("communityName") String communityName) {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        IncomeRecordPageReqVO pageReqVO = new IncomeRecordPageReqVO();
        pageReqVO.setMpId(mpId);
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);
        pageReqVO.setCommunityName(communityName);
        PageResult<MgrIncomeCommunityRespVO> resp = partnerService.incomeCollectPage(pageReqVO);
        return success(resp);
    }

    /**
     * 收益明细
     * @return
     */
    @GetMapping("/income/detail")
    @Operation(summary = "收益明细")
    @Parameters({@Parameter(name = "communityId", description = "小区id"),
            @Parameter(name = "startTime", description = "开始时间"),
            @Parameter(name = "endTime", description = "结束时间")})
    public CommonResult<PageResult<IncomeRecordDO>> incomeDetail(@RequestParam("communityId") Long communityId,
                                                                 @RequestParam("startTime") String startTime,
                                                                 @RequestParam("endTime") String endTime,
                                                                 @RequestParam("pageNo") Integer pageNo,
                                                                 @RequestParam("pageSize") Integer pageSize) {
        IncomeRecordPageReqVO pageReqVO = new IncomeRecordPageReqVO();
        pageReqVO.setCommunityId(communityId);
        LocalDateTime[] datetime = DateUtils.dateStr2LocalDateTimeArray(startTime, endTime);
        pageReqVO.setCreateTime(datetime);
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);

        PageResult<IncomeRecordDO> pageResult = partnerService.incomeDetail(pageReqVO);
        return success(pageResult);
    }

    /////////////////////////////////////// 收益统计 end //////////////////////////////////////////
}
