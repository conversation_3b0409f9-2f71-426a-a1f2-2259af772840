package cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 包月记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MonthlyPkgRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "21264")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "用户编号")
    @ExcelProperty("用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    @ExcelProperty("用户手机号")
    private String mobile;

    @Schema(description = "消费金额")
    @ExcelProperty("消费金额")
    private BigDecimal amount;

    @Schema(description = "购买时间")
    @ExcelProperty("购买时间")
    private LocalDateTime purchaseTime;

    @Schema(description = "包月小区id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2318")
    @ExcelProperty("包月小区id")
    private Long belongCommunityId;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    @Schema(description = "包月小区", requiredMode = Schema.RequiredMode.REQUIRED, example = "2318")
    @ExcelProperty("包月小区")
    private String belongCommunityName;
}
