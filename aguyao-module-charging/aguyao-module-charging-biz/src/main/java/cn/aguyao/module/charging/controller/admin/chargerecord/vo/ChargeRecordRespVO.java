package cn.aguyao.module.charging.controller.admin.chargerecord.vo;

import cn.aguyao.framework.excel.core.annotations.DictFormat;
import cn.aguyao.framework.excel.core.convert.DictConvert;
import cn.aguyao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 充电记录，消费记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ChargeRecordRespVO {

//    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "25604")
//    @ExcelProperty("主键")
//    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

//    @Schema(description = "用户id")
//    @ExcelProperty("用户id")
//    private Long mbeId;

    @Schema(description = "用户编号")
    @ExcelProperty("用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    @ExcelProperty("用户手机号")
    private String mobile;

//    @Schema(description = "充电时间")
//    @ExcelProperty("充电时间")
//    private LocalDateTime chargeTime;

//    @Schema(description = "充电小区id", example = "3424")
//    @ExcelProperty("充电小区id")
//    private Long chargeCommunityId;

    @Schema(description = "充电小区名称", example = "3424")
    @ExcelProperty("充电小区名称")
    private String chargeCommunityName;

//    @Schema(description = "楼栋id", example = "14702")
//    @ExcelProperty("楼栋id")
//    private Long buildingsId;

    @Schema(description = "楼栋名称", example = "14702")
    @ExcelProperty("楼栋名称")
    private String buildingsName;

//    @Schema(description = "设备id", example = "26557")
//    @ExcelProperty("设备id")
//    private Long deviceId;
    @Schema(description = "设备编码", example = "26557")
    @ExcelProperty("设备编码")
    private String device;

    @Schema(description = "开始充电时间", example = "你猜")
    @ExcelProperty("开始充电的时间")
    private LocalDateTime chargeTimeStart;

    @Schema(description = "结束充电的时间", example = "你猜")
    @ExcelProperty("结束充电时间")
    private LocalDateTime chargeTimeEnd;

    @Schema(description = "消费金额")
    @ExcelProperty("消费金额")
    private BigDecimal actualAmount;

    @Schema(description = "支付方式")
    @ExcelProperty(value = "支付方式", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.CHARGING_PAY_SOURCE)
    private Integer paySource;

    @Schema(description = "进行状态")
    @ExcelProperty(value = "进行状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.CHARGING_PROCEED_STATUS)
    private Integer proceedStatus;

    @Schema(description = "瓦数(瓦·分钟)")
    @ExcelProperty("瓦数(瓦·分钟)")
    private BigDecimal wattage;

    @Schema(description = "度数")
    @ExcelProperty("度数")
    private BigDecimal kilowatt;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "功率", example = "100")
    @ExcelProperty("功率")
    private Integer power;

    @Schema(description = "实际充电时间", example = "100")
    @ExcelProperty("实际充电时间")
    private String actualChargeTime;

    @Schema(description = "守护充电")
    @ExcelProperty(value = "守护充电", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.CHARGING_GUARD_FALG)
    private Integer guardFalg;
}
