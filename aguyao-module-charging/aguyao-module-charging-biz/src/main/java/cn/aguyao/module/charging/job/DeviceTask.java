package cn.aguyao.module.charging.job;

import org.springframework.scheduling.annotation.Scheduled;

public class DeviceTask {

    // Spring Boot 服务端定时检查
    @Scheduled(fixedRate = 30000)
    public void checkDeviceStatus() {
//        List<Device> devices = deviceRepository.findAll();
//        devices.forEach(device -> {
//            if (System.currentTimeMillis() - device.getLastActive() > 60000) {
//                device.setOnline(false);
//                eventPublisher.publishEvent(new DeviceOfflineEvent(device.getId()));
//            }
//        });
    }
}
