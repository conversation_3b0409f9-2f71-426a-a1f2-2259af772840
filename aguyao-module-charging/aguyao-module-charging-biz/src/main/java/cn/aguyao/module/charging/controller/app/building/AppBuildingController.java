package cn.aguyao.module.charging.controller.app.building;

import cn.aguyao.framework.common.exception.ServiceException;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.building.vo.BuildingRespVO;
import cn.aguyao.module.charging.controller.app.building.vo.AppBuildingRespVO;
import cn.aguyao.module.charging.dal.dataobject.building.BuildingDO;
import cn.aguyao.module.charging.service.building.BuildingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序端 - 楼栋")
@RestController
@RequestMapping("/charging/building")
@Validated
public class AppBuildingController {

    @Resource
    private BuildingService buildingService;


    @GetMapping("/rate/list")
    @Operation(summary = "获得小区各楼栋费率信息")
    @Parameter(name = "communityId", description = "小区id", required = true, example = "1024")
    public CommonResult<List<AppBuildingRespVO>> getBuildingList(@RequestParam(value = "communityId", required = false) Long communityId) {
        if (Objects.isNull(communityId)) {
            throw new ServiceException(1, "未关联小区，无法获取费率");
        }
        List<BuildingDO> list = buildingService.getBuildingList(communityId);
        return success(BeanUtils.toBean(list, AppBuildingRespVO.class));
    }


    @GetMapping("/rate/get")
    @Operation(summary = "获得楼栋")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<BuildingRespVO> getBuilding(@RequestParam("id") Long id) {
        BuildingDO building = buildingService.getBuilding(id);
        return success(BeanUtils.toBean(building, BuildingRespVO.class));
    }

}
