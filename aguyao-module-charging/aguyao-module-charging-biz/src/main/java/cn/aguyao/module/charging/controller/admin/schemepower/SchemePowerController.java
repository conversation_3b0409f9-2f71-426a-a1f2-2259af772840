package cn.aguyao.module.charging.controller.admin.schemepower;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.schemepower.vo.SchemePowerPageReqVO;
import cn.aguyao.module.charging.controller.admin.schemepower.vo.SchemePowerRespVO;
import cn.aguyao.module.charging.controller.admin.schemepower.vo.SchemePowerSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.schemepower.SchemePowerDO;
import cn.aguyao.module.charging.service.schemepower.SchemePowerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 收费方案—功率档位")
@RestController
@RequestMapping("/charging/scheme-power")
@Validated
public class SchemePowerController {

    @Resource
    private SchemePowerService schemePowerService;

    @PostMapping("/create")
    @Operation(summary = "创建收费方案—功率档位")
    @PreAuthorize("@ss.hasPermission('charging:scheme-power:create')")
    public CommonResult<Long> createSchemePower(@Valid @RequestBody SchemePowerSaveReqVO createReqVO) {
        return success(schemePowerService.createSchemePower(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新收费方案—功率档位")
    @PreAuthorize("@ss.hasPermission('charging:scheme-power:update')")
    public CommonResult<Boolean> updateSchemePower(@Valid @RequestBody SchemePowerSaveReqVO updateReqVO) {
        schemePowerService.updateSchemePower(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除收费方案—功率档位")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:scheme-power:delete')")
    public CommonResult<Boolean> deleteSchemePower(@RequestParam("id") Long id) {
        schemePowerService.deleteSchemePower(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得收费方案—功率档位")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:scheme-power:query')")
    public CommonResult<SchemePowerRespVO> getSchemePower(@RequestParam("id") Long id) {
        SchemePowerDO schemePower = schemePowerService.getSchemePower(id);
        return success(BeanUtils.toBean(schemePower, SchemePowerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得收费方案—功率档位分页")
    @PreAuthorize("@ss.hasPermission('charging:scheme-power:query')")
    public CommonResult<PageResult<SchemePowerRespVO>> getSchemePowerPage(@Valid SchemePowerPageReqVO pageReqVO) {
        PageResult<SchemePowerDO> pageResult = schemePowerService.getSchemePowerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SchemePowerRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出收费方案—功率档位 Excel")
    @PreAuthorize("@ss.hasPermission('charging:scheme-power:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSchemePowerExcel(@Valid SchemePowerPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SchemePowerDO> list = schemePowerService.getSchemePowerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "收费方案—功率档位.xls", "数据", SchemePowerRespVO.class,
                        BeanUtils.toBean(list, SchemePowerRespVO.class));
    }

}