package cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 提现记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WithdrawalRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "32603")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "主管id")
    private Long partnerId;

    @Schema(description = "申请时间")
    @ExcelProperty("申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "申请金额")
    @ExcelProperty("申请金额")
    private BigDecimal applyAmount;

    @Schema(description = "提现银行名称", example = "芋艿")
    @ExcelProperty("提现银行名称")
    private String bankName;

    @Schema(description = "银行卡号")
    @ExcelProperty("银行卡号")
    private String bankCardNum;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "申请状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("申请状态")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    ////////////////////////////////////////
    @Schema(description = "主管编号", example = "随便")
    @ExcelProperty("主管编号")
    private String partnerCode;

    @Schema(description = "主管名称", example = "随便")
    @ExcelProperty("主管名称")
    private String partnerName;

    @Schema(description = "主管手机", example = "随便")
    @ExcelProperty("主管手机")
    private String partnerMobile;


}
