package cn.aguyao.module.charging.service.refundrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.refundrecord.RefundRecordDO;
import cn.aguyao.module.charging.enums.RefundEnum;

import javax.validation.Valid;

/**
 * 退款记录 Service 接口
 *
 * <AUTHOR>
 */
public interface RefundRecordService {

    /**
     * 创建退款记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRefundRecord(@Valid RefundRecordSaveReqVO createReqVO);

    /**
     * 更新退款记录
     *
     * @param updateReqVO 更新信息
     */
    void updateRefundRecord(@Valid RefundRecordSaveReqVO updateReqVO);

    /**
     * 删除退款记录
     *
     * @param id 编号
     */
    void deleteRefundRecord(Long id);

    /**
     * 获得退款记录
     *
     * @param id 编号
     * @return 退款记录
     */
    RefundRecordDO getRefundRecord(Long id);

    /**
     * 获得退款记录分页
     *
     * @param pageReqVO 分页查询
     * @return 退款记录分页
     */
    PageResult<RefundRecordDO> getRefundRecordPage(RefundRecordPageReqVO pageReqVO);

    /**
     * 重新发起退款
     * @param id
     */
    void handleRefund(Long id);

    RefundRecordDO getRefundRecordByOrderNo(String orderNo);

    void notifyHandle(String merchantOrderId, Integer refundPrice, RefundEnum.RefundStatusEnum refundStatusEnum);
}
