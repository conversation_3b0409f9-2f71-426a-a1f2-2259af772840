package cn.aguyao.module.charging.controller.mgr.community.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 小区楼栋 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MgrBuildingInfoVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1376")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "楼栋名称")
    @ExcelProperty("楼栋名称")
    private String bname;

    @Schema(description = "备注信息")
    @ExcelProperty("备注信息")
    private String remark;

    /**
     * 总设备数 = 空闲设备数 + 使用设备数 + 故障设备数 + 离线设备数 + 禁用设备数
     */
    @Schema(description = "总设备数")
    @ExcelProperty("总设备数")
    private Integer totalCount;

    /**
     * 空闲设备数
     */
    @Schema(description = "空闲设备数")
    @ExcelProperty("空闲设备数")
    private Integer idleCount;

    /**
     * 使用设备数
     */
    @Schema(description = "使用设备数")
    @ExcelProperty("使用设备数")
    private Integer occupiedCount;

    /**
     * 故障设备数
     */
    @Schema(description = "故障设备数")
    @ExcelProperty("故障设备数")
    private Integer faultCount;

    /**
     * 离线设备数
     */
    @Schema(description = "离线设备数")
    @ExcelProperty("离线设备数")
    private Integer offlineCount;

    /**
     * 禁用设备数
     */
    @Schema(description = "禁用设备数")
    @ExcelProperty("禁用设备数")
    private Integer forbiddenCount;
}
