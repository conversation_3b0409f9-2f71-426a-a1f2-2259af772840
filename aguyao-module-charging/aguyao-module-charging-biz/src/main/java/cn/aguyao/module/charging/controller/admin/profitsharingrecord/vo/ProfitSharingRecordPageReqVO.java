package cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 分润记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProfitSharingRecordPageReqVO extends PageParam {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "主管编号")
    private String partnerCode;

    @Schema(description = "主管名称", example = "赵六")
    private String partnerName;

    @Schema(description = "主管手机")
    private String partnerMobile;

    @Schema(description = "费率")
    private BigDecimal rate;

    @Schema(description = "分润")
    private BigDecimal profitSharing;

    @Schema(description = "所属小区id", example = "15690")
    private Long belongCommunityId;

    @Schema(description = "用户手机")
    private String mbeMobile;

    @Schema(description = "消费时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] consumeTime;

    @Schema(description = "消费金额")
    private BigDecimal consumeAmount;

    @Schema(description = "消费类型", example = "2")
    private Integer consumeType;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}