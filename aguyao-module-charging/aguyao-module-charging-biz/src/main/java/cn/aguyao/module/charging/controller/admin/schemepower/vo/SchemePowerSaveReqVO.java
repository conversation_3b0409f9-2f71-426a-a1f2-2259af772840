package cn.aguyao.module.charging.controller.admin.schemepower.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 收费方案—功率档位新增/修改 Request VO")
@Data
public class SchemePowerSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26629")
    private Long id;

    @Schema(description = "方案id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7111")
    @NotNull(message = "方案id不能为空")
    private Long schemeId;

    @Schema(description = "序号")
    private Integer sort;

    @Schema(description = "功率区间，左侧（包含）")
    private Integer startPower;

    @Schema(description = "功率区间，右侧（不含）")
    private Integer endPower;

    @Schema(description = "收费标准（单位：元）")
    private BigDecimal amount;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}
