package cn.aguyao.module.charging.controller.admin.chargerecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 充电记录，消费记录新增/修改 Request VO")
@Data
public class ChargeRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "25604")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "用户id")
    private Long mbeId;

    @Schema(description = "用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "充电时间")
    private LocalDateTime chargeTime;

    @Schema(description = "充电小区id", example = "3424")
    private Long chargeCommunityId;

    @Schema(description = "楼栋id", example = "14702")
    private Long buildingsId;

    @Schema(description = "设备id", example = "26557")
    private Long deviceId;

    @Schema(description = "设备编码", example = "26557")
    private String device;

    @Schema(description = "消费金额")
    private BigDecimal amount;

    @Schema(description = "支付来源，消费来源")
    private Integer paySource;

    @Schema(description = "进行状态", example = "1")
    private Integer proceedStatus;

    @Schema(description = "瓦数，消耗的瓦数")
    private BigDecimal wattage;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

}
