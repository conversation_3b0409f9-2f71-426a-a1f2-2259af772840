package cn.aguyao.module.charging.dal.dataobject.insureconfig;

import cn.aguyao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 投保配置 DO
 *
 * <AUTHOR>
 */
@TableName("charging_insure_config")
@KeySequence("charging_insure_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsureConfigDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 优惠编号
     */
    private String code;
    /**
     * 投保标题
     */
    private String title;
    /**
     * 投保内容
     */
    private String content;
    /**
     * 备注
     */
    private String remark;
    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;

}