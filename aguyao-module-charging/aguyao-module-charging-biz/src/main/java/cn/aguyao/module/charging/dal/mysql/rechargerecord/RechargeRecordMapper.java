package cn.aguyao.module.charging.dal.mysql.rechargerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.rechargerecord.vo.RechargeRecordPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.rechargerecord.RechargeRecordDO;
import cn.aguyao.module.charging.enums.OrderStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 充值记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RechargeRecordMapper extends BaseMapperX<RechargeRecordDO> {

    default PageResult<RechargeRecordDO> selectPage(RechargeRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RechargeRecordDO>()
                .eqIfPresent(RechargeRecordDO::getMpId, reqVO.getMpId())
                .likeIfPresent(RechargeRecordDO::getCode, reqVO.getCode())
                .likeIfPresent(RechargeRecordDO::getMbeCode, reqVO.getMbeCode())
                .likeIfPresent(RechargeRecordDO::getMobile, reqVO.getMobile())
                .eqIfPresent(RechargeRecordDO::getAmount, reqVO.getAmount())
                .betweenIfPresent(RechargeRecordDO::getRechargeTime, reqVO.getRechargeTime())
                .eqIfPresent(RechargeRecordDO::getPaySource, reqVO.getPaySource())
                .eqIfPresent(RechargeRecordDO::getRemark, reqVO.getRemark())
                .eqIfPresent(RechargeRecordDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(RechargeRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RechargeRecordDO::getId));
    }

    default RechargeRecordDO selectByCode(String code) {
        return selectOne(new LambdaQueryWrapperX<RechargeRecordDO>()
                .eq(RechargeRecordDO::getCode, code));
    }

    default List<RechargeRecordDO> selectByMpId(Long mpId) {
        return selectList(new LambdaQueryWrapperX<RechargeRecordDO>()
                .eq(RechargeRecordDO::getMpId, mpId)
                .eq(RechargeRecordDO::getStatus, OrderStatusEnum.PAID.getValue())
                .orderByDesc(RechargeRecordDO::getCreateTime)
        );
    }
}
