package cn.aguyao.module.charging.controller.app.servicemanual;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.app.servicemanual.vo.AppServiceManualPageReqVO;
import cn.aguyao.module.charging.controller.app.servicemanual.vo.AppServiceManualRespVO;
import cn.aguyao.module.charging.dal.dataobject.servicemanual.ServiceManualDO;
import cn.aguyao.module.charging.service.servicemanual.ServiceManualService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 使用手册")
@RestController
@RequestMapping("/charging/service-manual")
@Validated
public class AppServiceManualController {

    @Resource
    private ServiceManualService serviceManualService;

    /**
     * 获得使用手册列表（数量少不分页）, 不返回具体内容
     * @return
     */
    @GetMapping("/list")
    @Operation(summary = "获得使用手册列表")
//    @PreAuthorize("@ss.hasPermission('charging:service-manual:query')")
    public CommonResult<List<AppServiceManualRespVO>> list(@Valid AppServiceManualPageReqVO pageReqVO) {
        pageReqVO.setHelpShow(true);
        List<ServiceManualDO> list = serviceManualService.list(pageReqVO);
        return success(BeanUtils.toBean(list, AppServiceManualRespVO.class));
    }

    /**
     * 获得使用手册详情
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @Operation(summary = "获得使用手册详情")
//    @PreAuthorize("@ss.hasPermission('charging:service-manual:query')")
    public CommonResult<AppServiceManualRespVO> list(@RequestParam("id") Long id) {
        ServiceManualDO detail = serviceManualService.getServiceManual(id);
        return success(BeanUtils.toBean(detail, AppServiceManualRespVO.class));
    }

}
