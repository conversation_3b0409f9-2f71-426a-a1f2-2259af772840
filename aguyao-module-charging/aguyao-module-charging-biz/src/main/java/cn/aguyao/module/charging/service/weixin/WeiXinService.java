package cn.aguyao.module.charging.service.weixin;

import cn.aguyao.module.charging.controller.app.auth.vo.AppAuthLoginRespVO;
import cn.aguyao.module.charging.controller.app.weixin.dto.SessionKeyDTO;
import cn.aguyao.module.charging.controller.app.weixin.vo.AppPhoneNumberReqVO;
import cn.aguyao.module.charging.controller.app.weixin.vo.AppUserInfoReqVO;

/**
 * 处理微信相关业务
 */
public interface WeiXinService {

    /**
     * 小程序授权登录，获取mp_user中的用户id
     * @param openid
     * @param unionid
     * @param sessionKey
     * @return
     */
    Long createUser(String openid, String unionid, String sessionKey);

    /**
     * 微信授权返回token
     *
     * @param code
     * @return
     */
    AppAuthLoginRespVO createToken(String code);


    ////////////////////// 调小程序官网接口 ////////////////////////////

    /**
     * 小程序登录凭证校验
     *
     * @param jsCode 登录时获取的 code
     * @return
     */
    public SessionKeyDTO jscode2session(String jsCode) throws Exception;

    /**
     * 小程序登录凭证校验
     *
     * @param appid  小程序 appId
     * @param secret 小程序 appSecret
     * @param jsCode 登录时获取的 code
     * @return
     */
    public SessionKeyDTO jscode2session(String appid, String secret, String jsCode) throws Exception;

    /**
     * 校验sessionKey是否有效
     * @param openid
     * @param sessionKey
     * @return 返回是否有效
     */
    public Boolean checkSessionKey(String openid, String sessionKey);

    /**
     * 重置用户sessionKey, 重置指定的登录态 session_key
     * @return 返回是否重置成功
     */
    public String resetUserSessionKey(String openid, String sessionKey);


    String getAccessToken();

    /**
     * 获取小程序全局唯一后台接口调用凭据，token有效期为7200s，开发者需要进行妥善保存
     *
     * @return 返回token
     */
    public String getAccessToken(String appid, String secret);

    /**
     * 刷新token
     * @param appid
     * @param secret
     * @return 返回最新的token
     */
    public String refreshAccessToken(String appid, String secret);



//    /**
//     * 获取用户手机号
//     * @param code
//     * @param openid
//     * @param accessToken
//     * @return
//     */
//    public String getPhoneNumber(String code, String openid, String accessToken );



    /**
     * 获取用户手机号
     * @param reqVO
     * @return
     */
    public String getPhoneNumber(AppPhoneNumberReqVO reqVO);

    /**
     * 获取用户信息
     * @param reqVO
     * @return
     */
    public String getUserInfo(AppUserInfoReqVO reqVO);

    /**
     * 创建用户，如果不存在则创建
     * @param openid
     * @param unionid
     * @param sessionKey
     * @return
     */
    Long createUserIfAbsent(String openid, String unionid, String sessionKey);
}

