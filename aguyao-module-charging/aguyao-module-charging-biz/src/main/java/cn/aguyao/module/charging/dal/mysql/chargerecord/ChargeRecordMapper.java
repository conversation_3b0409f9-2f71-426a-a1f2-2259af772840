package cn.aguyao.module.charging.dal.mysql.chargerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordRespVO;
import cn.aguyao.module.charging.controller.app.chargerecord.vo.AppChargeRecordRespVO;
import cn.aguyao.module.charging.controller.mgr.chargerecord.vo.MgrChargeRecordRespVO;
import cn.aguyao.module.charging.dal.dataobject.chargerecord.ChargeRecordDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 充电记录，消费记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ChargeRecordMapper extends BaseMapperX<ChargeRecordDO> {

    default PageResult<ChargeRecordDO> selectPage(ChargeRecordPageReqVO reqVO, List<Long> list) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ChargeRecordDO>()
                .likeIfPresent(ChargeRecordDO::getCode, reqVO.getCode())
                .likeIfPresent(ChargeRecordDO::getMbeCode, reqVO.getMbeCode())
                .likeIfPresent(ChargeRecordDO::getMobile, reqVO.getMobile())
                .betweenIfPresent(ChargeRecordDO::getChargeTimeStart, reqVO.getChargeTimeStart())
                .inIfPresent(ChargeRecordDO::getChargeCommunityId, list)
                .eqIfPresent(ChargeRecordDO::getBuildingsId, reqVO.getBuildingsId())
                .likeIfPresent(ChargeRecordDO::getDevice, reqVO.getDevice())
                .eqIfPresent(ChargeRecordDO::getAmount, reqVO.getAmount())
                .inIfPresent(ChargeRecordDO::getPaySource, reqVO.getPaySource())
                .inIfPresent(ChargeRecordDO::getProceedStatus, reqVO.getProceedStatus())
                .eqIfPresent(ChargeRecordDO::getWattage, reqVO.getWattage())
                .eqIfPresent(ChargeRecordDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ChargeRecordDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ChargeRecordDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(ChargeRecordDO::getMbeId, reqVO.getMbeId())
                .eqIfPresent(ChargeRecordDO::getMpId, reqVO.getMpId())
                .eqIfPresent(ChargeRecordDO::getType, reqVO.getType())
                .orderByDesc(ChargeRecordDO::getId));
    }

    /**
     * 充电中的记录
     * @param device
     * @param port
     * @return
     */
    @Select("select * from charging_charge_record where device = #{device} and port = #{port} " +
            "and proceed_status = #{proceedStatus} order by charge_time_start desc limit 1")
    ChargeRecordDO findByDeviceAndPort2(@Param("device") String device, @Param("port") Integer port,
                                       @Param("proceedStatus") Integer proceedStatus);

    /**
     * 充电中的记录
     * @param device
     * @param port
     * @return
     */
    @Select("select * from charging_charge_record where device = #{device} and port = #{port} " +
            "and proceed_status = #{proceedStatus} and charge_time_start >= #{chargeTimeStart} order by charge_time_start desc limit 1")
    ChargeRecordDO findByDeviceAndPort(@Param("device") String device, @Param("port") Integer port,
                                       @Param("proceedStatus") Integer proceedStatus,
                                       @Param("chargeTimeStart") LocalDateTime chargeTimeStart);

    @Select("select * from charging_charge_record where mp_id = #{mpId} order by charge_time_start desc limit 1")
    ChargeRecordDO lastOneByMpId(Long mpId);

    default ChargeRecordDO findByCode(String merchantOrderId) {
        return selectOne(new LambdaQueryWrapperX<ChargeRecordDO>()
                .eq(ChargeRecordDO::getCode, merchantOrderId));
    }

    default ChargeRecordDO selectByRecordCode(String recordCode) {
        return selectOne(new LambdaQueryWrapperX<ChargeRecordDO>()
                .eq(ChargeRecordDO::getCode, recordCode));
    }

    default ChargeRecordDO findByMpIdAndStatus(Long mpId, int type, Integer[] paySource, int proceedStatus) {
        return selectOne(new LambdaQueryWrapperX<ChargeRecordDO>()
                .eq(ChargeRecordDO::getMpId, mpId)
                .eq(ChargeRecordDO::getType, type)
                .inIfPresent(ChargeRecordDO::getPaySource, paySource)
                .eq(ChargeRecordDO::getProceedStatus, proceedStatus));
    }

    @Select("<script>" +
            " select t1.id, t1.code, t1.mbe_id mbeId, t1.mbe_code mbeCode, t1.mobile,  t1.power,  t1.actual_amount actualAmount," +
            " t1.charge_time_start chargeTimeStart, t1.charge_time_end chargeTimeEnd, t1.actual_charge_time actualChargeTime, t1.share_benefit shareBenefit," +
            " t3.name communityName,  t3.profit_sharing_points, t1.wattage, t1.proceed_status proceedStatus, t2.name buildingName, " +
            " t1.kilowatt, t1.discount_charge_time discountChargeTime, t1.pay_source paySource, t1.service_amt serviceAmt " +
            " from charging_charge_record t1 " +
            " left join charging_building t2 on t2.id = t1.buildings_id and t2.deleted = 0" +
            " left join charging_community t3 on t3.id = t1.charge_community_id and t3.deleted = 0" +
            " left join charging_partner_community t4 on t4.community_id = t3.id and t4.deleted = 0" +
            " where 1=1 and t1.deleted = 0 and t1.pay_source in (3,4) and t1.proceed_status in (1,2) " +
            " and t4.mp_id = #{reqVO.mpId}  and t1.create_time &gt;= #{reqVO.createTime[0]} and t1.create_time &lt;= #{reqVO.createTime[1]} " +
            "   <if test='reqVO.mobile != null and reqVO.mobile.trim() != \"\"'> " +
            "     AND t1.mobile LIKE CONCAT('%', #{reqVO.mobile}, '%') " +
            "   </if> " +
            "   <if test='reqVO.chargeCommunityId != null'> " +
            "     AND t1.charge_community_id = #{reqVO.chargeCommunityId}" +
            "   </if> " +
            " order by t1.create_time desc " +
            "</script>")
    IPage<MgrChargeRecordRespVO> getChargeRecordPage4Mgr(IPage iPage, @Param("reqVO") ChargeRecordPageReqVO reqVO);

    @Select("<script>" +
            " select count(distinct mbe_id) from charging_charge_record where charge_community_id = #{communityId} " +
            "  <if test='processStatus != null and processStatus.length > 0'>" +
            "    AND proceed_status IN " +
            "    <foreach item='status' collection='processStatus' open='(' separator=',' close=')'>" +
            "      #{status}" +
            "    </foreach>" +
            "  </if>" +
            "</script>")
    Long getUseNumByCommunityId(@Param("communityId") Long communityId, @Param("processStatus") Integer[] processStatus);

    default ChargeRecordDO findByMpIdAndDeviceInfo(Long mpId, int type, Integer[] paySource, int proceedStatus, String device, Integer port) {
        return selectOne(new LambdaQueryWrapperX<ChargeRecordDO>()
                .eq(ChargeRecordDO::getMpId, mpId)
                .eq(ChargeRecordDO::getType, type)
                .inIfPresent(ChargeRecordDO::getPaySource, paySource)
                .eq(ChargeRecordDO::getProceedStatus, proceedStatus)
                .eq(ChargeRecordDO::getDevice, device)
                .eq(ChargeRecordDO::getPort, port)
        );
    }

    @Select("<script>" +
            " select t1.id, t1.code, t1.mbe_id mbeId, t1.mbe_code mbeCode, t1.mobile,  t1.power,  t1.actual_amount actualAmount," +
            " t1.charge_time_start chargeTimeStart, t1.charge_time_end chargeTimeEnd, t1.actual_charge_time actualChargeTime, " +
            " t1.share_benefit shareBenefit, t1.kilowatt, t1.discount_charge_time discountChargeTime, t1.pay_source paySource, t1.device, " +
            " t3.name communityName, t3.profit_sharing_points, t1.wattage, t1.proceed_status proceedStatus, t2.name buildingName, " +
            " t1.service_amt serviceAmt" +
            " from charging_charge_record t1 " +
            " left join charging_building t2 on t2.id = t1.buildings_id and t2.deleted = 0" +
            " left join charging_community t3 on t3.id = t1.charge_community_id and t3.deleted = 0" +
            " where 1=1 and t1.deleted = 0 and t1.proceed_status = #{reqVO.proceedStatus[0]}" +
            " and t1.mp_id = #{reqVO.mpId} " +
            " AND t1.pay_source IN " +
            "   <foreach item='paySource' collection='reqVO.paySource' open='(' separator=',' close=')'>" +
            "      #{paySource}" +
            "   </foreach>" +
            " and t1.create_time &gt;= #{reqVO.createTime[0]} and t1.create_time &lt;= #{reqVO.createTime[1]} " +
            " order by t1.create_time desc" +
            "</script>")
    IPage<AppChargeRecordRespVO> getChargeRecordPage4App(IPage iPage, @Param("reqVO") ChargeRecordPageReqVO reqVO);

    List<ChargeRecordRespVO> getChargeRecord4Export(@Param("reqVO") ChargeRecordPageReqVO reqVO);
}
