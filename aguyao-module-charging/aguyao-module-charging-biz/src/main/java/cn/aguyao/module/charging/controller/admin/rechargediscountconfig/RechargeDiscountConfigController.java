package cn.aguyao.module.charging.controller.admin.rechargediscountconfig;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.rechargediscountconfig.vo.RechargeDiscountConfigPageReqVO;
import cn.aguyao.module.charging.controller.admin.rechargediscountconfig.vo.RechargeDiscountConfigRespVO;
import cn.aguyao.module.charging.controller.admin.rechargediscountconfig.vo.RechargeDiscountConfigSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.rechargediscountconfig.RechargeDiscountConfigDO;
import cn.aguyao.module.charging.service.rechargediscountconfig.RechargeDiscountConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 充值优惠配置")
@RestController
@RequestMapping("/charging/recharge-discount-config")
@Validated
public class RechargeDiscountConfigController {

    @Resource
    private RechargeDiscountConfigService rechargeDiscountConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建充值优惠配置")
    @PreAuthorize("@ss.hasPermission('charging:recharge-discount-config:create')")
    public CommonResult<Long> createRechargeDiscountConfig(@Valid @RequestBody RechargeDiscountConfigSaveReqVO createReqVO) {
        return success(rechargeDiscountConfigService.createRechargeDiscountConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新充值优惠配置")
    @PreAuthorize("@ss.hasPermission('charging:recharge-discount-config:update')")
    public CommonResult<Boolean> updateRechargeDiscountConfig(@Valid @RequestBody RechargeDiscountConfigSaveReqVO updateReqVO) {
        rechargeDiscountConfigService.updateRechargeDiscountConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除充值优惠配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:recharge-discount-config:delete')")
    public CommonResult<Boolean> deleteRechargeDiscountConfig(@RequestParam("id") Long id) {
        rechargeDiscountConfigService.deleteRechargeDiscountConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得充值优惠配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:recharge-discount-config:query')")
    public CommonResult<RechargeDiscountConfigRespVO> getRechargeDiscountConfig(@RequestParam("id") Long id) {
        RechargeDiscountConfigDO rechargeDiscountConfig = rechargeDiscountConfigService.getRechargeDiscountConfig(id);
        return success(BeanUtils.toBean(rechargeDiscountConfig, RechargeDiscountConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得充值优惠配置分页")
    @PreAuthorize("@ss.hasPermission('charging:recharge-discount-config:query')")
    public CommonResult<PageResult<RechargeDiscountConfigRespVO>> getRechargeDiscountConfigPage(@Valid RechargeDiscountConfigPageReqVO pageReqVO) {
        PageResult<RechargeDiscountConfigDO> pageResult = rechargeDiscountConfigService.getRechargeDiscountConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RechargeDiscountConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出充值优惠配置 Excel")
    @PreAuthorize("@ss.hasPermission('charging:recharge-discount-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRechargeDiscountConfigExcel(@Valid RechargeDiscountConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RechargeDiscountConfigDO> list = rechargeDiscountConfigService.getRechargeDiscountConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "充值优惠配置.xls", "数据", RechargeDiscountConfigRespVO.class,
                        BeanUtils.toBean(list, RechargeDiscountConfigRespVO.class));
    }

}