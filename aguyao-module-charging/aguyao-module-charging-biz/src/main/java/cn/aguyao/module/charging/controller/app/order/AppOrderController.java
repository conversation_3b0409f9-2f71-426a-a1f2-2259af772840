package cn.aguyao.module.charging.controller.app.order;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.module.charging.controller.app.order.vo.AppRechargeRespVO;
import cn.aguyao.module.charging.controller.app.order.vo.AppSingleChargeReqVO;
import cn.aguyao.module.charging.controller.app.order.vo.AppTempChargeReqVO;
import cn.aguyao.module.charging.controller.app.order.vo.AppTempChargeRespVO;
import cn.aguyao.module.charging.service.order.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序端 - 订单")
@RestController
@RequestMapping("/charging/order")
@Validated
public class AppOrderController {

    @Resource
    private OrderService orderService;

    /**
     * 单次充电
     * 1、判断是否包月
     * 2、判断余额是否充足
     * 3、打开端口开始充电
     * @param reqVO
     * @return
     */
    @PostMapping("/single-charge")
    @Operation(summary = "开始充电")
    @Parameter(name = "reqVO", description = "单次充电", required = true, example = "1024")
    public CommonResult<String> singleCharge(@RequestBody AppSingleChargeReqVO reqVO) throws Exception {
        CommonResult<String> result = orderService.singleCharge(reqVO);
        return result;
    }

    /**
     * 临时充电
     * 1、生成业务订单
     * 2、判断余额是否充足
     * 3、打开端口开始充电
     * @param reqVO
     * @return
     */
    @PostMapping("/temp-charge")
    @Operation(summary = "临时充电")
    @Parameter(name = "reqVO", description = "临时充电", required = true, example = "1024")
    public CommonResult<AppTempChargeRespVO> tempCharge(@RequestBody AppTempChargeReqVO reqVO) throws Exception {
        AppTempChargeRespVO respVO = orderService.tempCharge(reqVO);
        return success(respVO);
    }

    /**
     * 2.2、余额充值
     * @return
     */
    @PostMapping("/balance/recharge")
    @Operation(summary = "余额充值")
    public CommonResult<AppRechargeRespVO> balanceRecharge(@RequestParam("proId") Long proId) throws Exception {
        AppRechargeRespVO result = orderService.balanceRecharge(proId);
        return CommonResult.success(result);
    }

    /**
     * 2.4、包月充值
     * @return
     */
    @PostMapping("/monthly/recharge")
    @Operation(summary = "包月充值")
    public CommonResult<AppRechargeRespVO> monthlyRecharge(@RequestParam("proId") Long proId) throws Exception {
        AppRechargeRespVO result = orderService.monthlyRecharge(proId);
        return CommonResult.success(result);
    }


    /**
     * 是否还在充电
     * @return Boolean
     */
    @GetMapping("/is-charging")
    @Operation(summary = "是否还在充电")
    public CommonResult<Boolean> isCharging(@RequestParam("id") Long orderId) throws Exception {
        Boolean result = orderService.isCharging(orderId);
        return CommonResult.success(result);
    }
}
