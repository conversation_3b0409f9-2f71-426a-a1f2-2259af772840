package cn.aguyao.module.charging.controller.admin.community.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 小区 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CommunityBasicInfoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1376")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "编号")
    @ExcelProperty("编号")
    private String code;

    @Schema(description = "小区名称", example = "赵六")
    @ExcelProperty("小区名称")
    private String name;
}
