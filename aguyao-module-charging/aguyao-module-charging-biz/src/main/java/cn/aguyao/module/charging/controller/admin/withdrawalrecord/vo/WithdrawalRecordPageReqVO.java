package cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 提现记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WithdrawalRecordPageReqVO extends PageParam {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "")
    private Long mpId;

    @Schema(description = "主管id")
    private Long partnerId;

    @Schema(description = "申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] applyTime;

    @Schema(description = "申请金额")
    private BigDecimal applyAmount;

    @Schema(description = "提现银行名称", example = "芋艿")
    private String bankName;

    @Schema(description = "银行卡号")
    private String bankCardNum;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "申请状态", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


    @Schema(description = "主管编号")
    private String partnerCode;

    @Schema(description = "主管名称")
    private String partnerName;

    @Schema(description = "主管手机号")
    private String partnerMobile;

}
