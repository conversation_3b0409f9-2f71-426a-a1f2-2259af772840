package cn.aguyao.module.charging.controller.app.refundrecord;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.refundrecord.vo.RefundRecordPageReqVO;
import cn.aguyao.module.charging.controller.app.refundrecord.vo.AppRefundRecordRespVO;
import cn.aguyao.module.charging.dal.dataobject.refundrecord.RefundRecordDO;
import cn.aguyao.module.charging.service.refundrecord.RefundRecordService;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 退款记录")
@RestController
@RequestMapping("/charging/refund-record")
@Validated
public class AppRefundRecordController {

    @Resource
    private RefundRecordService refundRecordService;


    @GetMapping("/page")
    @Operation(summary = "获得退款记录分页")
    public CommonResult<PageResult<AppRefundRecordRespVO>> getRefundRecordPage(@RequestParam("type") Integer type,
                                                                               @RequestParam("startTime") String startTime,
                                                                               @RequestParam("endTime") String endTime) {

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        RefundRecordPageReqVO pageReqVO = new RefundRecordPageReqVO();
//        pageReqVO.setType(type);
        pageReqVO.setMpId(mpId);

        LocalDateTime[] createTime = new LocalDateTime[2];
        if (StrUtil.isNotBlank(startTime)) {
            LocalDateTime st = DateUtils.dateStr2LocalDateTimeStart(startTime);
            createTime[0] = st;
        }
        if (StrUtil.isNotBlank(endTime)) {
            LocalDateTime et = DateUtils.dateStr2LocalDateTimeEnd(endTime);
            createTime[1] = et;
        }
        pageReqVO.setCreateTime(createTime);
        PageResult<RefundRecordDO> pageResult = refundRecordService.getRefundRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppRefundRecordRespVO.class));
    }

}
