package cn.aguyao.module.charging.controller.admin.schemetime.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 收费方案—时段电费新增/修改 Request VO")
@Data
public class SchemeTimeSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10606")
    private Long id;

    @Schema(description = "方案id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26113")
    @NotNull(message = "方案id不能为空")
    private Long schemeId;

    @Schema(description = "序号")
    private Integer sort;

    @Schema(description = "开始时段，左侧（包含）")
    private String startTime;

    @Schema(description = "结束时段，右侧（不含）")
    private String endTime;

    @Schema(description = "电费 energy price（单位：元）", example = "25235")
    private BigDecimal eprice;

    @Schema(description = "服务费 service price（单位：元）", example = "10524")
    private BigDecimal sprice;

    @Schema(description = "备注", example = "随便")
    private String remark;

}