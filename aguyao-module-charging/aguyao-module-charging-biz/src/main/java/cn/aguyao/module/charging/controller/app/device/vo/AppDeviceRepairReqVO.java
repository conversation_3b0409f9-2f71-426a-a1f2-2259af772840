package cn.aguyao.module.charging.controller.app.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "故障报修 - 设备新增/修改 Request VO")
@Data
public class AppDeviceRepairReqVO {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "故障类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String faultType;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}
