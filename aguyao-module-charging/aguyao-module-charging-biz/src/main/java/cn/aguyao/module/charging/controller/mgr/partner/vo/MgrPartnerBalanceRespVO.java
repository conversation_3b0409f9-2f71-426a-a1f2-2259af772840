package cn.aguyao.module.charging.controller.mgr.partner.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 合作伙伴——小区管理人员（物业） Response VO")
@Data
@ExcelIgnoreUnannotated
public class MgrPartnerBalanceRespVO {

    @Schema(description = "可提现金额")
    private BigDecimal canWithdraw;

    @Schema(description = "已提现金额")
    private BigDecimal withdrawn;

    @Schema(description = "银行卡")
    private String cardNo;

    @Schema(description = "开户行")
    private String bankDeposit;


}
