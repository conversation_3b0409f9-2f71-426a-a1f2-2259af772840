package cn.aguyao.module.charging.service.monthlypkgrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo.MonthlyPkgRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.monthlypkgrecord.vo.MonthlyPkgRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgrecord.MonthlyPkgRecordDO;
import cn.aguyao.module.pay.api.order.dto.PayOrderNotifyRespDTO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 包月记录 Service 接口
 *
 * <AUTHOR>
 */
public interface MonthlyPkgRecordService {

    /**
     * 创建包月记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMonthlyPkgRecord(@Valid MonthlyPkgRecordSaveReqVO createReqVO);

    /**
     * 创建包月记录
     *
     * @param monthlyPkgRecord 创建信息
     * @return 编号
     */
    Long createMonthlyPkgRecord(@NotNull MonthlyPkgRecordDO monthlyPkgRecord);

    /**
     * 更新包月记录
     *
     * @param updateReqVO 更新信息
     */
    void updateMonthlyPkgRecord(@Valid MonthlyPkgRecordSaveReqVO updateReqVO);

    /**
     * 删除包月记录
     *
     * @param id 编号
     */
    void deleteMonthlyPkgRecord(Long id);

    /**
     * 获得包月记录
     *
     * @param id 编号
     * @return 包月记录
     */
    MonthlyPkgRecordDO getMonthlyPkgRecord(Long id);

    /**
     * 获得包月记录分页
     *
     * @param pageReqVO 分页查询
     * @return 包月记录分页
     */
    PageResult<MonthlyPkgRecordDO> getMonthlyPkgRecordPage(MonthlyPkgRecordPageReqVO pageReqVO);

    /**
     * 根据会员id，获取用户的包月情况
     * @param memberId
     * @return
     */
    MonthlyPkgRecordDO getMonthlyPkgRecordByMbeId(Long memberId);

    /**
     * 根据mpId查询包月记录，有效期内的第一条记录
     * @param mpId
     * @return
     */
    MonthlyPkgRecordDO findOneByMpId(Long mpId);

    void notifyHandle(PayOrderNotifyRespDTO respDTO);

    MonthlyPkgRecordDO findByCode(String merchantOrderId);
}
