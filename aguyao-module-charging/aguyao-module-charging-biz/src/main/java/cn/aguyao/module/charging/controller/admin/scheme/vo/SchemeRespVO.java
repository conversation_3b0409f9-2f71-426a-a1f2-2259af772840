package cn.aguyao.module.charging.controller.admin.scheme.vo;

import cn.aguyao.module.charging.controller.admin.schemecollect.vo.SchemeCollectSaveReqVO;
import cn.aguyao.module.charging.controller.admin.schemepower.vo.SchemePowerSaveReqVO;
import cn.aguyao.module.charging.controller.admin.schemetime.vo.SchemeTimeSaveReqVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 收费方案 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SchemeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "22942")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "方案名称", example = "芋艿")
    @ExcelProperty("方案名称")
    private String name;

    @Schema(description = "方案描述")
    @ExcelProperty("方案描述")
    private String description;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    @Schema(description = "计费方式", example = "1")
    private Integer type;

    @Schema(description = "按时间—功率档位", example = "按时间—功率档位")
    private List<SchemePowerSaveReqVO> powerItems;

    @Schema(description = "按时间—收费套餐", example = "按时间—收费套餐")
    private List<SchemeCollectSaveReqVO> setmealItems1;

    @Schema(description = "按电量—时段", example = "按电量—时段")
    private List<SchemeTimeSaveReqVO> timeItems;

    @Schema(description = "按电量—收费套餐", example = "按电量—收费套餐")
    private List<SchemeCollectSaveReqVO> setmealItems2;


    ///////////按电量2.0/////////////
    @Schema(description = "按电量—时段", example = "按电量—时段")
    private List<SchemeTimeSaveReqVO> timeItems3;

    private List<SchemePowerSaveReqVO> powerItems3;

    @Schema(description = "按时间—收费套餐", example = "按时间—收费套餐")
    private List<SchemeCollectSaveReqVO> setmealItems3;
}
