package cn.aguyao.module.charging.controller.admin.scheme;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.scheme.vo.SchemePageReqVO;
import cn.aguyao.module.charging.controller.admin.scheme.vo.SchemeRespVO;
import cn.aguyao.module.charging.controller.admin.scheme.vo.SchemeSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.scheme.SchemeDO;
import cn.aguyao.module.charging.service.scheme.SchemeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 收费方案")
@RestController
@RequestMapping("/charging/scheme")
@Validated
public class SchemeController {

    @Resource
    private SchemeService schemeService;

    @PostMapping("/create")
    @Operation(summary = "创建收费方案")
    @PreAuthorize("@ss.hasPermission('charging:scheme:create')")
    public CommonResult<Long> createScheme(@Valid @RequestBody SchemeSaveReqVO createReqVO) {
        return success(schemeService.createScheme(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新收费方案")
    @PreAuthorize("@ss.hasPermission('charging:scheme:update')")
    public CommonResult<Boolean> updateScheme(@Valid @RequestBody SchemeSaveReqVO updateReqVO) {
        schemeService.updateScheme(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除收费方案")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:scheme:delete')")
    public CommonResult<Boolean> deleteScheme(@RequestParam("id") Long id) {
        schemeService.deleteScheme(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得收费方案")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:scheme:query')")
    public CommonResult<SchemeRespVO> getScheme(@RequestParam("id") Long id) {
        SchemeRespVO scheme = schemeService.getSchemeWithDetail(id);
        return success(scheme);
    }

    @GetMapping("/page")
    @Operation(summary = "获得收费方案分页")
    @PreAuthorize("@ss.hasPermission('charging:scheme:query')")
    public CommonResult<PageResult<SchemeRespVO>> getSchemePage(@Valid SchemePageReqVO pageReqVO) {
        PageResult<SchemeDO> pageResult = schemeService.getSchemePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SchemeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出收费方案 Excel")
    @PreAuthorize("@ss.hasPermission('charging:scheme:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSchemeExcel(@Valid SchemePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SchemeDO> list = schemeService.getSchemePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "收费方案.xls", "数据", SchemeRespVO.class,
                        BeanUtils.toBean(list, SchemeRespVO.class));
    }

}
