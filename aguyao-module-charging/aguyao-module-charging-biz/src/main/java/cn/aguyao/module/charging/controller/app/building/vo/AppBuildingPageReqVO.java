package cn.aguyao.module.charging.controller.app.building.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "小程序端 - 楼栋分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppBuildingPageReqVO extends PageParam {

    @Schema(description = "编号")
    private String code;

    @Schema(description = "楼栋名称", example = "王五")
    private String name;

    @Schema(description = "小区名称", example = "")
    @ExcelProperty("小区名称")
    private String communityName;

    @Schema(description = "设备数")
    private Integer deviceNum;

    @Schema(description = "充电中的设备数")
    private Integer chargingDeviceNum;

    @Schema(description = "待机设备数")
    private Integer standbyDeviceNum;

    @Schema(description = "断网设备数")
    private Integer networkDisconnDeviceNum;

    @Schema(description = "费率")
    private String rate;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
