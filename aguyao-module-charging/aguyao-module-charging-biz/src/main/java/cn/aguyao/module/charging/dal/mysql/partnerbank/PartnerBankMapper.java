package cn.aguyao.module.charging.dal.mysql.partnerbank;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.partnerbank.vo.PartnerBankPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.partnerbank.PartnerBankDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 伙伴银行 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PartnerBankMapper extends BaseMapperX<PartnerBankDO> {

    default PageResult<PartnerBankDO> selectPage(PartnerBankPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PartnerBankDO>()
                .eqIfPresent(PartnerBankDO::getPartnerId, reqVO.getPartnerId())
                .eqIfPresent(PartnerBankDO::getCardNo, reqVO.getCardNo())
                .eqIfPresent(PartnerBankDO::getBankDeposit, reqVO.getBankDeposit())
                .likeIfPresent(PartnerBankDO::getBankName, reqVO.getBankName())
                .betweenIfPresent(PartnerBankDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PartnerBankDO::getId));
    }

    IPage<PartnerBankDO> findPage(IPage<PartnerBankDO> iPage, @Param("reqVO") PartnerBankPageReqVO reqVO);

    default PartnerBankDO getPartnerBankByPartnerId(Long partnerId) {
        return selectOne(new LambdaQueryWrapperX<PartnerBankDO>()
                .eq(PartnerBankDO::getPartnerId, partnerId)
                .eq(PartnerBankDO::getDeleted, false)
        );
    }

    default int deleteByPartnerId(Long partnerId) {
        return update(new LambdaUpdateWrapper<PartnerBankDO>()
                .eq(PartnerBankDO::getPartnerId, partnerId)
                .ne(PartnerBankDO::getDeleted, true)
                .set(PartnerBankDO::getDeleted, true)
        );
    }
}
