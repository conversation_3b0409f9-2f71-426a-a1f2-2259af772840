package cn.aguyao.module.charging.service.schemetime;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.schemetime.vo.SchemeTimePageReqVO;
import cn.aguyao.module.charging.controller.admin.schemetime.vo.SchemeTimeSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.schemetime.SchemeTimeDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 收费方案—时段电费 Service 接口
 *
 * <AUTHOR>
 */
public interface SchemeTimeService {

    /**
     * 创建收费方案—时段电费
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSchemeTime(@Valid SchemeTimeSaveReqVO createReqVO);

    /**
     * 创建收费方案—时段电费
     *
     * @param list 创建信息
     * @param schemeId 方案id
     * @return 编号
     */
    void createSchemeTime(List<SchemeTimeSaveReqVO> list, Long schemeId);

    /**
     * 更新收费方案—时段电费
     *
     * @param updateReqVO 更新信息
     */
    void updateSchemeTime(@Valid SchemeTimeSaveReqVO updateReqVO);

    /**
     * 更新收费方案—时段电费
     *
     * @param list 更新信息
     * @param schemeId 方案id
     */
    void updateSchemeTime(List<SchemeTimeSaveReqVO> list, Long schemeId);

    /**
     * 删除收费方案—时段电费
     *
     * @param id 编号
     */
    void deleteSchemeTime(Long id);

    /**
     * 获得收费方案—时段电费
     *
     * @param id 编号
     * @return 收费方案—时段电费
     */
    SchemeTimeDO getSchemeTime(Long id);

    /**
     * 获得收费方案—时段电费分页
     *
     * @param pageReqVO 分页查询
     * @return 收费方案—时段电费分页
     */
    PageResult<SchemeTimeDO> getSchemeTimePage(SchemeTimePageReqVO pageReqVO);

    /**
     * 根据方案id查询时段电费
     * @param schemeId
     * @return
     */
    List<SchemeTimeDO> findBySchemeId(Long schemeId);
}
