package cn.aguyao.module.charging.service.withdrawalrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo.WithdrawalRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.withdrawalrecord.vo.WithdrawalRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.withdrawalrecord.WithdrawalRecordDO;

import javax.validation.Valid;

/**
 * 提现记录 Service 接口
 *
 * <AUTHOR>
 */
public interface WithdrawalRecordService {

    /**
     * 创建提现记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWithdrawalRecord(@Valid WithdrawalRecordSaveReqVO createReqVO);

    /**
     * 更新提现记录
     *
     * @param updateReqVO 更新信息
     */
    void updateWithdrawalRecord(@Valid WithdrawalRecordSaveReqVO updateReqVO);

    /**
     * 删除提现记录
     *
     * @param id 编号
     */
    void deleteWithdrawalRecord(Long id);

    /**
     * 获得提现记录
     *
     * @param id 编号
     * @return 提现记录
     */
    WithdrawalRecordDO getWithdrawalRecord(Long id);

    /**
     * 获得提现记录分页
     *
     * @param pageReqVO 分页查询
     * @return 提现记录分页
     */
    PageResult<WithdrawalRecordDO> getWithdrawalRecordPage(WithdrawalRecordPageReqVO pageReqVO);

}