package cn.aguyao.module.charging.controller.admin.rechargediscountconfig.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 充值优惠配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RechargeDiscountConfigPageReqVO extends PageParam {

    @Schema(description = "优惠编号")
    private String code;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "赠送金额")
    private BigDecimal giftAmount;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}