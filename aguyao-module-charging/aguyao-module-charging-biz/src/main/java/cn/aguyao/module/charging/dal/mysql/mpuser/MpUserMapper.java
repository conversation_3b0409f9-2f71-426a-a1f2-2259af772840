package cn.aguyao.module.charging.dal.mysql.mpuser;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.mpuser.vo.MpUserPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.user.MpUserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 微信小程序粉丝 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MpUserMapper extends BaseMapperX<MpUserDO> {

    default PageResult<MpUserDO> selectPage(MpUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MpUserDO>()
                .eqIfPresent(MpUserDO::getAppId, reqVO.getAppId())
                .eqIfPresent(MpUserDO::getOpenid, reqVO.getOpenid())
                .eqIfPresent(MpUserDO::getUnionId, reqVO.getUnionId())
                .eqIfPresent(MpUserDO::getMobile, reqVO.getMobile())
                .likeIfPresent(MpUserDO::getNickname, reqVO.getNickname())
                .eqIfPresent(MpUserDO::getHeadImageUrl, reqVO.getHeadImageUrl())
                .eqIfPresent(MpUserDO::getLanguage, reqVO.getLanguage())
                .eqIfPresent(MpUserDO::getCountry, reqVO.getCountry())
                .eqIfPresent(MpUserDO::getProvince, reqVO.getProvince())
                .eqIfPresent(MpUserDO::getCity, reqVO.getCity())
                .eqIfPresent(MpUserDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MpUserDO::getSubscribeStatus, reqVO.getSubscribeStatus())
                .betweenIfPresent(MpUserDO::getSubscribeTime, reqVO.getSubscribeTime())
                .betweenIfPresent(MpUserDO::getUnsubscribeTime, reqVO.getUnsubscribeTime())
                .betweenIfPresent(MpUserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MpUserDO::getId));
    }

}
