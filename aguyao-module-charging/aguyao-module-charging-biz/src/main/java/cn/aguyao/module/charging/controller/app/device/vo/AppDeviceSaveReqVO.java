package cn.aguyao.module.charging.controller.app.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 设备新增/修改 Request VO")
@Data
public class AppDeviceSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10825")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "联网状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "联网状态不能为空")
    private Integer networkStatus;

    @Schema(description = "运行状态", example = "1")
    private Integer runningStatus;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}
