package cn.aguyao.module.charging.controller.mgr.partnerbank.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理端小程序 - 伙伴银行 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MgrPartnerBankRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16954")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "合作伙伴id", example = "22868")
    @ExcelProperty("合作伙伴id")
    private Long partnerId;

    @Schema(description = "银行卡号")
    @ExcelProperty("银行卡号")
    private String cardNo;

    @Schema(description = "开户行")
    @ExcelProperty("开户行")
    private String bankDeposit;

    @Schema(description = "银行名称", example = "张三")
    @ExcelProperty("银行名称")
    private String bankName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
