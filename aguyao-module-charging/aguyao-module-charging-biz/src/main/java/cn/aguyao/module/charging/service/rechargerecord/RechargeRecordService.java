package cn.aguyao.module.charging.service.rechargerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.module.charging.controller.admin.rechargerecord.vo.RechargeRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.rechargerecord.vo.RechargeRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.rechargerecord.RechargeRecordDO;
import cn.aguyao.module.pay.api.order.dto.PayOrderNotifyRespDTO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 充值记录 Service 接口
 *
 * <AUTHOR>
 */
public interface RechargeRecordService {

    /**
     * 创建充值记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRechargeRecord(@Valid RechargeRecordSaveReqVO createReqVO);

    /**
     * 创建充值记录
     *
     * @param rechargeRecord 创建信息
     * @return 编号
     */
    Long createRechargeRecord(@NotNull RechargeRecordDO rechargeRecord);

    /**
     * 更新充值记录
     *
     * @param updateReqVO 更新信息
     */
    void updateRechargeRecord(@Valid RechargeRecordSaveReqVO updateReqVO);

    /**
     * 删除充值记录
     *
     * @param id 编号
     */
    void deleteRechargeRecord(Long id);

    /**
     * 获得充值记录
     *
     * @param id 编号
     * @return 充值记录
     */
    RechargeRecordDO getRechargeRecord(Long id);

    /**
     * 获得充值记录分页
     *
     * @param pageReqVO 分页查询
     * @return 充值记录分页
     */
    PageResult<RechargeRecordDO> getRechargeRecordPage(RechargeRecordPageReqVO pageReqVO);

    void notifyHandle(PayOrderNotifyRespDTO respDTO);

    RechargeRecordDO findByCode(String code);

    /**
     * 根据商户编号查询充值记录， 有效充值记录
     * @param mpId
     * @return
     */
    List<RechargeRecordDO> findByMpId(Long mpId);
}
