package cn.aguyao.module.charging.dal.dataobject.scheme;

import cn.aguyao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 收费方案 DO
 *
 * <AUTHOR>
 */
@TableName("charging_scheme")
@KeySequence("charging_scheme_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchemeDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 方案名称
     */
    private String name;
    /**
     * 方案描述
     */
    private String description;
    /**
     * 备注
     */
    private String remark;

    /**
     * 计费方式, 1：按时间计费； 2：按电量计费
     */
    private Integer type;
}
