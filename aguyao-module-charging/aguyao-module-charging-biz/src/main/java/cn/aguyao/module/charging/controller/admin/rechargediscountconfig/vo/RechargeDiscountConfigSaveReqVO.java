package cn.aguyao.module.charging.controller.admin.rechargediscountconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 充值优惠配置新增/修改 Request VO")
@Data
public class RechargeDiscountConfigSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12503")
    private Long id;

    @Schema(description = "优惠编号")
    private String code;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "赠送金额")
    private BigDecimal giftAmount;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;


    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sort;
}
