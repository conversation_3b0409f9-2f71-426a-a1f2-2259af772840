package cn.aguyao.module.charging.controller.app.insurerecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 投保记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppInsureRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15208")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "投保人，会员id", example = "21489")
    @ExcelProperty("投保人，会员id")
    private Long mbeId;

    @Schema(description = "投保人，微信id", example = "21489")
    @ExcelProperty("投保人，微信id")
    private Long mpId;

    @Schema(description = "投保配置的id", example = "10939")
    @ExcelProperty("投保配置的id")
    private Long insureConfigId;

    @Schema(description = "充电记录id", example = "21489")
    @ExcelProperty("充电记录id")
    private Long chargeRecordId;

    @Schema(description = "充电记录编码", example = "21489")
    @ExcelProperty("充电记录编码")
    private String chargeRecordCode;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;


    /**
     * 投保费用
     */
    private BigDecimal amount;

    private String mbeCode;

    private String mobile;
}
