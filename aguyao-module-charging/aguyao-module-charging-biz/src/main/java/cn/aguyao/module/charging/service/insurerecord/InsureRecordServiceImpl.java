package cn.aguyao.module.charging.service.insurerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.insurerecord.vo.InsureRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.insurerecord.vo.InsureRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.insurerecord.InsureRecordDO;
import cn.aguyao.module.charging.dal.mysql.insurerecord.InsureRecordMapper;
import cn.aguyao.module.charging.dal.mysql.mpuser.mbe.MbeDO;
import cn.aguyao.module.charging.service.mbe.MbeService;
import org.hibernate.validator.internal.util.stereotypes.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.INSURE_RECORD_NOT_EXISTS;

/**
 * 投保记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InsureRecordServiceImpl implements InsureRecordService {

    @Resource
    private InsureRecordMapper insureRecordMapper;

    @Lazy
    @Resource
    private MbeService mbeService;

    @Override
    public Long createInsureRecord(InsureRecordSaveReqVO createReqVO) {
        // 插入
        InsureRecordDO insureRecord = BeanUtils.toBean(createReqVO, InsureRecordDO.class);
        insureRecordMapper.insert(insureRecord);
        // 返回
        return insureRecord.getId();
    }

    @Override
    public Long createInsureRecord(InsureRecordDO insureRecord) {

        if (insureRecord.getMbeId() == null && insureRecord.getMpId() != null) {
            MbeDO mbe = mbeService.selectMemberByMpUserId(insureRecord.getMpId());
            if (Objects.nonNull(mbe)) {
                // 更新用户
                insureRecord.setMbeId(mbe.getId());
            }
        }
        insureRecordMapper.insert(insureRecord);

        return insureRecord.getId();
    }

    @Override
    public void updateInsureRecord(InsureRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateInsureRecordExists(updateReqVO.getId());
        // 更新
        InsureRecordDO updateObj = BeanUtils.toBean(updateReqVO, InsureRecordDO.class);
        insureRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteInsureRecord(Long id) {
        // 校验存在
        validateInsureRecordExists(id);
        // 删除
        insureRecordMapper.deleteById(id);
    }

    private void validateInsureRecordExists(Long id) {
        if (insureRecordMapper.selectById(id) == null) {
            throw exception(INSURE_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public InsureRecordDO getInsureRecord(Long id) {
        return insureRecordMapper.selectById(id);
    }

    @Override
    public PageResult<InsureRecordDO> getInsureRecordPage(InsureRecordPageReqVO pageReqVO) {
        PageResult<InsureRecordDO> pageResult = insureRecordMapper.selectPage(pageReqVO);
        pageResult.getList().stream().forEach(item -> {
            // 自定义转换
            MbeDO mbe = mbeService.getMember(item.getMbeId());
            if (Objects.nonNull(mbe)) {
                item.setMbeCode(mbe.getCode());
                item.setMobile(mbe.getMobile());
            }
        });
        return pageResult;
    }

}
