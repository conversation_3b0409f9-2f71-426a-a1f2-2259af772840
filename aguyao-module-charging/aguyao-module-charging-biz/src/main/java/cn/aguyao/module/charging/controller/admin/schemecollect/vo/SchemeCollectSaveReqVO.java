package cn.aguyao.module.charging.controller.admin.schemecollect.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 收费方案—收费套餐新增/修改 Request VO")
@Data
public class SchemeCollectSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12603")
    private Long id;

    @Schema(description = "方案id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11194")
    @NotNull(message = "方案id不能为空")
    private Long schemeId;

    @Schema(description = "收费金额")
    private BigDecimal amount;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "套餐类别", example = "1")
    private Integer type;

    @Schema(description = "排序", example = "1")
    private Integer sort;
}
