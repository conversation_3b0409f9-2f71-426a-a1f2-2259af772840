package cn.aguyao.module.charging.controller.mgr.mbe;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.mgr.mbe.vo.MgrCommunitySimpleVO;
import cn.aguyao.module.charging.controller.mgr.mbe.vo.MgrMbeCountRespVO;
import cn.aguyao.module.charging.controller.mgr.mbe.vo.MgrMbeDetailRespVO;
import cn.aguyao.module.charging.controller.mgr.mbe.vo.MgrMbePageReqVO;
import cn.aguyao.module.charging.service.mbe.MbeService;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序_管理端 - 会员，即充电的用户")
@RestController
@RequestMapping("/charging/mbe")
@Validated
public class MgrMbeController {

    @Resource
    private MbeService memberService;


    /**
     * 3.1、用户统计主页
     * @return
     */
    @GetMapping("/info")
    @Operation(summary = "获得用户统计首页")
    public CommonResult<MgrMbeCountRespVO> statisticsInfo(@RequestParam("communityName") String communityName) {
        MgrMbeCountRespVO mbeInfo = memberService.statisticsInfo(communityName);
        return success(mbeInfo);
    }


    /**
     * 3.2、用户统计列表
     * @return
     */
    @GetMapping("/info/page")
    @Operation(summary = "用户详情统计")
    @Parameters({@Parameter(name = "startTime", description = "开始时间"),
            @Parameter(name = "endTime", description = "结束时间"),
            @Parameter(name = "communityId", description = "小区id")})
    public CommonResult<PageResult<MgrCommunitySimpleVO>> statisticsList(@RequestParam("pageNo") Integer pageNo,
                                                                         @RequestParam("pageSize") Integer pageSize,
                                                                         @RequestParam("communityName") String communityName) {

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();

        MgrMbePageReqVO pageReqVO = new MgrMbePageReqVO();
        pageReqVO.setMpId(mpId);
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);
        pageReqVO.setBelongCommunityName(communityName);

        PageResult<MgrCommunitySimpleVO> pageResult = memberService.statisticsList(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MgrCommunitySimpleVO.class));
    }

    /**
     * 3.3、用户详情统计
     * @return
     */
    @GetMapping("/info/detail")
    @Operation(summary = "用户详情统计")
    @Parameters({@Parameter(name = "startTime", description = "开始时间"),
            @Parameter(name = "endTime", description = "结束时间"),
            @Parameter(name = "communityId", description = "小区id")})
    public CommonResult<PageResult<MgrMbeDetailRespVO>> statisticsDetail(@RequestParam("startTime") String startTime,
                                                                         @RequestParam("endTime") String endTime,
                                                                         @RequestParam("pageNo") Integer pageNo,
                                                                         @RequestParam("pageSize") Integer pageSize,
                                                                         @RequestParam("communityId") Long communityId,
                                                                         @RequestParam("mobile") String mobile) {
        Assert.isTrue(StrUtil.isNotBlank(startTime), "开始时间不能为空");
        Assert.isTrue(StrUtil.isNotBlank(endTime), "结束时间不能为空");

        MgrMbePageReqVO pageReqVO = new MgrMbePageReqVO();
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);
        pageReqVO.setBelongCommunityId(communityId);
        LocalDateTime[] createTime = DateUtils.dateStr2LocalDateTimeArray(startTime, endTime);
        pageReqVO.setCreateTime(createTime);

        pageReqVO.setMobile(mobile);

        PageResult<MgrMbeDetailRespVO> pageResult = memberService.statisticsDetail(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MgrMbeDetailRespVO.class));
    }

    /**
     * 3.4、是否合作伙伴，管理人员
     * @return
     */
    @GetMapping("/ispartner")
    @Operation(summary = "是否合作伙伴，管理人员")
    public CommonResult<Boolean> ispartner() {
        Boolean result = memberService.ispartner();
        return success(result);
    }
}
