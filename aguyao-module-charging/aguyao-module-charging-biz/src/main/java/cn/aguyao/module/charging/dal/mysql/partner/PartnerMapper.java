package cn.aguyao.module.charging.dal.mysql.partner;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.partner.vo.PartnerPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.partner.PartnerDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 合作伙伴——小区管理人员（物业） Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PartnerMapper extends BaseMapperX<PartnerDO> {

    default PageResult<PartnerDO> selectPage(PartnerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PartnerDO>()
                .likeIfPresent(PartnerDO::getCode, reqVO.getCode())
                .likeIfPresent(PartnerDO::getMobile, reqVO.getMobile())
                .likeIfPresent(PartnerDO::getName, reqVO.getName())
                .eqIfPresent(PartnerDO::getProfitSharingPoints, reqVO.getProfitSharingPoints())
                .eqIfPresent(PartnerDO::getHistoricalProfitSharing, reqVO.getHistoricalProfitSharing())
                .eqIfPresent(PartnerDO::getCashoutProfitSharing, reqVO.getCashoutProfitSharing())
                .eqIfPresent(PartnerDO::getCommunityNum, reqVO.getCommunityNum())
                .eqIfPresent(PartnerDO::getRemark, reqVO.getRemark())
                .eqIfPresent(PartnerDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(PartnerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PartnerDO::getId));
    }

    default PartnerDO getUserByMobile(String mobile) {
//        return selectOne(new LambdaQueryWrapperX<PartnerDO>()
//                .eq(PartnerDO::getMobile, mobile));
        return selectOne(PartnerDO::getMobile, mobile);
    }

    default int updateUserLogin(Long userId, String clientIP) {
        return update(new LambdaUpdateWrapper<PartnerDO>()
                .eq(PartnerDO::getId, userId)
                .set(PartnerDO::getLoginIp, clientIP));
    }
}
