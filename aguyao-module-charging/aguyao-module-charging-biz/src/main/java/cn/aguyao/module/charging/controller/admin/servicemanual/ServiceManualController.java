package cn.aguyao.module.charging.controller.admin.servicemanual;

import cn.aguyao.framework.apilog.core.annotation.ApiAccessLog;
import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageParam;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.excel.core.util.ExcelUtils;
import cn.aguyao.module.charging.controller.admin.servicemanual.vo.ServiceManualPageReqVO;
import cn.aguyao.module.charging.controller.admin.servicemanual.vo.ServiceManualRespVO;
import cn.aguyao.module.charging.controller.admin.servicemanual.vo.ServiceManualSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.servicemanual.ServiceManualDO;
import cn.aguyao.module.charging.service.servicemanual.ServiceManualService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.aguyao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 使用手册")
@RestController
@RequestMapping("/charging/service-manual")
@Validated
public class ServiceManualController {

    @Resource
    private ServiceManualService serviceManualService;

    @PostMapping("/create")
    @Operation(summary = "创建使用手册")
    @PreAuthorize("@ss.hasPermission('charging:service-manual:create')")
    public CommonResult<Long> createServiceManual(@Valid @RequestBody ServiceManualSaveReqVO createReqVO) {
        return success(serviceManualService.createServiceManual(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新使用手册")
    @PreAuthorize("@ss.hasPermission('charging:service-manual:update')")
    public CommonResult<Boolean> updateServiceManual(@Valid @RequestBody ServiceManualSaveReqVO updateReqVO) {
        serviceManualService.updateServiceManual(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除使用手册")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('charging:service-manual:delete')")
    public CommonResult<Boolean> deleteServiceManual(@RequestParam("id") Long id) {
        serviceManualService.deleteServiceManual(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得使用手册")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('charging:service-manual:query')")
    public CommonResult<ServiceManualRespVO> getServiceManual(@RequestParam("id") Long id) {
        ServiceManualDO serviceManual = serviceManualService.getServiceManual(id);
        return success(BeanUtils.toBean(serviceManual, ServiceManualRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得使用手册分页")
    @PreAuthorize("@ss.hasPermission('charging:service-manual:query')")
    public CommonResult<PageResult<ServiceManualRespVO>> getServiceManualPage(@Valid ServiceManualPageReqVO pageReqVO) {
        PageResult<ServiceManualDO> pageResult = serviceManualService.getServiceManualPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ServiceManualRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出使用手册 Excel")
    @PreAuthorize("@ss.hasPermission('charging:service-manual:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportServiceManualExcel(@Valid ServiceManualPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ServiceManualDO> list = serviceManualService.getServiceManualPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "使用手册.xls", "数据", ServiceManualRespVO.class,
                        BeanUtils.toBean(list, ServiceManualRespVO.class));
    }

}
