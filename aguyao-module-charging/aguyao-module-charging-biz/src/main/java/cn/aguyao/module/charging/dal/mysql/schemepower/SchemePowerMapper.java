package cn.aguyao.module.charging.dal.mysql.schemepower;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.schemepower.vo.SchemePowerPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.schemepower.SchemePowerDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 收费方案—功率档位 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SchemePowerMapper extends BaseMapperX<SchemePowerDO> {

    default PageResult<SchemePowerDO> selectPage(SchemePowerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SchemePowerDO>()
                .eqIfPresent(SchemePowerDO::getSchemeId, reqVO.getSchemeId())
                .eqIfPresent(SchemePowerDO::getSort, reqVO.getSort())
                .eqIfPresent(SchemePowerDO::getStartPower, reqVO.getStartPower())
                .eqIfPresent(SchemePowerDO::getEndPower, reqVO.getEndPower())
                .eqIfPresent(SchemePowerDO::getAmount, reqVO.getAmount())
                .eqIfPresent(SchemePowerDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(SchemePowerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SchemePowerDO::getId));
    }

    default void deleteBySchemeId(Long schemeId) {
        update(new LambdaUpdateWrapper<SchemePowerDO>()
                .eq(SchemePowerDO::getSchemeId, schemeId)
                .set(SchemePowerDO::getDeleted, true));
    }
}
