package cn.aguyao.module.charging.dal.mysql.insurerecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.insurerecord.vo.InsureRecordPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.insurerecord.InsureRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 投保记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InsureRecordMapper extends BaseMapperX<InsureRecordDO> {

    default PageResult<InsureRecordDO> selectPage(InsureRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InsureRecordDO>()
                .eqIfPresent(InsureRecordDO::getMbeId, reqVO.getMbeId())
                .eqIfPresent(InsureRecordDO::getMpId, reqVO.getMpId())
                .eqIfPresent(InsureRecordDO::getInsureConfigId, reqVO.getInsureConfigId())
                .betweenIfPresent(InsureRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InsureRecordDO::getId));
    }

}
