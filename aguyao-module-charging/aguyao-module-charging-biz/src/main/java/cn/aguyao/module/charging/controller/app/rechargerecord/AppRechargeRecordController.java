package cn.aguyao.module.charging.controller.app.rechargerecord;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.rechargerecord.vo.RechargeRecordPageReqVO;
import cn.aguyao.module.charging.controller.app.rechargerecord.vo.AppRechargeRecordRespVO;
import cn.aguyao.module.charging.dal.dataobject.rechargerecord.RechargeRecordDO;
import cn.aguyao.module.charging.service.rechargerecord.RechargeRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 充值记录")
@RestController
@RequestMapping("/charging/recharge-record")
@Validated
public class AppRechargeRecordController {

    @Resource
    private RechargeRecordService rechargeRecordService;



    @GetMapping("/page")
    @Operation(summary = "获得充值记录分页")
//    @PreAuthorize("@ss.hasPermission('charging:recharge-record:query')")
    public CommonResult<PageResult<AppRechargeRecordRespVO>> getRechargeRecordPage(
            @RequestParam("startTime") String startTime,
            @RequestParam("endTime") String endTime,
            @RequestParam("pageNo") Integer pageNo,
            @RequestParam("pageSize") Integer pageSize
    ) {
        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        RechargeRecordPageReqVO pageReqVO = new RechargeRecordPageReqVO();
        pageReqVO.setMpId(mpId);
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);
        LocalDateTime[] createTime = DateUtils.dateStr2LocalDateTimeArray(startTime, endTime);
        pageReqVO.setCreateTime(createTime);

        PageResult<RechargeRecordDO> pageResult = rechargeRecordService.getRechargeRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppRechargeRecordRespVO.class));
    }


}
