package cn.aguyao.module.charging.controller.admin.gitfbalancerecord;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.module.charging.controller.admin.gitfbalancerecord.vo.GiftBalanceRecordSaveReqVO;
import cn.aguyao.module.charging.service.giftbalancerecord.GiftBalanceRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 投保记录")
@RestController
@RequestMapping("/charging/gift-balance")
@Validated
public class GiftBalanceRecordController {

    @Resource
    private GiftBalanceRecordService giftBalanceRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建投保记录")
    @PreAuthorize("@ss.hasPermission('charging:member:giftBalance')")
    public CommonResult<Long> giftBalance(@Valid @RequestBody GiftBalanceRecordSaveReqVO createReqVO) {
        giftBalanceRecordService.giftBalance(createReqVO);
        return success();
    }


}
