package cn.aguyao.module.charging.service.profitsharingrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo.ProfitSharingRecordPageReqVO;
import cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo.ProfitSharingRecordSaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.profitsharingrecord.ProfitSharingRecordDO;
import cn.aguyao.module.charging.dal.mysql.profitsharingrecord.ProfitSharingRecordMapper;
import cn.aguyao.module.charging.enums.PrefixConstants;
import cn.aguyao.module.system.api.serial.SerialApi;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.PROFIT_SHARING_RECORD_NOT_EXISTS;

/**
 * 分润记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProfitSharingRecordServiceImpl implements ProfitSharingRecordService {

    @Resource
    private ProfitSharingRecordMapper profitSharingRecordMapper;

    @Resource
    private SerialApi serialApi;

    @Override
    public Long createProfitSharingRecord(ProfitSharingRecordSaveReqVO createReqVO) {

        String code = serialApi.getCode(PrefixConstants.PREFIX_FR);
        // 插入
        ProfitSharingRecordDO profitSharingRecord = BeanUtils.toBean(createReqVO, ProfitSharingRecordDO.class);
        profitSharingRecord.setCode(code);
        profitSharingRecordMapper.insert(profitSharingRecord);
        // 返回
        return profitSharingRecord.getId();
    }

    @Override
    public void updateProfitSharingRecord(ProfitSharingRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateProfitSharingRecordExists(updateReqVO.getId());
        // 更新
        ProfitSharingRecordDO updateObj = BeanUtils.toBean(updateReqVO, ProfitSharingRecordDO.class);
        profitSharingRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteProfitSharingRecord(Long id) {
        // 校验存在
        validateProfitSharingRecordExists(id);
        // 删除
        profitSharingRecordMapper.deleteById(id);
    }

    private void validateProfitSharingRecordExists(Long id) {
        if (profitSharingRecordMapper.selectById(id) == null) {
            throw exception(PROFIT_SHARING_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public ProfitSharingRecordDO getProfitSharingRecord(Long id) {
        return profitSharingRecordMapper.selectById(id);
    }

    @Override
    public PageResult<ProfitSharingRecordDO> getProfitSharingRecordPage(ProfitSharingRecordPageReqVO pageReqVO) {
        return profitSharingRecordMapper.selectPage(pageReqVO);
    }

}
