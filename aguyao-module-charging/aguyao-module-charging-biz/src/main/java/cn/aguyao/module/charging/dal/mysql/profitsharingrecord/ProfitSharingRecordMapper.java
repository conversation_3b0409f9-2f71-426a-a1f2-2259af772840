package cn.aguyao.module.charging.dal.mysql.profitsharingrecord;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.profitsharingrecord.vo.ProfitSharingRecordPageReqVO;
import cn.aguyao.module.charging.dal.dataobject.profitsharingrecord.ProfitSharingRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 分润记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProfitSharingRecordMapper extends BaseMapperX<ProfitSharingRecordDO> {

    default PageResult<ProfitSharingRecordDO> selectPage(ProfitSharingRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProfitSharingRecordDO>()
                .likeIfPresent(ProfitSharingRecordDO::getCode, reqVO.getCode())
                .likeIfPresent(ProfitSharingRecordDO::getPartnerCode, reqVO.getPartnerCode())
                .likeIfPresent(ProfitSharingRecordDO::getPartnerName, reqVO.getPartnerName())
                .likeIfPresent(ProfitSharingRecordDO::getPartnerMobile, reqVO.getPartnerMobile())
                .eqIfPresent(ProfitSharingRecordDO::getRate, reqVO.getRate())
                .eqIfPresent(ProfitSharingRecordDO::getProfitSharing, reqVO.getProfitSharing())
                .eqIfPresent(ProfitSharingRecordDO::getBelongCommunityId, reqVO.getBelongCommunityId())
                .likeIfPresent(ProfitSharingRecordDO::getMbeMobile, reqVO.getMbeMobile())
                .betweenIfPresent(ProfitSharingRecordDO::getConsumeTime, reqVO.getConsumeTime())
                .eqIfPresent(ProfitSharingRecordDO::getConsumeAmount, reqVO.getConsumeAmount())
                .eqIfPresent(ProfitSharingRecordDO::getConsumeType, reqVO.getConsumeType())
                .eqIfPresent(ProfitSharingRecordDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ProfitSharingRecordDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ProfitSharingRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProfitSharingRecordDO::getId));
    }

}
