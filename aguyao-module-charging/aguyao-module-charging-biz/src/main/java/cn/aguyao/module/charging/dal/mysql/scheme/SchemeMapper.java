package cn.aguyao.module.charging.dal.mysql.scheme;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.scheme.vo.SchemePageReqVO;
import cn.aguyao.module.charging.dal.dataobject.scheme.SchemeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 收费方案 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SchemeMapper extends BaseMapperX<SchemeDO> {

    default PageResult<SchemeDO> selectPage(SchemePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SchemeDO>()
                .likeIfPresent(SchemeDO::getName, reqVO.getName())
                .eqIfPresent(SchemeDO::getDescription, reqVO.getDescription())
                .eqIfPresent(SchemeDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(SchemeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SchemeDO::getId));
    }

    @Select("SELECT " +
            "t1.*  " +
            "FROM " +
            "charging_scheme t1 " +
            "LEFT JOIN charging_building t2 ON t1.id = t2.scheme_id " +
            "LEFT JOIN charging_device t3 ON t2.id = t3.building_id  " +
            "WHERE t1.deleted = 0 and t3.deleted = 0 " +
            "AND t3.device = #{device} ")
    SchemeDO selectByDevice(String device);
}
