package cn.aguyao.module.charging.controller.admin.schemepower.vo;

import cn.aguyao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 收费方案—功率档位分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SchemePowerPageReqVO extends PageParam {

    @Schema(description = "方案id", example = "7111")
    private Long schemeId;

    @Schema(description = "序号")
    private Integer sort;

    @Schema(description = "功率区间，左侧（包含）")
    private Integer startPower;

    @Schema(description = "功率区间，右侧（不含）")
    private Integer endPower;

    @Schema(description = "收费标准（单位：元）")
    private BigDecimal amount;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}