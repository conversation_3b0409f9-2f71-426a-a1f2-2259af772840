package cn.aguyao.module.charging.controller.admin.rechargerecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 充值记录新增/修改 Request VO")
@Data
public class RechargeRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "22924")
    private Long id;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "用户编号")
    private String mbeCode;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "消费金额")
    private BigDecimal amount;

    @Schema(description = "充值时间")
    private LocalDateTime rechargeTime;

    @Schema(description = "支付来源，消费来源")
    private Integer paySource;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "帐号状态（0正常 1停用）不能为空")
    private Integer status;

}
