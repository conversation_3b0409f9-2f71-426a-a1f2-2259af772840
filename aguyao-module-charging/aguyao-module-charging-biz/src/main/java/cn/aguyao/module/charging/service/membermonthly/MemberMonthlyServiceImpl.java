package cn.aguyao.module.charging.service.membermonthly;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.common.util.object.BeanUtils;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.membermonthly.vo.MemberMonthlyPageReqVO;
import cn.aguyao.module.charging.controller.admin.membermonthly.vo.MemberMonthlySaveReqVO;
import cn.aguyao.module.charging.dal.dataobject.membermonthly.MemberMonthlyDO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgconfig.MonthlyPkgConfigDO;
import cn.aguyao.module.charging.dal.dataobject.monthlypkgrecord.MonthlyPkgRecordDO;
import cn.aguyao.module.charging.dal.mysql.membermonthly.MemberMonthlyMapper;
import cn.aguyao.module.charging.service.monthlypkgconfig.MonthlyPkgConfigService;
import cn.hutool.core.date.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

import static cn.aguyao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.MEMBER_MONTHLY_NOT_EXISTS;
import static cn.aguyao.module.charging.enums.ErrorCodeConstants.MONTHLY_PKG_CONFIG_NOT_EXISTS;

/**
 * 用户和小区包月的关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberMonthlyServiceImpl implements MemberMonthlyService {

    @Resource
    private MemberMonthlyMapper memberMonthlyMapper;

    @Resource
    private MonthlyPkgConfigService monthlyPkgConfigService;

    @Override
    public Long createMemberMonthly(MemberMonthlySaveReqVO createReqVO) {
        // 插入
        MemberMonthlyDO memberMonthly = BeanUtils.toBean(createReqVO, MemberMonthlyDO.class);
        memberMonthlyMapper.insert(memberMonthly);
        // 返回
        return memberMonthly.getId();
    }

    @Override
    public void updateMemberMonthly(MemberMonthlySaveReqVO updateReqVO) {
        // 校验存在
        validateMemberMonthlyExists(updateReqVO.getId());
        // 更新
        MemberMonthlyDO updateObj = BeanUtils.toBean(updateReqVO, MemberMonthlyDO.class);
        memberMonthlyMapper.updateById(updateObj);
    }

    @Override
    public void deleteMemberMonthly(Long id) {
        // 校验存在
        validateMemberMonthlyExists(id);
        // 删除
        memberMonthlyMapper.deleteById(id);
    }

    private void validateMemberMonthlyExists(Long id) {
        if (memberMonthlyMapper.selectById(id) == null) {
            throw exception(MEMBER_MONTHLY_NOT_EXISTS);
        }
    }

    @Override
    public MemberMonthlyDO getMemberMonthly(Long id) {
        return memberMonthlyMapper.selectById(id);
    }

    @Override
    public PageResult<MemberMonthlyDO> getMemberMonthlyPage(MemberMonthlyPageReqVO pageReqVO) {
        return memberMonthlyMapper.selectPage(pageReqVO);
    }

    @Override
    public void createOrUpdateMemberMonthly(MonthlyPkgRecordDO entity) {
        MonthlyPkgConfigDO config = monthlyPkgConfigService.getMonthlyPkgConfig(entity.getProId());
        if (Objects.isNull(config)) {
            throw exception(MONTHLY_PKG_CONFIG_NOT_EXISTS);
        }

        Long communityId = entity.getBelongCommunityId();
        if (Objects.isNull(communityId)) {
            communityId = config.getCommunityId();
        }

        // 根据会员和小区查询是否存在包月记录
        MemberMonthlyDO memberMonthlyDO = this.findByMpIdAndCommunityId(entity.getMpId(), communityId);

        // 自动延期就好了
        Integer num = config.getMonthNum();
        if (Objects.isNull(memberMonthlyDO)) {
            // local date time 转 date
            Date date = DateUtil.date();
            // 新的过期时间
            Date newPassDuration = DateUtils.addMonths(date, num);
            LocalDateTime dateTime = DateUtil.toLocalDateTime(newPassDuration);

            MemberMonthlyDO mbeMonthly = new MemberMonthlyDO();
            mbeMonthly.setMpId(entity.getMpId());
            mbeMonthly.setCommunityId(communityId);
            mbeMonthly.setExpiration(dateTime);
            memberMonthlyMapper.insert(mbeMonthly);
        } else {
            // local date time 转 date
            Date date = null;
            if (Objects.isNull(memberMonthlyDO.getExpiration()) || memberMonthlyDO.getExpiration().isBefore(LocalDateTime.now())) {
                date = DateUtil.date();
            } else {
                date = DateUtils.localDateTime2Date(memberMonthlyDO.getExpiration());
            }

            // 新的过期时间
            Date newPassDuration = DateUtils.addMonths(date, num);
            LocalDateTime dateTime = DateUtil.toLocalDateTime(newPassDuration);

            memberMonthlyDO.setExpiration(dateTime);
            memberMonthlyDO.setUpdateTime(LocalDateTime.now());
            memberMonthlyMapper.updateById(memberMonthlyDO);
        }
    }

    @Override
    public MemberMonthlyDO findByMpIdAndCommunityId(Long mpId, Long communityId) {
        return memberMonthlyMapper.selectOne(new LambdaQueryWrapperX<MemberMonthlyDO>()
                .eq(MemberMonthlyDO::getMpId, mpId)
                .eq(MemberMonthlyDO::getCommunityId, communityId));
    }

}
