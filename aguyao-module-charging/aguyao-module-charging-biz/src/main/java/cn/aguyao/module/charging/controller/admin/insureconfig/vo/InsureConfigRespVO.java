package cn.aguyao.module.charging.controller.admin.insureconfig.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 投保配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InsureConfigRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5128")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "优惠编号")
    @ExcelProperty("优惠编号")
    private String code;

    @Schema(description = "投保标题")
    @ExcelProperty("投保标题")
    private String title;

    @Schema(description = "投保内容")
    @ExcelProperty("投保内容")
    private String content;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}