package cn.aguyao.module.charging.dal.mysql.schemetime;

import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.mybatis.core.mapper.BaseMapperX;
import cn.aguyao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.aguyao.module.charging.controller.admin.schemetime.vo.SchemeTimePageReqVO;
import cn.aguyao.module.charging.dal.dataobject.schemetime.SchemeTimeDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 收费方案—时段电费 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SchemeTimeMapper extends BaseMapperX<SchemeTimeDO> {

    default PageResult<SchemeTimeDO> selectPage(SchemeTimePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SchemeTimeDO>()
                .eqIfPresent(SchemeTimeDO::getSchemeId, reqVO.getSchemeId())
                .eqIfPresent(SchemeTimeDO::getSort, reqVO.getSort())
                .betweenIfPresent(SchemeTimeDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(SchemeTimeDO::getEndTime, reqVO.getEndTime())
                .eqIfPresent(SchemeTimeDO::getEprice, reqVO.getEprice())
                .eqIfPresent(SchemeTimeDO::getSprice, reqVO.getSprice())
                .eqIfPresent(SchemeTimeDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(SchemeTimeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SchemeTimeDO::getId));
    }

    default void deleteBySchemeId(Long schemeId) {
        update(new LambdaUpdateWrapper<SchemeTimeDO>()
                .eq(SchemeTimeDO::getSchemeId, schemeId)
                .set(SchemeTimeDO::getDeleted, true));
    }
}
