package cn.aguyao.module.charging.controller.mgr.chargerecord;

import cn.aguyao.framework.common.pojo.CommonResult;
import cn.aguyao.framework.common.pojo.PageResult;
import cn.aguyao.framework.common.util.date.DateUtils;
import cn.aguyao.framework.security.core.util.SecurityFrameworkUtils;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordDetailRespVO;
import cn.aguyao.module.charging.controller.admin.chargerecord.vo.ChargeRecordPageReqVO;
import cn.aguyao.module.charging.controller.mgr.chargerecord.vo.MgrChargeRecordRespVO;
import cn.aguyao.module.charging.service.chargerecord.ChargeRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static cn.aguyao.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 充电记录，消费记录")
@RestController
@RequestMapping("/charging/charge-record")
@Validated
public class MgrChargeRecordController {

    @Resource
    private ChargeRecordService chargeRecordService;

    /**
     * 充电记录分页
     * @param startTime
     * @param endTime
     * @param pageNo
     * @param pageSize
     * @param mobile
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "充电记录分页")
    public CommonResult<PageResult<MgrChargeRecordRespVO>> getChargeRecordPage(
            @RequestParam(name = "startTime", required = false) String startTime,
            @RequestParam(name = "endTime", required = false) String endTime,
            @RequestParam("pageNo") Integer pageNo,
            @RequestParam("pageSize") Integer pageSize,
            @RequestParam(value = "mobile", required = false) String mobile,
            @RequestParam(value = "communityId", required = false) Long communityId
    ) {
        ChargeRecordPageReqVO pageReqVO = new ChargeRecordPageReqVO();
        pageReqVO.setMobile(mobile);
        pageReqVO.setPageNo(pageNo);
        pageReqVO.setPageSize(pageSize);

        LocalDateTime[] timeArray = DateUtils.dateStr2LocalDateTimeArray(startTime, endTime);
        pageReqVO.setCreateTime(timeArray);

        Long mpId = SecurityFrameworkUtils.getLoginUserIdCheck();
        pageReqVO.setMpId(mpId);

        pageReqVO.setChargeCommunityId(communityId);
        PageResult<MgrChargeRecordRespVO> pageResult = chargeRecordService.getChargeRecordPage4Mgr(pageReqVO);
        return success(pageResult);
    }


    /**
     * 充电记录详情
     * @param id 充电记录id
     * @return
     */
    @GetMapping("/detail")
    @Operation(summary = "充电记录详情")
    public CommonResult<ChargeRecordDetailRespVO> detail(@RequestParam("id") Long id) {
        ChargeRecordDetailRespVO respVO = chargeRecordService.detailById(id);
        return success(respVO);
    }

}
