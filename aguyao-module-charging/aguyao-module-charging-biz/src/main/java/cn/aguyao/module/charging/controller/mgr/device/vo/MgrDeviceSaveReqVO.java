package cn.aguyao.module.charging.controller.mgr.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理端 - 设备新增/修改 Request VO")
@Data
public class MgrDeviceSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10825")
    private Long id;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}
