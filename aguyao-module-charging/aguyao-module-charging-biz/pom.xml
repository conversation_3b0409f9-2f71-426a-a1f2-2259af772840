<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.aguyao</groupId>
        <artifactId>aguyao-module-charging</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>aguyao-module-charging-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        member 模块，我们放会员业务。
        例如说：会员中心等等
    </description>

    <dependencies>
        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-module-charging-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>cn.aguyao</groupId>-->
<!--            <artifactId>aguyao-spring-boot-starter-biz-pay</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>cn.aguyao</groupId>
            <artifactId>aguyao-module-pay-api</artifactId>
            <version>${revision}</version>
        </dependency>


        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>

        <!-- 添加 Log4j2 支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>

    </dependencies>

</project>
