package cn.aguyao.module.charging.enums;

/**
 * redis key 常量
 */
public class RedisKeyConstants {

    ////////////////////////////////////////////////////////////////
    /////////////////////////第三方配置//////////////////////////////
    ////////////////////////////////////////////////////////////////
    /**
     * 微信小程序全局的access_token（用户端）
     */
    public static final String GLOBAL_ACCESS_TOKEN_FOR_USER = "mp:applet:access_token:user";

    /**
     * 微信小程序全局的access_token（管理端）
     */
    public static final String GLOBAL_ACCESS_TOKEN_FOR_MGR = "mp:applet:access_token:mgr";


    ////////////////////////////////////////////////////////////////
    /////////////////////////系统自带////////////////////////////////
    ////////////////////////////////////////////////////////////////
    /**
     * 编码前缀：MgrSerialrole
     */
    public static final String MGR_SERIAL_ROLE = "mgr:serial:role:";


    ////////////////////////////////////////////////////////////////
    /////////////////////////基础数据////////////////////////////////
    ////////////////////////////////////////////////////////////////
    /**
     * 编码前缀：油品信息
     */
    public static final String BASIC_OIL_INFO = "basic:oil:info";

    /**
     * 编码前缀：设备信息
     */
    public static final String BASIC_DEVICE_INFO = "basic:device:info";


    /**
     * 编码前缀：设备心跳检测
     */
    public static final String DEVICE_HEARTBEAT_DETECTION = "device:heartbeat:";


    /**
     * 编码前缀：充电最大功率
     */
    public static final String DEVICE_MAX_POWER = "device:maxPower:";

    /**
     * 编码前缀：设备状态
     */
    public static final String DEVICE_STATUS = "device:status:";

    /**
     * 编码前缀：消息内容
     */
    public static final String MSG_INFO = "message:info:";
}
