package cn.aguyao.module.charging.enums;

/**
 * 微信相关URL常量
 */
public interface WeiXinUrlConstants {

    /**
     * 小程序
     */
    public enum Applet {

        JSCODE2SESSION("https://api.weixin.qq.com/sns/jscode2session", "GET", "获取用户openid"),
        CHECKSESSION("https://api.weixin.qq.com/wxa/checksession", "GET", "校验session_key是否有效"),
        RESETUSERSESSIONKEY("https://api.weixin.qq.com/wxa/resetusersessionkey", "GET", "重置用户session_key"),

        TOKEN("https://api.weixin.qq.com/cgi-bin/token", "GET", "获取access_token"),
        GETUSERPHONENUMBER("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=", "POST", "该接口用于将code换取用户手机号"),

        ;

        Applet(String url, String method, String desc) {
            this.url = url;
            this.method = method;
            this.desc = desc;
        }

        private String url;

        private String method;

        private String desc;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getMethod() {
            return method;
        }

        public void setMethod(String method) {
            this.method = method;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }



}
