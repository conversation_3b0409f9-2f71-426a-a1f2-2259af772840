package cn.aguyao.module.charging.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeviceCmdEnum {

    LOGIN(0, "设备登录"),
    HEARTBEAT(1, "设备心跳"),
    REPORT_STATUS(2, "主动上报充电端口状态"),
    OPEN_PORT(3, "打开指定端口输出"),
    CLOSE_PORT(4, "关闭指定端口220V输出"),
    ALL_STATUS(5, "查询全部端口状态"),
    REPORT_DATA(6, "上报刷卡数据"),
    RESTART(10, "重启设备"),
    FUNC_CONFIG(11, "功能参数配置"),
    ;

    private Integer value;

    private String desc;

    public static DeviceCmdEnum getByValue(Integer value) {
        for (DeviceCmdEnum cmd : values()) {
            if (cmd.value.equals(value)) {
                return cmd;
            }
        }
        return null;
    }

    public static String getDescByValue(Integer value) {
        DeviceCmdEnum cmd = getByValue(value);
        return cmd == null ? "未知指令" : cmd.getDesc();
    }

}
