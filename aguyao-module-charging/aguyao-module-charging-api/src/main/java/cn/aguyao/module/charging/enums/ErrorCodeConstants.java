package cn.aguyao.module.charging.enums;

import cn.aguyao.framework.common.exception.ErrorCode;

/**
 * Member 错误码枚举类
 * <p>
 * member 系统，使用 1-004-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 用户相关  1-004-001-000 ============
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_004_001_000, "用户不存在");
    ErrorCode USER_MOBILE_NOT_EXISTS = new ErrorCode(1_004_001_001, "手机号未注册用户");
    ErrorCode USER_MOBILE_USED = new ErrorCode(1_004_001_002, "修改手机失败，该手机号({})已经被使用");
    ErrorCode USER_POINT_NOT_ENOUGH = new ErrorCode(1_004_001_003, "用户积分余额不足");

    // ========== AUTH 模块 1-004-003-000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1_004_003_000, "登录失败，账号密码不正确");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1_004_003_001, "登录失败，账号被禁用");
    ErrorCode AUTH_SOCIAL_USER_NOT_FOUND = new ErrorCode(1_004_003_005, "登录失败，解析不到三方登录信息");
    ErrorCode AUTH_MOBILE_USED = new ErrorCode(1_004_003_007, "手机号已经被使用");

    // ========== 用户收件地址 1-004-004-000 ==========
    ErrorCode ADDRESS_NOT_EXISTS = new ErrorCode(1_004_004_000, "用户收件地址不存在");

    //========== 用户标签 1-004-006-000 ==========
    ErrorCode TAG_NOT_EXISTS = new ErrorCode(1_004_006_000, "用户标签不存在");
    ErrorCode TAG_NAME_EXISTS = new ErrorCode(1_004_006_001, "用户标签已经存在");
    ErrorCode TAG_HAS_USER = new ErrorCode(1_004_006_002, "用户标签下存在用户，无法删除");

    //========== 积分配置 1-004-007-000 ==========

    //========== 积分记录 1-004-008-000 ==========
    ErrorCode POINT_RECORD_BIZ_NOT_SUPPORT = new ErrorCode(1_004_008_000, "用户积分记录业务类型不支持");

    //========== 签到配置 1-004-009-000 ==========
    ErrorCode SIGN_IN_CONFIG_NOT_EXISTS = new ErrorCode(1_004_009_000, "签到天数规则不存在");
    ErrorCode SIGN_IN_CONFIG_EXISTS = new ErrorCode(1_004_009_001, "签到天数规则已存在");

    //========== 签到配置 1-004-010-000 ==========
    ErrorCode SIGN_IN_RECORD_TODAY_EXISTS = new ErrorCode(1_004_010_000, "今日已签到，请勿重复签到");

    //========== 用户等级 1-004-011-000 ==========
    ErrorCode LEVEL_NOT_EXISTS = new ErrorCode(1_004_011_000, "用户等级不存在");
    ErrorCode LEVEL_NAME_EXISTS = new ErrorCode(1_004_011_001, "用户等级名称[{}]已被使用");
    ErrorCode LEVEL_VALUE_EXISTS = new ErrorCode(1_004_011_002, "用户等级值[{}]已被[{}]使用");
    ErrorCode LEVEL_EXPERIENCE_MIN = new ErrorCode(1_004_011_003, "升级经验必须大于上一个等级[{}]设置的升级经验[{}]");
    ErrorCode LEVEL_EXPERIENCE_MAX = new ErrorCode(1_004_011_004, "升级经验必须小于下一个等级[{}]设置的升级经验[{}]");
    ErrorCode LEVEL_HAS_USER = new ErrorCode(1_004_011_005, "用户等级下存在用户，无法删除");

    ErrorCode EXPERIENCE_BIZ_NOT_SUPPORT = new ErrorCode(1_004_011_201, "用户经验业务类型不支持");

    //========== 用户分组 1-004-012-000 ==========
    ErrorCode GROUP_NOT_EXISTS = new ErrorCode(1_004_012_000, "用户分组不存在");
    ErrorCode GROUP_HAS_USER = new ErrorCode(1_004_012_001, "用户分组下存在用户，无法删除");



    //========== 用户管理（会员、合作伙伴） 1-004-013-000 ==========

    ErrorCode PARTNER_NOT_EXISTS = new ErrorCode(1_004_013_001, "合作伙伴不存在");

    ErrorCode MEMBER_NOT_EXISTS = new ErrorCode(1_004_013_002, "会员不存在");

    ErrorCode BUILDING_NOT_EXISTS = new ErrorCode(1_004_013_003, "楼栋不存在");

    ErrorCode CHARGE_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_004, "充电记录不存在");

    ErrorCode COMMUNITY_NOT_EXISTS = new ErrorCode(1_004_013_005, "小区不存在");

    ErrorCode DEVICE_NOT_EXISTS = new ErrorCode(1_004_013_006, "请提供正确的设备编号");

    ErrorCode INSURE_CONFIG_NOT_EXISTS = new ErrorCode(1_004_013_007, "保养配置不存在");

    ErrorCode MONTHLY_PKG_CONFIG_NOT_EXISTS = new ErrorCode(1_004_013_008, "月卡套餐不存在");

    ErrorCode MONTHLY_PKG_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_009, "月卡套餐记录不存在");

    ErrorCode PLATFORM_INFO_NOT_EXISTS = new ErrorCode(1_004_013_010, "平台信息不存在");

    ErrorCode PROFIT_SHARING_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_011, "分润记录不存在");

    ErrorCode RECHARGE_DISCOUNT_CONFIG_NOT_EXISTS = new ErrorCode(1_004_013_012, "充值优惠配置不存在");

    ErrorCode RECHARGE_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_013, "充值记录不存在");

    ErrorCode REFUND_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_014, "退款记录不存在");

    ErrorCode REPAIR_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_015, "报修记录不存在");

    ErrorCode WITHDRAWAL_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_016, "提现记录不存在");


    // ========== 楼号 ==========
    ErrorCode BUILDING_NUM_NOT_EXISTS = new ErrorCode(1_004_013_017, "楼号不存在");


    ErrorCode INSURE_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_018, "投保记录不存在");

    ErrorCode SERVICE_MANUAL_NOT_EXISTS = new ErrorCode(1_004_013_019, "使用手册不存在");

    ErrorCode MP_USER_NOT_EXISTS = new ErrorCode(1_004_013_020, "微信用户不存在");


    ErrorCode PARTNER_BANK_NOT_EXISTS = new ErrorCode(1_004_013_021, "伙伴银行不存在");

    ErrorCode PARTNER_BALANCE_INSUFFICIENT = new ErrorCode(1_004_013_022, "账户余额不足");

    ErrorCode PARTNER_BANK_NOT_BINDING = new ErrorCode(1_004_013_023, "伙伴未绑定银行卡");

    ErrorCode PARTNER_BANK_HAS_BINDED = new ErrorCode(1_004_013_024, "伙伴已绑定银行卡");

    ErrorCode INCOME_RECORD_NOT_EXISTS = new ErrorCode(1_004_013_025, "收入（收益）不存在");

    // ========== 收费方案 ==========
    ErrorCode SCHEME_NOT_EXISTS = new ErrorCode(1_004_013_026, "收费方案不存在");

    ErrorCode SCHEME_COLLECT_NOT_EXISTS = new ErrorCode(1_004_013_027, "收费方案—收费明细不存在");

    ErrorCode SCHEME_POWER_NOT_EXISTS = new ErrorCode(1_004_013_028, "收费方案—电费不存在");

    ErrorCode SCHEME_TIME_NOT_EXISTS = new ErrorCode(1_004_013_029, "收费方案—充电时段不存在");

    ErrorCode BANNER_NOT_EXISTS = new ErrorCode(1_004_013_030, "banner不存在");

    ErrorCode MEMBER_MONTHLY_NOT_EXISTS = new ErrorCode(1_004_013_031, "用户和小区包月的关系不存在");


    ErrorCode SCHEME_POWER_NOT_EMPTY = new ErrorCode(1_004_013_032, "功率档位不能为空");

    ErrorCode SCHEME_SETMEAL_NOT_EMPTY = new ErrorCode(1_004_013_033, "套餐不能为空");

    ErrorCode SCHEME_TIME_NOT_EMPTY = new ErrorCode(1_004_013_034, "时段不能为空");





}
