package cn.aguyao.module.charging.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    WAIT_PAY(0, "待支付"),
    PAID(1, "已支付"),
    PAY_FAIL(2, "支付失败"),
    REFUNDING(3, "退款中"),
    REFUNDED(4, "已退款");

    private int value;
    private String desc;

    public static OrderStatusEnum getByValue(int value) {
        for (OrderStatusEnum status : OrderStatusEnum.values()) {
            if (status.value == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的订单状态值：" + value);
    }

}
