package cn.aguyao.module.charging.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款审批状态枚举
 *
 * <AUTHOR>
 */

public interface ApprovalStatusEnum {

    /**
     * 退款申请状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum RefundStatusEnum {
        APPLYING_0(0, "已提交"),
        SUCCEED_1(1, "已同意"),
        REFUSE_2(2, "已拒绝")
        ;

        /**
         * 值
         */
        private final int value;
        /**
         * 描述
         */
        private final String desc;
    }


    /**
     * 提现申请状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum WithdrawStatusEnum {
        APPLYING_0(0, "已提交"),
        SUCCEED_1(1, "已同意"),
        REFUSE_2(2, "已拒绝"),
        RECEIVED_MONEY_3(3, "已打款")
        ;

        /**
         * 值
         */
        private final int value;
        /**
         * 描述
         */
        private final String desc;
    }

}
