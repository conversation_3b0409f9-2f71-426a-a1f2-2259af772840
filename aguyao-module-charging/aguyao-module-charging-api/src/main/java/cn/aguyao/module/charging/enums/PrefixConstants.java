package cn.aguyao.module.charging.enums;

/**
 * 编码前缀
 */
public interface PrefixConstants {

    // 会员编码
    public static final String PREFIX_HY = "HY";

    // 主管编码
    public static final String PREFIX_ZG = "ZG";

    // 小区编码
    public static final String PREFIX_XQ = "XQ";

    // 楼栋编码
    public static final String PREFIX_LD = "LD";

    // 设备编码 device
    public static final String PREFIX_DE = "DE";

    // 报修编码
    public static final String PREFIX_BX = "BX";

    // 充值编码
    public static final String PREFIX_CZ = "CZ";

    // 退款编码
    public static final String PREFIX_TK = "TK";

    // 包月编码
    public static final String PREFIX_BY = "BY";

    // 分润编码
    public static final String PREFIX_FR = "FR";

    // 充电编码(微信充电)
    public static final String PREFIX_CD = "CD";

    // 充值优惠编码
    public static final String PREFIX_CZYH = "CZYH";

    // 包月配置编码
    public static final String PREFIX_BYPZ = "BYPZ";

    // 平台信息编码
    public static final String PREFIX_PTXX = "PTXX";

    // 投保配置编码
    public static final String PREFIX_TBPZ = "TBPZ";

    // 使用手册编码
    public static final String PREFIX_SYSC = "SYSC";
}
