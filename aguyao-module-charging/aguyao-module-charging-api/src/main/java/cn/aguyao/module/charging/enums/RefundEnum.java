package cn.aguyao.module.charging.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款
 */
public interface RefundEnum {

    /**
     * 退款状态
     */
    @Getter
    @AllArgsConstructor
    public enum RefundStatusEnum {
        REFUND_STATUS_0(0, "待退款"),
        REFUND_STATUS_1(1, "退款成功"),
        REFUND_STATUS_2(2, "退款失败")
        ;

        /**
         * 值
         */
        private final int value;
        /**
         * 描述
         */
        private final String desc;
    }

    /**
     * 退款类型
     */
    @Getter
    @AllArgsConstructor
    public enum RefundTypeEnum {
        REFUND_TYPE_0(0, "余额退款"),
        REFUND_TYPE_1(1, "包月退款"),
        REFUND_TYPE_2(2, "临时退款")
        ;

        /**
         * 值
         */
        private final int value;
        /**
         * 描述
         */
        private final String desc;
    }
}
