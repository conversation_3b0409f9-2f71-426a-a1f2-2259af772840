package cn.aguyao.module.charging.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款申请状态枚举
 *
 * <AUTHOR>
 */

public interface ApplyStatusEnum {

    @Getter
    @AllArgsConstructor
    public enum RefundStatusEnum {
        APPLYING_0(0, "申请中"),
        SUCCEED_1(1, "已退款"),
        FAILURE_2(2, "退款失败")
        ;

        /**
         * 值
         */
        private final int value;
        /**
         * 描述
         */
        private final String desc;
    }



}
