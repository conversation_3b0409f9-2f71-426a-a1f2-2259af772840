package cn.aguyao.module.charging.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 端口状态枚举
 */
@Getter
@AllArgsConstructor
public enum DeviceBtStatEnum {

    BT_STAT_0(0, "待机"),
    BT_STAT_1(1, "端口正在输出"),
    BT_STAT_2(2, "输出时间到"),
    BT_STAT_3(3, "充电完成"),
    BT_STAT_4(4, "插头拨出"),
    BT_STAT_5(5, "功率过大"),
    BT_STAT_6(6, "端口异常"),
    BT_STAT_7(7, "高温报警"),
    ;

    private Integer value;

    private String desc;

    /**
     * 空闲的
     * @return
     */
    public static Integer[] idle() {
        return new Integer[] {BT_STAT_0.getValue(), BT_STAT_4.getValue()};
    }

    /**
     * 使用中的
     * @return Integer[]
     */
    public static Integer[] used () {
        return new Integer[]{BT_STAT_1.getValue(), BT_STAT_2.getValue(), BT_STAT_3.getValue(), BT_STAT_5.getValue()};
    }

    public static Integer[] abnormal() {
        return new Integer[]{BT_STAT_6.getValue(), BT_STAT_7.getValue()};
    }


    public static String getDescByValue(Integer value) {
        for (DeviceBtStatEnum deviceBtStat : DeviceBtStatEnum.values()) {
            if (deviceBtStat.value.equals(value)) {
                return deviceBtStat.desc;
            }
        }
        return null;
    }

    /**
     * 是否空闲
     * @param value
     * @return Boolean
     */
    public static Boolean isIdle (Integer value) {
        return Arrays.stream(idle()).anyMatch(v -> v.equals(value));
    }


    /**
     * 是否使用中
     * @param value
     * @return Boolean
     */
    public static Boolean isUsed (Integer value) {
        return Arrays.stream(used()).anyMatch(v -> v.equals(value));
    }

    /**
     * 是否故障状态
     * @param value
     * @return Boolean
     */
    public static Boolean isAbnormal (Integer value) {
        return Arrays.stream(abnormal()).anyMatch(v -> v.equals(value));
    }
}
