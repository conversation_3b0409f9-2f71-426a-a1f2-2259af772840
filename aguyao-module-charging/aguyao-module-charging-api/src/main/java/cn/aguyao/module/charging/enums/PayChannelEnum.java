package cn.aguyao.module.charging.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付渠道枚举
 */
@Getter
@AllArgsConstructor
public enum PayChannelEnum {

    WX_LITE("wx_lite", 10, "微信小程序"),
    ALI_APP("ali_app", 20, "支付宝APP"),
    ;

    private String key;

    private Integer value;

    private String desc;

    public static PayChannelEnum getByValue(Integer value) {
        for (PayChannelEnum payChannel : values()) {
            if (payChannel.value.equals(value)) {
                return payChannel;
            }
        }
        return null;
//        throw new IllegalArgumentException("支付渠道枚举不存在");
    }

}
